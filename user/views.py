import datetime
import os
import random
import string
import uuid
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from io import BytesIO
from pathlib import Path

import qrcode
import qrcode.image.svg
import requests
from cryptography import x509
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa
from cryptography.x509.oid import NameOID
from django.conf import settings
from django.contrib.auth import authenticate
from django.contrib.auth.models import Permission
from django.core.files.base import ContentFile
from django.core.mail import send_mail
from django.db.models import Case, Count, DecimalField, ExpressionWrapper, F, Q, Sum, Value, When
from django.db.models.functions import Coalesce
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.crypto import get_random_string
from django.utils.translation import gettext_lazy as _
from django_filters.rest_framework import DjangoFilterBackend
from django_otp import devices_for_user
from django_otp.plugins.otp_email.models import EmailDevice
from django_otp.plugins.otp_totp.models import TOTPDevice
from rest_flex_fields import is_expanded
from rest_framework import filters, permissions, status, views, viewsets
from rest_framework.authtoken.models import Token
from rest_framework.decorators import action
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView

from gateway.company.utils import OnboardException, WithdrawalException, EntityInformationError
from gateway.tasks import antecipate
from payment.models import Payment
from payment.serializers import PaymentSerializer
from payment.services.cashout_factory import CashoutStrategyFactory
from user.services.entity_information_factory import EntityInformationFactory
from user.tasks import register_access
from user.events import send_webhooks
from user.paginations import UserPagination
from user.permissions import (
    ApproveAccountsPermission,
    DenyAllPermission,
    FullAccessPermission,
    ManageAnticipationsPermission,
    ManageCompaniesPermission,
    ManageWithdrawalsPermission,
    SellerBlockUnblockViewPermission,
)
from user.utils import remove_special_chars

from .filters import AnalysisFilter, UserAdminFilter
from .models import (
    Analysis,
    Company,
    Document,
    Event,
    IdWallProfile,
    User,
    UserCertificate,
    UserStatusChangeLog,
    Webhook,
    Withdrawal,
    WithdrawalIP,
)
from .serializers import (
    AnalysisSerializer,
    ApproveAccountSerializer,
    CompanySerializer,
    DocumentSerializer,
    EventSerializer,
    GatewayEditSerializer,
    GatewaySerializer,
    KycSerializer,
    ResetPasswordSerializer,
    TargetUserSerializer,
    TokenSerializer,
    TOTPDeviceSerializer,
    UserCertificateSerializer,
    UserCreateByAPISerializer,
    UserCreateSerializer,
    UserSerializer,
    UserStatusChangeLogSerializer,
    WebhookSerializer,
    WithdrawalIPSerializer,
    WithdrawalRequestSerializer,
    WithdrawalSerializer,
)
# ErrorException
from gateway.company.utils import ErrorException
import waffle

from splitpay.logging import get_split_logger

class UserViewSet(viewsets.ModelViewSet):
    pagination_class = UserPagination
    serializer_class = UserSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    filterset_class = UserAdminFilter
    filterset_fields = [
        'id',
        'first_name',
        'last_name',
        'email',
        'status',
        'is_staff',
        'is_superuser',
    ]
    ordering_fields = ['id', 'first_name', 'last_name', 'email', 'status']
    search_fields = [
        'id',
        'first_name',
        'last_name',
        'email',
        'cnpj',
        'cpf',
        'commercialName',
        'companyLegalName',
    ]

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = []

        if self.action in ['approve', 'reject']:
            necessary_permissions = [
                IsAdminUser(),
                FullAccessPermission(),
                ApproveAccountsPermission(),
            ]
        elif self.action in ['list', 'retrieve', 'companies']:
            necessary_permissions = [
                IsAdminUser(),
                FullAccessPermission(),
                ManageCompaniesPermission(),
            ]
        has_permissions = [
            permission
            for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        return  User.objects.filter(
            status__in=['gateway_pending'],
            is_staff=False,
            is_superuser=False,
            cnpj__isnull=False,
            cnpj__gt=''
        ).order_by('-date_joined')


    def get_object(self):
        return User.objects.get(pk=self.kwargs['pk'])

    def calculate_percent(self, value_field, total_field):
        """Helper function to calculate percentage safely"""
        return ExpressionWrapper(
            Case(
                When(
                    **{f'{total_field}__gt': 0},
                    then=(F(value_field) * Value(Decimal('100.0'))) / F(total_field),
                ),
                default=Value(Decimal('0.0')),
            ),
            output_field=DecimalField(max_digits=10, decimal_places=2),
        )

    def companies(self, request):
        status_filter = request.query_params.get('status')
        if status_filter:
            companies = User.objects.filter(is_staff=False, is_superuser=False)
        else:
            companies = User.objects.filter(
                is_staff=False, is_superuser=False, status__in=['approved', 'blocked']
            )

        total_user_count = User.objects.count()

        total_payments = Coalesce(
            Sum(
                Case(
                    When(
                        payments__status__in=[
                            'paid',
                            'processing',
                            'chargedback',
                            'refused',
                            'prechargeback',
                            'declined',
                            'partially_paid',
                            'refunded',
                        ],
                        then=F('payments__amount'),
                    )
                )
            ),
            Value(Decimal('0.0')),
            output_field=DecimalField(),
        )

        chargeback_payments = Coalesce(
            Sum(Case(When(payments__status='chargedback', then=F('payments__amount')))),
            Value(Decimal('0.0')),
            output_field=DecimalField(),
        )

        refunded_payments = Coalesce(
            Sum(Case(When(payments__status='refunded', then=F('payments__amount')))),
            Value(Decimal('0.0')),
            output_field=DecimalField(),
        )

        companies = companies.annotate(
            # total_value=total_payments,
            chargeback_value=chargeback_payments,
            # chargeback_percent=self.calculate_percent('chargeback_value', 'total_value'),
            refunded_value=refunded_payments,
            # refunded_percent=self.calculate_percent('refunded_value', 'total_value'),
            # average_ticket=ExpressionWrapper(
            #     Coalesce(Sum('payments__amount') / Count('payments__id', filter=Q(payments__status='paid')), Value(Decimal('0.0'))),
            #     output_field=DecimalField()
            # ),
            refusal_value=Coalesce(
                Sum(Case(When(payments__status='refused', then=F('payments__amount')))),
                Value(Decimal('0.0')),
                output_field=DecimalField(),
            ),
            refusal_count=Count('payments__id', filter=Q(payments__status='refused')),
            # refusal_percent=self.calculate_percent('refusal_value', 'total_value'),
            pix_value=Coalesce(
                Sum(Case(When(payments__paymentMethod='pix', then=F('payments__amount')))),
                Value(Decimal('0.0')),
                output_field=DecimalField(),
            ),
            # pix_percent=self.calculate_percent('pix_value', 'total_value'),
            boleto_value=Coalesce(
                Sum(Case(When(payments__paymentMethod='boleto', then=F('payments__amount')))),
                Value(Decimal('0.0')),
                output_field=DecimalField(),
            ),
            # boleto_percent=self.calculate_percent('boleto_value', 'total_value'),
            card_value=Coalesce(
                Sum(Case(When(payments__paymentMethod='credit_card', then=F('payments__amount')))),
                Value(Decimal('0.0')),
                output_field=DecimalField(),
            ),
            # credit_card_percent=self.calculate_percent('card_value', 'total_value'),
        )

        # Filter with backend
        companies = self.filter_queryset(companies)
        ordering = self.request.query_params.get('ordering')
        if ordering:
            if ordering == 'totalSales':
                # Fetch all users and then sort in Python
                companies = sorted(companies, key=lambda x: x.totalSales)
            elif ordering == '-totalSales':
                companies = sorted(companies, key=lambda x: x.totalSales, reverse=True)
            elif ordering == 'liquidSales':
                companies = sorted(companies, key=lambda x: x.liquidSales)
            elif ordering == '-liquidSales':
                companies = sorted(companies, key=lambda x: x.liquidSales, reverse=True)
            elif ordering == 'withdrawAmount':
                companies = sorted(companies, key=lambda x: x.withdrawAmount)
            elif ordering == '-withdrawAmount':
                companies = sorted(companies, key=lambda x: x.withdrawAmount, reverse=True)
        serializer = self.get_serializer(companies, many=True)
        # paginate
        page = self.paginate_queryset(serializer.data)

        if page is not None:
            response = self.get_paginated_response(page)
        else:
            response = Response(serializer.data)

        response.data['total_user_count'] = total_user_count
        return response

    @action(detail=True, methods=['post'])
    # TODO: adicionar criacao de usuário após criação de subconta
    def approve(self, request, pk=None):
        user = self.get_object()

        if not user.passwordBaas:
            user.passwordBaas = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
            user.save()

        if user.status == 'approved':
            return Response({'status': user.status})

        if user.status == 'rejected':
            return Response(
                {'detail': 'Não é possível aprovar um usuário rejeitado'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        user.pin = None
        user.status = 'approved'
        user.approvedDate = timezone.now()
        user.save()
        send_webhooks(user, 'account.changed', ApproveAccountSerializer(user).data)
        return Response({'status': user.status})

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        user = self.get_object()
        user.status = 'rejected'
        user.save()
        send_webhooks(user, 'account.changed', ApproveAccountSerializer(user).data)
        return Response({'status': user.status})

    def retrieve(self, request, pk=None):
        try:
            user = User.objects.get(pk=pk)
        except User.DoesNotExist:
            return Response({'error': 'Empresa não encontrada'}, status=status.HTTP_404_NOT_FOUND)

        queryset = (
            User.objects.filter(pk=user.pk)
            .annotate(
                total_value=Sum(
                    Case(
                        When(
                            payments__status__in=[
                                'paid',
                                'processing',
                                'chargedback',
                                'refused',
                                'prechargeback',
                                'declined',
                                'partially_paid',
                                'refunded',
                            ],
                            then=F('payments__amount'),
                        ),
                        default=Value(0),
                        output_field=DecimalField(),
                    )
                ),
                chargeback_value=Sum(
                    Case(
                        When(payments__status='chargedback', then=F('payments__amount')),
                        default=Value(0),
                        output_field=DecimalField(),
                    )
                ),
                chargeback_percent=ExpressionWrapper(
                    F('chargeback_value') * 100.0 / F('total_value'), output_field=DecimalField()
                ),
                refunded_value=Sum(
                    Case(
                        When(payments__status='refunded', then=F('payments__amount')),
                        default=Value(0),
                        output_field=DecimalField(),
                    )
                ),
                refunded_percent=ExpressionWrapper(
                    F('refunded_value') * 100.0 / F('total_value'), output_field=DecimalField()
                ),
                average_ticket=ExpressionWrapper(
                    Sum('payments__amount')
                    / Count('payments__id', filter=Q(payments__status='paid')),
                    output_field=DecimalField(),
                ),
                refusal_value=Sum(
                    Case(
                        When(payments__status='refused', then=F('payments__amount')),
                        default=Value(0),
                        output_field=DecimalField(),
                    )
                ),
                refusal_count=Count('payments__id', filter=Q(payments__status='refused')),
                refusal_percent=ExpressionWrapper(
                    F('refusal_value') * 100.0 / F('total_value'), output_field=DecimalField()
                ),
                pix_value=Sum(
                    Case(
                        When(payments__paymentMethod='pix', then=F('payments__amount')),
                        default=Value(0),
                        output_field=DecimalField(),
                    )
                ),
                pix_percent=ExpressionWrapper(
                    F('pix_value') * 100.0 / F('total_value'), output_field=DecimalField()
                ),
                boleto_value=Sum(
                    Case(
                        When(payments__paymentMethod='boleto', then=F('payments__amount')),
                        default=Value(0),
                        output_field=DecimalField(),
                    )
                ),
                boleto_percent=ExpressionWrapper(
                    F('boleto_value') * 100.0 / F('total_value'), output_field=DecimalField()
                ),
                card_value=Sum(
                    Case(
                        When(payments__paymentMethod='credit_card', then=F('payments__amount')),
                        default=Value(0),
                        output_field=DecimalField(),
                    )
                ),
                credit_card_percent=ExpressionWrapper(
                    F('card_value') * 100.0 / F('total_value'), output_field=DecimalField()
                ),
            )
            .first()
        )

        serializer = self.get_serializer(queryset)
        return Response(serializer.data)


class GatewayViewSet(viewsets.ModelViewSet):
    serializer_class = GatewaySerializer

    def get_object(self):
        return User.objects.get(pk=self.kwargs['pk'])

    def partial_update(self, request, pk=None):
        user = self.get_object()
        serializer = GatewayEditSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            ser = GatewaySerializer(user)
            send_webhooks(user, 'account.changed', ApproveAccountSerializer(user).data)
            return Response(ser.data)
        return Response({'status': 'error'})


def verify_certificate_signature(cert, ca_cert):
    try:
        # Verificar se o certificado foi assinado pela CA
        ca_public_key = ca_cert.public_key()
        ca_public_key.verify(
            cert.signature,
            cert.tbs_certificate_bytes,
            padding.PKCS1v15(),
            cert.signature_hash_algorithm,
        )
        return True
    except Exception as e:
        return False


def validate_certificate(cert, public_key):
    # Load the certificate and public key
    certificate = x509.load_pem_x509_certificate(cert.read(), default_backend())
    public_key = serialization.load_pem_public_key(public_key.read(), backend=default_backend())

    # Verify date of validity of the certificate
    now = datetime.datetime.utcnow()
    if not (certificate.not_valid_before <= now <= certificate.not_valid_after):
        return False

    # Verify signature
    signature = certificate.signature
    data_to_verify = certificate.tbs_certificate_bytes
    try:
        public_key.verify(signature, data_to_verify, padding.PKCS1v15(), hashes.SHA256())
        return True
    except Exception as e:
        return False


def verify_certificate_in_request(request):
    cert = request.FILES.get('certificate')
    return cert


def get_remote_ip(request):
    if request.META.get('HTTP_X_FORWARDED_FOR'):
        return request.META.get('HTTP_X_FORWARDED_FOR', '').split(',')[0].strip()
    else:
        return request.META.get('REMOTE_ADDR')


# List withdrawals and approve/reject them
class WithdrawalViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = WithdrawalSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    ordering_fields = ['id', 'status', 'amountReceived', 'fee', 'createdAt']
    search_fields = ['id', 'user__email', 'user__cnpj', 'user__cpf', 'amountReceived', 'amount', 'createdAt']

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = []

        if self.action == 'create':
            if self.request.data.get('type') == 'antecipation':
                necessary_permissions = [
                    FullAccessPermission(),
                    ManageAnticipationsPermission(),
                ]
            else:
                necessary_permissions = [
                    FullAccessPermission(),
                    ManageWithdrawalsPermission(),
                ]
        elif self.action in ['list', 'retrieve']:
            necessary_permissions = [
                FullAccessPermission(),
                ManageWithdrawalsPermission(),
                # To-do: Add permission to view only withdrawals and only anticipations based on filter
            ]
        elif self.action == 'approve':
            necessary_permissions = [
                FullAccessPermission(),
                ManageWithdrawalsPermission(),
            ]
        elif self.action == 'reject':
            necessary_permissions = [
                FullAccessPermission(),
                ManageWithdrawalsPermission(),
            ]

        has_permissions = [
            permission
            for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def create(self, request):
        if request.user.status == 'blocked':
            return Response({'detail': 'Conta bloqueada!'})

        serializer = WithdrawalRequestSerializer(data=request.data, context={'user': request.user})
        if serializer.is_valid():
            req_validated_data = serializer.validated_data

            withdrawal_model = self.to_withdrawal_model(req_validated_data)

            try:
                available_balance = withdrawal_model.user.getAnticipationBalance()
            except Exception as e:
                return Response(
                    {'message': 'Erro ao calcular saldo disponível.'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            # certificate = verify_certificate_in_request(request)

            # if certificate:
            #     public_key = UserCertificate.objects.get(user=request.user).public_key
            #     if not validate_certificate(certificate, public_key):
            #         return Response({'message': 'Certificado inválido.'}, status=status.HTTP_400_BAD_REQUEST)

            # # Check if the user has a certificate or the IP is whitelisted
            # ip = get_remote_ip(request)
            # if not certificate and not WithdrawalIP.objects.filter(user=request.user,ip_address=ip).exists():
            #     return Response({'message': f'Acesso negado, solicite um certificado ou registre o IP {ip}.'}, status=status.HTTP_400_BAD_REQUEST)

            # Check if the user has enough balance
            if available_balance < float(withdrawal_model.amount):
                return Response(
                    {'message': 'Saldo insuficiente.'}, status=status.HTTP_400_BAD_REQUEST
                )

            withdrawal = serializer.save(user=request.user, type='antecipation')

            response_serializer = WithdrawalSerializer(withdrawal)
            return Response(
                {'status': 'success', 'data': response_serializer.data},
                status=status.HTTP_201_CREATED,
            )
        return Response(
            {
                'status': 'error',
                'detail': serializer.errors.get('non_field_errors')[0]
                if serializer.errors.get('non_field_errors')
                else 'Erro ao criar saque.',
                'message': serializer.errors,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    def get_queryset(self):
        if self.request.query_params.get('status'):
            return Withdrawal.objects.filter(
                status__in=self.request.query_params.get('status').split(',')
            ).order_by('-createdAt')
        if self.request.query_params.get('type'):
            return Withdrawal.objects.filter(
                type__in=self.request.query_params.get('type').split(',')
            ).order_by('-createdAt')
        if self.request.query_params.get('id'):
            return Withdrawal.objects.filter(
                id__in=self.request.query_params.get('id').split(',')
            ).order_by('-createdAt')
        if self.request.query_params.get('status'):
            return Withdrawal.objects.filter(
                status__in=self.request.query_params.get('status').split(',')
            ).order_by('-createdAt')
        if self.request.query_params.get('createdAt'):
            startDate = datetime.datetime.strptime(
                self.request.query_params.get('createdAt').split(',')[0], '%Y-%m-%d'
            )
            endDate = datetime.datetime.strptime(
                self.request.query_params.get('createdAt').split(',')[1], '%Y-%m-%d'
            )
            return Withdrawal.objects.filter(
                createdAt__date__gte=startDate, createdAt__date__lte=endDate
            ).order_by('-createdAt')
        if self.request.query_params.get('search'):
            return Withdrawal.objects.filter(
                user__first_name__icontains=self.request.query_params.get('search'),
                user__last_name__icontains=self.request.query_params.get('search'),
                amount__icontains=self.request.query_params.get('search'),
                amountReceived__icontains=self.request.query_params.get('search'),
                fee__icontains=self.request.query_params.get('search'),
                status__icontains=self.request.query_params.get('search'),
            ).order_by('-createdAt')
        return Withdrawal.objects.all().order_by('-createdAt')

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        withdrawal = self.get_object()
        if withdrawal.status != 'pending':
            return Response({'message': 'Esse pedido já foi processado.'}, status=400)

        if withdrawal.type == 'default':
            if not withdrawal.user.pixKey:
                return Response({'message': 'Chave PIX do vendedor não foi informada.'}, status=400)

            if not request.data.get('manual'):
                if not request.data.get('pin'):
                    return Response({'message': 'PIN não informado.'}, status=400)

                try:
                    cashout_strategy = CashoutStrategyFactory.get_strategy()
                    withdrawal = cashout_strategy.executeCashout(withdrawal)
                except WithdrawalException as e:
                    if e.code in ['documento_bloqueado', 'chave_nao_pertence_ao_documento']:
                        withdrawal.reason = e.message
                        withdrawal.reject()
                    withdrawal.internalReason = e.message
                    return Response({'message': str(e.message)}, status=400)
        elif withdrawal.type == 'antecipation':
            status = antecipate(withdrawal)

        withdrawal.status = 'approved'
        withdrawal.save()
        return Response({'status': 'approved'})

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        withdrawal = self.get_object()
        if withdrawal.status != 'pending':
            return Response(
                {'message': 'Esse pedido já foi processado.'}, status=status.HTTP_400_BAD_REQUEST
            )

        withdrawal.reject()

        return Response({'status': 'rejected'})

    def to_withdrawal_model(self, data):
        return Withdrawal(
            amount=data.get('amount'),
            user=self.request.user,
            type=data.get('type'),
            status='pending',
        )


class DocumentAPIView(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    model = Document
    serializer_class = DocumentSerializer

    def get_queryset(self):
        return Document.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


def generate_password():
    return ''.join(random.choice(string.ascii_uppercase + string.digits) for _ in range(10))


class MainCompanyAPIView(viewsets.ModelViewSet):
    model = Company
    serializer_class = CompanySerializer

    def get_object(self):
        user = User.objects.filter(is_superuser=True).first()
        return Company.objects.get_or_create(user=user)[0]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def create(self, request, *args, **kwargs):
        if Company.objects.filter(user=request.user, externalId__isnull=False).exists():
            return Response(
                {'message': 'Você já possui uma empresa cadastrada.'},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            if not request.user.passwordBaas:
                request.user.passwordBaas = generate_password()
                request.user.save()
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            if not Company.objects.filter(user=request.user).exists():
                self.perform_create(serializer)
                instance = serializer.instance
            else:
                Company.objects.filter(user=request.user).update(**serializer.validated_data)
                instance = Company.objects.get(user=request.user)

            headers = self.get_success_headers(serializer.data)

            request.user.cnpj = instance.cnpj
            request.user.save()
        return Response(
            {'message': 'Empresa criada com sucesso.'},
            status=status.HTTP_201_CREATED,
            headers=headers,
        )


class SellerUserViewset(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    model = User
    serializer_class = UserSerializer

    def get_queryset(self):
        return User.objects.filter(is_staff=False, is_superuser=False)

    def get_object(self):
        return self.request.user

    def partial_update(self, request, *args, **kwargs):
        res = super().partial_update(request, *args, **kwargs)
        instance = self.get_object()
        try:
            cpf_already_exists = _cpf_already_exists_for_another_user(instance)
            if cpf_already_exists:
                raise ErrorException(
                    message='Já existe uma conta com esse CPF',
                    code=400,
                    type='onboard'
                )

            instance.set_status() # type: ignore
            return res
        except (OnboardException) as e:
            return Response({'detail': str(e.message)}, status=status.HTTP_400_BAD_REQUEST)

def _cpf_already_exists_for_another_user(instance):
    cpf_already_exists = User.objects.filter(
        cpf=instance.cpf
    ).exclude(
        id=instance.id # type: ignore
    ).exists()

    if not cpf_already_exists:
        cpf_already_exists = IdWallProfile.objects.filter(
            cpf=instance.cpf,
            status='approved'
        ).exclude(
            user=instance
        ).exists()

    return cpf_already_exists

def _validate_cpf_info(cpf_info, user):
    if cpf_info.menor_de_idade:
        raise ErrorException(
            message='Você precisa ter 18 anos ou mais para completar seu cadastro',
            code=400,
            type='onboard'
        )

    if cpf_info.obito_identificado:
        raise ErrorException(
            message='O CPF informado está inativo',
            code=400,
            type='onboard'
        )

    if not cpf_info.situacao_regular:
        raise ErrorException(
            message=(
                'O CPF informado está em situação irregular junto a receita federal'
            ),
            code=400,
            type='onboard'
        )

    if remove_special_chars(user.first_name).lower() != cpf_info.primeiro_nome.lower():
        raise ErrorException(
            message=(
                'Você deve digitar o nome completo como consta no documento do CPF informado.'
            ),
            code=400,
            type='onboard'
        )

    if user.birthDate != cpf_info.data_nascimento:
        raise ErrorException(
            message=(
                'Você deve digitar a data de nascimento como consta no documento do CPF informado.'
            ),
            code=400,
            type='onboard'
        )



class SellerUserViewsetAPI(viewsets.ModelViewSet):
    model = User
    serializer_class = UserSerializer
    logger = get_split_logger('SellerUserViewsetAPI')

    def get_queryset(self):
        return User.objects.filter(is_staff=False, is_superuser=False, owner=self.request.user)

    def get_object(self):
        return User.objects.get(id=self.kwargs.get('pk'))

    def create(self, request, *args, **kwargs):
        serializer = UserCreateByAPISerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save(owner=self.request.user)
            return Response(
                self.get_serializer(serializer.instance).data, status=status.HTTP_201_CREATED
            )

    def partial_update(self, request, *args, **kwargs):
        super().partial_update(request, *args, **kwargs)

        instance = self.get_object()
        try:
            if instance.status != 'approved':
                cpf_already_exists = _cpf_already_exists_for_another_user(instance)
                if cpf_already_exists:
                    raise ErrorException(
                        message='Já existe uma conta com esse CPF',
                        code=400,
                        type='onboard'
                    )

            if instance.status  == 'pending' and instance.cpf:
                strategy = EntityInformationFactory.get_active_strategy_or_dummy()
                cpf_info = strategy.get_info_from_cpf(instance.cpf)
                if cpf_info is not None:
                    _validate_cpf_info(cpf_info, instance)

                    instance.first_name = (
                        cpf_info.primeiro_nome if cpf_info.primeiro_nome else instance.first_name
                    )
                    instance.last_name = (
                        cpf_info.ultimo_nome if cpf_info.ultimo_nome else instance.last_name
                    )
                    instance.save(update_fields=['first_name', 'last_name'])
                    instance.set_status()

            if instance.status != 'approved':
                instance.set_status()


        except (OnboardException, EntityInformationError) as e:
            self.logger.exception(
                f'{e.message} (user_id={instance.id}) ', # type: ignore
                exc_info=True
            )
            return Response({'detail': str(e.message)}, status=status.HTTP_400_BAD_REQUEST)

        send_webhooks(instance, 'account.approved', KycSerializer(instance).data)

        return Response(
            self.get_serializer(instance).data,
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['post'])
    def uploadDocument(self, request, pk=None):
        user = self.get_object()
        serializer = DocumentSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save(user=user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def uploadDocuments(self, request, pk=None):
        user = self.get_object()
        documents = []
        for file in request.FILES.getlist('files'):
            serializer = DocumentSerializer(data={'file': file})
            if serializer.is_valid(raise_exception=True):
                serializer.save(user=user)
                documents.append(serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        return Response(documents, status=status.HTTP_201_CREATED)


class SellerBlockUnblockView(viewsets.ViewSet):
    permission_classes = [SellerBlockUnblockViewPermission]

    @staticmethod
    def get_reason(request) -> str | Response:
        reason = request.POST.get('reason')
        if not reason:
            return Response(
                status=status.HTTP_400_BAD_REQUEST, data={'detail': 'Reason is required'}
            )
        return reason

    @staticmethod
    def get_user_or_404(pk) -> User | None:
        return get_object_or_404(User, pk=pk)

    @action(detail=True, methods=['post'])
    def block(self, request, pk=None):
        user = self.get_user_or_404(pk)

        if user.is_blocked:
            return Response(
                status=status.HTTP_400_BAD_REQUEST, data={'detail': 'User already blocked'}
            )

        reason = self.get_reason(request)

        if not isinstance(reason, str):
            return reason

        user.block(reason=reason, blocked_by=request.user)

        return Response(status=status.HTTP_201_CREATED, data={'detail': 'User blocked'})

    @action(detail=True, methods=['post'])
    def unblock(self, request, pk=None):
        user = self.get_user_or_404(pk=pk)

        if not user.is_blocked:
            return Response(status=status.HTTP_400_BAD_REQUEST, data={'detail': 'User not blocked'})

        reason = self.get_reason(request)

        if not isinstance(reason, str):
            return reason

        user.unblock(reason=reason, unblocked_by=request.user)

        return Response(status=status.HTTP_201_CREATED, data={'detail': 'User unblocked'})

    @action(detail=True, methods=['get'])
    def get_user_history(self, request, pk=None):
        user = self.get_user_or_404(pk=pk)
        expand_fields = ['user', 'changed_by']
        logs = UserStatusChangeLog.objects.filter(user=user).select_related('user', 'changed_by')
        if not is_expanded(request, 'user'):
            expand_fields.remove('user')
        if not is_expanded(request, 'changed_by'):
            expand_fields.remove('changed_by')
        serializer = UserStatusChangeLogSerializer(logs, many=True, expand=expand_fields)
        return Response(serializer.data)

class SellerResetVerificationAPI(viewsets.ModelViewSet):
    model = User
    permission_classes = [IsAdminUser]

    @staticmethod
    def get_user_or_404(pk) -> User | None:
        return get_object_or_404(User, pk=pk)


    @action(detail=True, methods=['post'])
    def reset_verification(self, request, pk):
        user : User = self.get_user_or_404(pk=pk) # type: ignore

        if user.status == 'blocked':
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={
                    'detail': 'Reset verification is not allowed for blocked accounts'
                }
            )

        user.reset_idwall_profile()
        user.reset_verification()
        user.save()

        return Response(
            status=status.HTTP_200_OK,
            data={'detail': 'User verification reset'}
        )

    @action(detail=True, methods=['post'])
    def reset_related_idwall_profiles(self, request, pk):
        user : User = self.get_user_or_404(pk=pk) # type: ignore
        idwall_profiles = IdWallProfile.objects.filter(cpf=user.cpf)
        reset_emails = []
        for idwall_profile in idwall_profiles:
            idwall_profile.reset_status()

            related_user = idwall_profile.user
            related_user.status = 'pending'
            if user != idwall_profile.user:
                related_user.reset_verification()
                related_user.cpf = None
                related_user.cnpj = None

                reset_emails.append(related_user.email)

            related_user.save()

        return Response(
            {
                'detail': 'Idwall profiles reseted',
                'emails': reset_emails
            }
            , status=status.HTTP_200_OK)

class WebhookAPIView(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    model = Webhook
    serializer_class = WebhookSerializer

    # def get_permissions(self):
    #     permissions = super().get_permissions()

    #     necessary_permissions = []

    #     if self.action == 'list':
    #         necessary_permissions = [IsAdminUser(), FullAccessPermission(), ViewWebhookPermission()]
    #     elif self.action == 'create':
    #         necessary_permissions = [IsAdminUser(), FullAccessPermission(), AddWebhookPermission()]
    #     elif self.action == 'partial_update':
    #         necessary_permissions = [
    #             IsAdminUser(),
    #             FullAccessPermission(),
    #             ChangeWebhookPermission(),
    #         ]
    #     elif self.action == 'destroy':
    #         necessary_permissions = [
    #             IsAdminUser(),
    #             FullAccessPermission(),
    #             DeleteWebhookPermission(),
    #         ]

    #     has_permissions = [
    #         permission
    #         for permission in necessary_permissions
    #         if permission.has_permission(self.request, self)
    #     ]

    #     if has_permissions:
    #         permissions.extend(has_permissions)
    #     else:
    #         permissions = [DenyAllPermission()]

    #     return permissions

    def get_queryset(self):
        return Webhook.objects.filter(user=self.request.user)

    def create(self, request, *args, **kwargs):
        request.data['user'] = request.user.pk
        return super().create(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(status=status.HTTP_200_OK)


class APIKeyAPIView(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    model = Token
    serializer_class = TokenSerializer

    def get_object(self):
        # get or create api key from rest framework token
        token, created = Token.objects.get_or_create(user=self.request.user)
        return token

    def retrieve(self, request, *args, **kwargs):
        return Response({'key': self.get_object().key})

    def create(self, request, *args, **kwargs):
        Token.objects.filter(user=self.request.user).delete()
        return Response({'key': self.get_object().key})


class AccountUpdateWebhook(viewsets.ViewSet):
    permission_classes = [permissions.AllowAny]

    def create(self, request):
        print(request.data)
        data = request.data.get('data')
        if request.data.get('type') == 'pix.received':
            payment = Payment.objects.get(externalId=data.get('id_tx'))

            payment.status = 'paid' if data.get('status') == 'executed' else 'pending'
            payment.paidAt = timezone.now()
            payment.save()

            serializer = PaymentSerializer(payment)
            res = requests.post(payment.postbackUrl, data=serializer.data)
        elif 'account' in request.data.get('type'):
            company = Company.objects.filter(externalId=data.get('id')).last()
            if company.status == 'active':
                company.status = data.get('status')
                company.sub_status = data.get('sub_status')
                company.save()

        return Response(status=status.HTTP_200_OK)


class RegisterViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.AllowAny]

    model = User
    serializer_class = UserCreateSerializer

    def create(self, request, *args, **kwargs):
        if not waffle.switch_is_active("disable_user_registration"):
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                user = serializer.save()
                user.set_password(serializer.validated_data.get('password'))

                # if user has a referrer, set it as affiliate
                if serializer.validated_data.get('referrerId'):
                    user.affiliate = serializer.validated_data.get('referrerId')

                user.save()

                refresh = RefreshToken.for_user(user)
                return Response(
                    {
                        'message': 'Usuário criado com sucesso.',
                        'refresh': str(refresh),
                        'access': str(refresh.access_token),
                    },
                    status=status.HTTP_201_CREATED,
                )
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            {'message': 'Registro de usuários desativado.'}, status=status.HTTP_400_BAD_REQUEST
        )

class CompanyDetailsViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def retrieve(self, request, pk=None):
        user = get_object_or_404(User, id=pk)
        serializer = TargetUserSerializer(user)
        return Response(serializer.data)


class SendResetCodeView(views.APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        if not settings.STANDALONE_GATEWAY:
            return Response({'error': 'Disabled'}, status=status.HTTP_400_BAD_REQUEST)

        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email é obrigatório'}, status=status.HTTP_400_BAD_REQUEST)

        user = User.objects.filter(email=email).first()
        if not user:
            return Response({'error': 'Usuário não encontrado'}, status=status.HTTP_404_NOT_FOUND)

        reset_code = get_random_string(length=6, allowed_chars='1234567890')
        user.reset_code = reset_code
        user.reset_code_generated_at = timezone.now()
        user.save()

        send_mail(
            'Seu código de redefinição de senha',
            f'Seu código de redefinição de senha é {reset_code}',
            settings.DEFAULT_FROM_MAIL,  # TODO: Change this to a valid email
            [email],
            fail_silently=False,
        )
        return Response(
            {'message': 'Código de redefinição enviado para o email'}, status=status.HTTP_200_OK
        )


class ResetPasswordView(views.APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = ResetPasswordSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            reset_code = serializer.validated_data['reset_code']
            new_password = serializer.validated_data['new_password']

            user = User.objects.filter(email=email, reset_code=reset_code).first()
            if not user:
                return Response(
                    {'error': 'Código de redefinição inválido ou usuário não encontrado'},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Check if the reset code is expired (24 hours)
            expiration_time = user.reset_code_generated_at + timedelta(hours=24)
            if timezone.now() > expiration_time:
                return Response(
                    {'error': 'Código de redefinição expirou'}, status=status.HTTP_400_BAD_REQUEST
                )

            user.set_password(new_password)
            user.reset_code = ''
            user.reset_code_generated_at = None
            user.save()
            return Response({'message': 'Senha redefinida com sucesso'}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class Enable2FA(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user

        if user.isTwoFactorEnabled:
            return Response(
                {'error': '2FA is already enabled.'}, status=status.HTTP_400_BAD_REQUEST
            )

        device = EmailDevice.objects.filter(user=user).first()
        if device and device.confirmed:
            return Response(
                {'error': '2FA is already enabled.'}, status=status.HTTP_400_BAD_REQUEST
            )

        device = EmailDevice.objects.filter(user=user, confirmed=False).first()
        if not device:
            device = EmailDevice.objects.create(user=user, name='default', confirmed=False)

        try:
            device.generate_challenge()
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(
            {'message': 'Token sent to your email. Please confirm to activate 2FA.'},
            status=status.HTTP_200_OK,
        )


class Confirm2FAActivation(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user
        token = request.data.get('token')

        if user.isTwoFactorEnabled:
            return Response(
                {'error': '2FA is already enabled.'}, status=status.HTTP_400_BAD_REQUEST
            )

        email_device = None
        for device in devices_for_user(user, confirmed=False):
            if isinstance(device, EmailDevice) and device.name == 'default':
                email_device = device
                break

        if not email_device:
            return Response({'error': 'Email device not found'}, status=status.HTTP_404_NOT_FOUND)

        if email_device.verify_token(token):
            # Enable 2FA device
            email_device.confirmed = True
            email_device.save()

            # Enable 2FA for the user
            user.isTwoFactorEnabled = True
            user.save()
            return Response({'success': '2FA is now activated.'}, status=status.HTTP_200_OK)
        else:
            return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)


class Disable2FA(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user

        device = EmailDevice.objects.filter(user=user, confirmed=True).first()
        if not device or not user.isTwoFactorEnabled:
            return Response(
                {'error': '2FA is not enabled. Please enable 2FA before trying to disable it.'},
                status=status.HTTP_409_CONFLICT,
            )

        try:
            device.generate_challenge()
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(
            {'message': 'Token sent to your email. Please confirm to disable 2FA.'},
            status=status.HTTP_200_OK,
        )


class Confirm2FADeactivation(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user
        token = request.data.get('token')

        if not user.isTwoFactorEnabled:
            return Response({'error': '2FA is not enabled.'}, status=status.HTTP_400_BAD_REQUEST)

        email_device = None
        for device in devices_for_user(user, confirmed=True):
            if isinstance(device, EmailDevice) and device.name == 'default':
                email_device = device
                break

        if not email_device:
            return Response({'error': 'Email device not found'}, status=status.HTTP_404_NOT_FOUND)

        if email_device.verify_token(token):
            # Disable 2FA for the user
            user.isTwoFactorEnabled = False
            user.save()

            for device in TOTPDevice.objects.filter(user=user):
                device.delete()

            email_device.delete()

            return Response({'success': '2FA is now disabled.'}, status=status.HTTP_200_OK)
        else:
            return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)


class QRCodeAPIView(views.APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = request.user

        if not user.isTwoFactorEnabled:
            return Response(
                {'error': 'Please enable 2FA first.'}, status=status.HTTP_400_BAD_REQUEST
            )

        # Clean up unconfirmed devices
        for device in TOTPDevice.objects.filter(user=user, confirmed=False):
            device.delete()

        # Verify if the user has reached the maximum number of devices
        confirmed_devices_count = TOTPDevice.objects.filter(user=user, confirmed=True).count()
        if confirmed_devices_count >= settings.MAX_TOTP_DEVICES:
            return Response(
                {'error': f'Maximum number of devices ({settings.MAX_TOTP_DEVICES}) exceeded.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Determine the device name
        if confirmed_devices_count == 0:
            device_name = 'default'
        else:
            existing_device_names = TOTPDevice.objects.filter(
                user=user, confirmed=True
            ).values_list('name', flat=True)
            device_index = 1
            while f'device_{device_index}' in existing_device_names:
                device_index += 1
            device_name = f'device_{device_index}'

        # Create the device
        device = TOTPDevice.objects.create(user=user, confirmed=False, name=device_name)

        try:
            uri = device.config_url
            img = qrcode.make(uri, image_factory=qrcode.image.svg.SvgImage)
            buffer = BytesIO()
            img.save(buffer)
            buffer.seek(0)
            image_svg = buffer.getvalue().decode()
        except Exception as e:
            # Clean up the device if the QR code generation fails
            device.delete()
            return Response(
                {'error': f'Failed to generate QR Code: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        return HttpResponse(image_svg, content_type='image/svg+xml')


class QRCodeConfirmAPIView(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user
        token = request.data.get('2fa_token')
        device_id = request.data.get(
            'device_id'
        )  # Adicione um campo para identificar o dispositivo

        # Find the device by ID
        device = TOTPDevice.objects.filter(user=user, confirmed=False, id=device_id).first()
        if not device:
            return Response({'error': '2FA device not found.'}, status=status.HTTP_404_NOT_FOUND)

        if device.verify_token(token):
            device.confirmed = True
            device.save()
            return Response({'success': '2FA device now enabled.'}, status=status.HTTP_200_OK)
        else:
            return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)


class TokenObtainPair2FAView(TokenObtainPairView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return request.META.get('HTTP_DO_CONNECTING_IP') or ip

    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        password = request.data.get('password')
        token = request.data.get('2fa_token')

        user = authenticate(email=email, password=password)
        if user is None:
            return Response(
                {'error': _('Invalid credentials.')}, status=status.HTTP_401_UNAUTHORIZED
            )

        if user.isTwoFactorEnabled:
            if not token:
                device = EmailDevice.objects.filter(user=user, confirmed=True, name='default').first()
                if device:
                    device.generate_challenge()
                    generated_token = device.token
                    send_mail(
                        subject='Código de verificação 2FA',
                        message=f'Seu código é: {generated_token}',
                        from_email=settings.DEFAULT_FROM_MAIL,
                        recipient_list=[user.email],
                        fail_silently=False,
                    )
                return Response(
                    {'error': _('2FA token is required.')}, status=status.HTTP_400_BAD_REQUEST
                )
            devices = TOTPDevice.objects.filter(user=user, confirmed=True)
            valid_token = any(device.verify_token(token) for device in devices)
            if not valid_token:
                device = EmailDevice.objects.filter(user=user, confirmed=True, name='default').first()
                if not device or not device.verify_token(token):
                    return Response(
                        {'error': _('Invalid 2FA token.')},
                        status=status.HTTP_401_UNAUTHORIZED
                    )

        register_access.delay(
            user=user,
            request_data={
                "ip_address": self.get_client_ip(request),
                "user_agent": request.META.get('HTTP_USER_AGENT', '')
            }
        )
        return super().post(request, *args, **kwargs)


class List2FADevices(views.APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = request.user
        devices = TOTPDevice.objects.filter(user=user)
        serializer = TOTPDeviceSerializer(devices, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class Remove2FADevice(views.APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, pk=None):
        user = request.user
        device = TOTPDevice.objects.filter(pk=pk, user=user).first()

        if device:
            total_devices = TOTPDevice.objects.filter(user=user).count()

            if total_devices == 1:
                # Send a token to the user's email to confirm the removal of the last device
                email_device = EmailDevice.objects.filter(user=user).first()
                try:
                    email_device.generate_challenge()
                except Exception as e:
                    return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                return Response(
                    {'message': 'Token sent to your email. Please confirm to disable 2FA.'},
                    status=status.HTTP_200_OK,
                )
            else:
                # Remove the device
                device.delete()
                return Response(
                    {'message': 'Device removed successfully.'}, status=status.HTTP_200_OK
                )
        else:
            return Response({'error': 'Device not found.'}, status=status.HTTP_404_NOT_FOUND)


class ConfirmRemoveAll2FADevices(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user
        token = request.data.get('token')

        email_device = None
        for device in devices_for_user(user, confirmed=True):
            if isinstance(device, EmailDevice) and device.name == 'default':
                email_device = device
                break

        if not email_device:
            return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)

        if email_device.verify_token(token):
            # Disable 2FA for the user
            user.isTwoFactorEnabled = False
            user.save()

            # Also delete all other 2FA devices
            for device in TOTPDevice.objects.filter(user=user):
                device.delete()

            email_device.delete()

            return Response({'success': '2FA is now disabled.'}, status=status.HTTP_200_OK)
        else:
            return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)


class UserMeView(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def retrieve(self, request):
        context = {
            'request': request,
            'expand_user_permissions': "user_permissions" in request.query_params.get("expand", ""),
            'expand_api_endpoints': "api_endpoint" in request.query_params.get("expand", "")
        }

        serializer = UserSerializer(request.user, context=context)
        return Response(serializer.data)


class EventsView(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    queryset = Event.objects.all()
    serializer_class = EventSerializer


class WithdrawalIPViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    queryset = WithdrawalIP.objects.all()
    serializer_class = WithdrawalIPSerializer

    def get_queryset(self):
        return WithdrawalIP.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class UserCertificateViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = UserCertificateSerializer

    def get_queryset(self):
        return UserCertificate.objects.filter(user=self.request.user)

    def create(self, request, *args, **kwargs):
        user = request.user

        # Generate a private key
        key = rsa.generate_private_key(
            public_exponent=65537, key_size=2048, backend=default_backend()
        )

        # Certificate subject
        subject = issuer = x509.Name(
            [
                x509.NameAttribute(NameOID.COUNTRY_NAME, 'BR'),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, 'São Paulo'),
                x509.NameAttribute(NameOID.LOCALITY_NAME, 'São Paulo'),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, 'Cakto'),
                x509.NameAttribute(NameOID.COMMON_NAME, '{}'.format(user.username)),
            ]
        )

        # Generate a self-signed certificate
        cert = (
            x509.CertificateBuilder()
            .subject_name(subject)
            .issuer_name(issuer)
            .public_key(key.public_key())
            .serial_number(x509.random_serial_number())
            .not_valid_before(datetime.datetime.utcnow())
            .not_valid_after(datetime.datetime.utcnow() + timedelta(days=365))
            .add_extension(
                x509.SubjectAlternativeName([x509.DNSName('localhost')]),
                critical=False,
            )
            .sign(key, hashes.SHA256(), default_backend())
        )

        # Serialize private and public keys
        private_key = key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.TraditionalOpenSSL,
            encryption_algorithm=serialization.NoEncryption(),
        )
        public_key = key.public_key().public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )
        certificate = cert.public_bytes(serialization.Encoding.PEM)

        user_dir = str(uuid.uuid4())
        user_dir_path = os.path.join('certificates', user_dir)

        Path(user_dir_path).mkdir(parents=True, exist_ok=True)
        path_private = 'private_key.pem'
        path_public = 'public_key.pem'
        path_certificate = 'certificate.pem'

        private_key_file = ContentFile(private_key, name=path_private)
        public_key_file = ContentFile(public_key, name=path_public)
        certificate_file = ContentFile(certificate, name=path_certificate)

        if UserCertificate.objects.filter(user=user).exists():
            UserCertificate.objects.filter(user=user).delete()

        user_certificate = UserCertificate.objects.create(
            user=user,
            uuid=user_dir,
            private_key=private_key_file,
            certificate=certificate_file,
            public_key=public_key_file,
        )
        return Response(
            {'message': 'Certificate successfully generated.'}, status=status.HTTP_201_CREATED
        )


class AnalysisViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = AnalysisSerializer
    queryset = Analysis.objects.all()
    filterset_class = AnalysisFilter


class UserPermissionViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated, IsAdminUser]

    def retrieve(self, request, user_id=None):
        user = get_object_or_404(User, id=user_id)
        permissions = user.user_permissions.values_list('codename', flat=True)
        return Response({'permissions': permissions}, status=status.HTTP_200_OK)

    def update(self, request, user_id=None):
        user = get_object_or_404(User, id=user_id)
        permissions_codenames = request.data.get('permissions', [])

        user.user_permissions.clear()

        if permissions_codenames:
            permissions = Permission.objects.filter(codename__in=permissions_codenames)
            user.user_permissions.set(permissions)

        return Response(status=status.HTTP_204_NO_CONTENT)
