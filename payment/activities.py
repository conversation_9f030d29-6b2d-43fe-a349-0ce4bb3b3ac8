from typing import Callable
from asgiref.sync import sync_to_async

from customer.models import Card
from payment.models import Payment, Subscription
from .strategies.base import PaymentStrategy
from .services.payment_factory import PaymentStrategyFactory
from temporalio import activity
from django.utils import timezone
from payment.tasks import schedule_payment, handleWebhooks, schedule_payment_check
from .strategies.subscription.base import SubscriptionStrategy


def schedule_retry(payment: Payment, subscription: Subscription) -> str:
    if payment.retry_count >= subscription.max_retries:
        payment.retry_scheduled = False
        payment.status = 'failed'
        payment.save(update_fields=['status', 'retry_scheduled'])

        subscription.cancel()

        return f"Payment {payment.id} failed permanently after {payment.retry_count} retries"

    payment.retry_count += 1
    payment.due_date = timezone.now() + SubscriptionStrategy.get_time_delta_from_interval(
        time_interval=subscription.retry_interval)
    payment.retry_scheduled = True
    payment.status = 'scheduled'
    payment.save(update_fields=['retry_count', 'due_date', 'retry_scheduled', 'status'])
    schedule_payment(payment, fire_webhook=False)

    return f"Payment {payment.id} failed, retrying with new due date {payment.due_date} (attempt {payment.retry_count})"


def after_payment_execution_credit_card_payment_method(payment: Payment, subscription: Subscription):
    if not payment.status == 'paid':
        subscription.retry_count += 1
        subscription.save(update_fields=['retry_count'])
        return schedule_retry(payment, subscription)

    subscription.status = Subscription.STATUS_ACTIVE
    subscription.save(update_fields=['status'])

    message = f"Payment {payment.id} processed successfully"

    if subscription.quantity_recurrences == -1:
        result: str = schedule_next_recurrent_payment(payment, subscription)
        message += result

    return message


def after_payment_execution_pix_or_bill_payment_method(payment: Payment, subscription: Subscription):
    if not payment.status == 'scheduled':
        subscription.retry_count += 1
        subscription.save(update_fields=['retry_count'])
        return schedule_retry(payment, subscription)

    # the original status is scheduled, so after the payment is processed, it should be pending
    payment.status = 'pending'
    payment.save(update_fields=['status'])

    schedule_payment_check(payment)
    return f"Payment {payment.id} processed successfully"


def after_payment_execution(payment: Payment, subscription: Subscription):
    process_methods: dict[str, Callable] = {
        'credit_card': after_payment_execution_credit_card_payment_method,
        'pix': after_payment_execution_pix_or_bill_payment_method,
        'boleto': after_payment_execution_pix_or_bill_payment_method,
    }

    if process_func := process_methods.get(payment.paymentMethod):
        return process_func(payment, subscription)
    raise Exception('Invalid payment method')


def schedule_next_recurrent_payment(payment: Payment, subscription: Subscription) -> str:
    due_date = timezone.now() + SubscriptionStrategy.get_time_delta_from_interval(
        time_interval=subscription.recurrence_period)

    splits = payment.splits.all()

    payment.pk = None
    payment.id = None
    payment._state.adding = True
    payment.due_date = due_date
    payment.status = 'scheduled'
    if payment.interest:
        payment.amount -= payment.interest
    payment.save()

    if splits:
        payment.splits.set(splits)

    schedule_payment(payment)

    return f"Next payment scheduled for {due_date}"


@sync_to_async
def get_payment_with_subscription(payment_id: str) -> Payment:
    return (
        Payment.objects.select_related("subscription", "card")
        .get(id=payment_id, subscription__isnull=False)
    )


@activity.defn
async def process_payment_activity(payment_id: str) -> str:
    try:
        payment: Payment = await get_payment_with_subscription(payment_id)
        subscription: Subscription = payment.subscription
        card: Card = payment.card
        payment_method = payment.paymentMethod
    except Payment.DoesNotExist:
        return f"Payment {payment_id} does not exist"

    if not payment.status == 'scheduled':
        return f"Payment {payment_id} is not in a scheduled state"

    payment_strategy: PaymentStrategy = await sync_to_async(PaymentStrategyFactory.get_strategy)(payment_method)
    payment = await sync_to_async(payment_strategy.executePayment)(payment, card)

    result: str = await sync_to_async(after_payment_execution)(payment, subscription)

    await sync_to_async(handleWebhooks)(payment)

    return result


@activity.defn
async def check_payment_status_activity(payment_id: str) -> str:
    try:
        payment = await get_payment_with_subscription(payment_id)
    except Payment.DoesNotExist:
        return f"Payment {payment_id} does not exist"

    subscription: Subscription = payment.subscription

    if payment.status != 'paid':
        subscription_status = Subscription.STATUS_CANCELED
        payment.status = 'cancelled'
        await payment.asave(update_fields=['status'])
    else:
        subscription_status = Subscription.STATUS_ACTIVE

    message = f"Payment {payment_id} is {payment.status}, subscription {subscription.id} {subscription_status}"

    if subscription.status == Subscription.STATUS_ACTIVE and subscription.quantity_recurrences == -1:
        result: str = schedule_next_recurrent_payment(payment, subscription)
        message += result

    subscription.status = subscription_status
    await subscription.asave(update_fields=['status'])

    return message
