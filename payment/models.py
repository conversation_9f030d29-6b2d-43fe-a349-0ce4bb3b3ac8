import logging
import uuid
from decimal import Decimal

from django.core.cache import cache
from django.db import models
from django.db.models import Case, When, Value, BooleanField
from django.utils import timezone
from django_lifecycle import (
    AFTER_CREATE,
    AFTER_UPDATE,
    BEFORE_UPDATE,
    LifecycleModelMixin,
    hook, LifecycleModel,
)

from gateway.company.hoppy import Hopypay
from gateway.models import Extract, PendingBalance, Acquirer

logger = logging.getLogger(__name__)


class Payment(LifecycleModelMixin, models.Model):
    class Meta:
        permissions = [
            ("view_payments", "Can view payments"),
            ("add_payments", "Can add payments"),
            ("change_payments", "Can change payments"),
            ("delete_payments", "Can delete payments"),
            ("manage_payments", "Can manage payments"),
        ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    user = models.ForeignKey('user.User', on_delete=models.CASCADE, related_name='payments', db_index=True)

    # Acquirer data
    externalId = models.CharField(max_length=255)
    chargeId = models.CharField(max_length=255, null=True, blank=True)
    e2eId = models.CharField(max_length=255, null=True, blank=True)
    acquirerType = models.CharField(max_length=255)
    acquirer = models.ForeignKey('gateway.Acquirer', on_delete=models.CASCADE, null=True, blank=True)

    # Customer data
    customer = models.ForeignKey('customer.Customer', on_delete=models.CASCADE)
    address = models.ForeignKey('customer.Address', on_delete=models.CASCADE, null=True, blank=True)

    # Payment data
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    baseAmount = models.DecimalField(max_digits=10, decimal_places=2)
    installments = models.IntegerField(default=1)
    paidAmount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    refundedAmount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    refundId = models.CharField(max_length=255, null=True, blank=True)
    feeByItem = models.BooleanField(default=False)
    status = models.CharField(max_length=255, db_index=True, choices=(
        ('paid', 'Paid'),
        ('refunded', 'Refunded'),
        ('pending', 'Pending'),
        ('refused', 'Refused'),
        ('canceled', 'Canceled'),
        ('declined', 'Declined'),
        ('blocked', 'Blocked'),
        ('in_protest', 'In Protest'),
        ('chargedback', 'Chargedback'),
        ('prechargeback', 'Prechargeback'),
        ('partially_paid', 'Partially Paid'),
        ('scheduled', 'Scheduled'),
        ('retrying', 'Retrying'),
        ('MED', 'Med'),
    ), default='pending')
    reason = models.CharField(max_length=255, null=True, blank=True)
    internalReason = models.CharField(max_length=255, null=True, blank=True)
    paymentMethod = models.CharField(max_length=255, db_index=True, choices=(
        ('credit_card', 'Credit Card'),
        ('boleto', 'Boleto'),
        ('pix', 'Pix'),
        ('picpay', 'PicPay'),
        ('nupay', 'NuPay'),
        ('googlepay', 'GooglePay'),
        ('applepay', 'ApplePay'),
        ('openfinance_nubank', 'Open Finance Nubank'),
    ), default='credit_card')
    description = models.CharField(max_length=255, null=True, blank=True)
    exId = models.CharField(max_length=255, null=True, blank=True)
    checkoutUrl = models.TextField(null=True, blank=True)
    refererUrl = models.TextField(null=True, blank=True)
    receipt = models.TextField(null=True, blank=True)
    nextStep = models.TextField(null=True, blank=True)

    # User fee
    fine = models.DecimalField(max_digits=10, decimal_places=2, default=0)  # Chargeback fine
    gatewayFee = models.DecimalField(max_digits=10, decimal_places=2, default=0)  # Gateway fee
    interest = models.DecimalField(max_digits=10, decimal_places=2, default=0)  # Interest

    # Fee
    fixedFee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    percentageFee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)  # fee
    liquidAmount = models.DecimalField(max_digits=10, decimal_places=2, default=0)  # amount received

    # Acquirer cost
    costRelease = models.IntegerField(default=0)
    cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)  # cost
    fixedCost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    percentageCost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    rawPercentageCost = models.DecimalField(max_digits=10, decimal_places=2, default=0)  # raw percentage cost

    # Gateway data
    profit = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Product data
    items = models.JSONField(default=list)

    # Card data
    card = models.ForeignKey('customer.Card', on_delete=models.CASCADE, null=True, blank=True)

    # Boleto data
    boleto = models.ForeignKey('payment.Boleto', on_delete=models.CASCADE, null=True, blank=True)

    # Pix data
    pix = models.ForeignKey('payment.Pix', on_delete=models.CASCADE, null=True, blank=True)

    # PicPay data
    picpay = models.ForeignKey('payment.PicPay', on_delete=models.CASCADE, null=True, blank=True)

    # NuPay data
    nupay = models.ForeignKey('payment.NuPay', on_delete=models.CASCADE, null=True, blank=True)

    # GooglePay data
    googlepay = models.ForeignKey('payment.GooglePay', on_delete=models.CASCADE, null=True, blank=True)

    # ApplePay data
    applepay = models.ForeignKey('payment.ApplePay', on_delete=models.CASCADE, null=True, blank=True)

    # Open Finance Nubank data
    openFinanceNubank = models.ForeignKey('payment.OpenFinanceNubank', on_delete=models.CASCADE, null=True, blank=True)

    # Mercado Pago data
    mercadopago = models.ForeignKey('payment.MercadoPago', on_delete=models.CASCADE, null=True, blank=True)

    # Postback
    postbackUrl = models.CharField(max_length=255, null=True, blank=True)

    # Antifraud
    refusedByAntifraud = models.BooleanField(default=False)
    antifraud_recomendation_id = models.CharField(max_length=255, null=True, blank=True)
    antifraud_profiling_attempt_reference = models.CharField(max_length=255, null=True, blank=True)
    antifraud_transaction_id = models.CharField(max_length=255, null=True, blank=True)

    # Division
    producerSplit = models.ForeignKey('payment.Split', on_delete=models.CASCADE, null=True, blank=True,
                                      related_name='payment_producer')
    affiliateSplit = models.ForeignKey('payment.Split', on_delete=models.CASCADE, null=True, blank=True,
                                       related_name='payment_affiliate')
    splits = models.ManyToManyField('payment.Split', blank=True, related_name='payment_splits')

    # Tracking
    trackingCode = models.CharField(max_length=255, null=True, blank=True)

    # Times
    createdAt = models.DateTimeField(auto_now_add=True, db_index=True)
    updatedAt = models.DateTimeField(auto_now=True)
    paidAt = models.DateTimeField(null=True, blank=True, db_index=True)
    chargedbackAt = models.DateTimeField(null=True, blank=True, db_index=True)
    refundedAt = models.DateTimeField(null=True, blank=True)

    expiresInDays = models.IntegerField(null=True, blank=True)

    # NFe
    nfeId = models.CharField(max_length=255, null=True, blank=True)
    nfeURL = models.CharField(max_length=255, null=True, blank=True)
    nfeStatus = models.CharField(max_length=255, null=True, blank=True)

    # Subscription
    due_date = models.DateTimeField(null=True, blank=True)
    subscription = models.ForeignKey(
        'Subscription',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='payments'
    )
    retry_count = models.SmallIntegerField(
        default=0,
        help_text='The number of retries that have been attempted'
    )
    retry_scheduled = models.BooleanField(default=False, help_text='O pagamento está agendado para uma retentativa')

    def __str__(self):
        return str(self.id) or ''

    def getTx(self):
        if self.acquirerType == 'hopypay':
            hopypay = Hopypay(self.acquirer)
            return hopypay.getTransaction(self.externalId)

    def can_refund(self):
        return self.status == 'paid'

    def can_be_refunded(self):
        return self.status != 'refunded'

    def chargeback(self, amount=None):
        from payment.tasks import handleWebhooks, handle_financial_health_score

        if self.status == 'paid':
            fee = self.user.getFee()

            self.status = 'MED' if self.paymentMethod == 'pix' else 'chargedback'
            self.chargedbackAt = timezone.now()
            self.save()

            chargeback_amount = self.baseAmount

            if amount:
                chargeback_amount += Decimal(amount)
            elif fee.get('prechargebackFixed'):
                chargeback_amount += fee.get('prechargebackFixed')

            for split in self.splits.all():
                if split.pendingBalance:
                    if split.pendingBalance.status == 'paid':
                        # Update the balance of the user with the amount of the split

                        split.pendingBalance.status = 'chargedback'
                        split.pendingBalance.save()

                    split.user.deduct_balance(split.pendingBalance.amount, transaction_type=Extract.CHARGEBACK,
                                              transaction_object=split.pendingBalance)
                if split.pendingBalanceReserve:
                    if split.pendingBalance.status == 'paid':
                        # Update the balance of the user with the amount of the split

                        split.pendingBalance.status = 'chargedback'
                        split.pendingBalance.save()
                    split.user.deduct_balance(split.pendingBalanceReserve.amount, transaction_type=Extract.CHARGEBACK,
                                              transaction_object=split.pendingBalanceReserve)

        handleWebhooks(self)
        handle_financial_health_score.delay(self.user)

    def cancel_chargeback(self, amount=None):
        from payment.tasks import handleWebhooks, handle_financial_health_score

        if self.status in ['chargedback', 'MED']:
            self.status = 'paid'
            self.save()

            for split in self.splits.all():
                if split.pendingBalance:
                    if split.pendingBalance.status == 'chargedback':
                        # Update the balance of the user with the amount of the split
                        split.pendingBalance.status = 'paid'
                        split.pendingBalance.save()

                    split.user.add_balance(split.pendingBalance.amount, transaction_type=Extract.CHARGEBACK,
                                           transaction_object=split.pendingBalance)
                if split.pendingBalanceReserve:
                    if split.pendingBalance.status == 'chargedback':
                        # Update the balance of the user with the amount of the split
                        split.pendingBalance.status = 'paid'
                        split.pendingBalance.save()
                    split.user.add_balance(split.pendingBalanceReserve.amount, transaction_type=Extract.CHARGEBACK,
                                           transaction_object=split.pendingBalanceReserve)

        handleWebhooks(self)
        handle_financial_health_score.delay(self.user)

    @property
    def pending(self):
        return self.pending_balances.get('pending')

    @property
    def pendingReserve(self):
        return self.pending_balances.get('pending_reverse')

    @property
    def pending_balances(self) -> dict:
        cache_key = f'pending_balances_{self.id}'
        cached_balances = cache.get(cache_key)

        if cached_balances:
            return cached_balances

        balances = (
            PendingBalance.objects.filter(payment=self, user=self.user)
            .annotate(
                is_pending=Case(
                    When(isReserve=False, then=Value(True)),
                    default=Value(False),
                    output_field=BooleanField(),
                )
            )
            .order_by("isReserve", "-id")  # Prioritize isReserve=False, then order by latest
        )

        last_pending = next((balance for balance in balances if not balance.isReserve), None)
        last_pending_reserve = next((balance for balance in balances if balance.isReserve), None)

        result = {
            "pending": last_pending,
            "pending_reverse": last_pending_reserve,
        }

        cache.set(cache_key, result, timeout=60)
        return result

    @property
    def hasPaymentDispute(self) -> bool:
        return self.paymentDispute.exists()

    @property
    def is_recurrent(self) -> bool:
        """
        A recurrent payment is a payment that is part of a subscription and is not the first payment of the subscription.
        """
        if subscription := self.subscription:
            return subscription.first_subscription_payment != self
        return False

    def get_payment_dispute(self):
        return self.paymentDispute.last()

    def log_refused_payment(self, reason_message: str = None):
        RefusedPaymentLog.objects.create(
            payment=self,
            acquirer=self.acquirer,
            amount=self.amount,
            reason=reason_message if reason_message else self.reason,
            status=self.status
        )

    @hook(AFTER_CREATE)
    def _insert_payment_on_bigquery(self):
        from payment.tasks import insert_payment_on_bigquery

        try:
            insert_payment_on_bigquery.delay(payment=self)
        except Exception as e:
            logger.error(
                f'Error occurred while inserting payment on BigQuery in AFTER_CREATE signal: {e}'
            )

    @hook(AFTER_CREATE)
    def _log_payment_created(self):
        try:
            PaymentStatusChangeLog.object.log_payment_created(payment=self)
        except Exception as e:
            logger.error(
                f'Error occurred while logging payment creation in AFTER_CREATE signal: {e}'
            )

    @hook(BEFORE_UPDATE, when='status', has_changed=True)
    def _log_payment_status_change(self):
        try:
            match self.status:
                case 'refused':
                    PaymentStatusChangeLog.object.log_payment_refused(payment=self)
                case 'paid':
                    PaymentStatusChangeLog.object.log_payment_approved(payment=self)
                case 'chargedback':
                    PaymentStatusChangeLog.object.log_payment_charged_back(payment=self)
                case 'MED':
                    PaymentStatusChangeLog.object.log_payment_charged_back(payment=self)
                case 'canceled':
                    PaymentStatusChangeLog.object.log_payment_canceled(payment=self)
        except Exception as e:
            logger.error(
                f'Error occurred while logging payment status change in BEFORE_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when="acquirer_id", has_changed=True)
    def _update_acquirer_id_on_bigquery(self):
        from payment.tasks import update_payment_by_id_on_bigquery

        try:
            update_payment_by_id_on_bigquery.delay(
                payment_id=str(self.id),
                field_to_update='acquirer_id',
                new_value=str(self.acquirer_id)
            )
        except Exception as e:
            logger.error(
                f'Error occurred while updating acquirer_id on BigQuery in AFTER_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when='status', has_changed=True)
    def _update_payment_on_bigquery(self):
        from payment.tasks import update_payment_by_id_on_bigquery

        try:
            update_payment_by_id_on_bigquery.delay(
                payment_id=str(self.id),
                field_to_update='status',
                new_value=str(self.status)
            )
        except Exception as e:
            logger.error(
                f'Error occurred while updating status on BigQuery in AFTER_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when='profit', has_changed=True)
    def _update_payment_profit_on_bigquery(self):
        from payment.tasks import update_payment_by_id_on_bigquery

        try:
            update_payment_by_id_on_bigquery.delay(
                payment_id=str(self.id),
                field_to_update='profit',
                new_value=float(self.profit)
            )
        except Exception as e:
            logger.error(
                f'Error occurred while updating profit on BigQuery in AFTER_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when='interest', has_changed=True)
    def _update_payment_interest_on_bigquery(self):
        from payment.tasks import update_payment_by_id_on_bigquery

        try:
            update_payment_by_id_on_bigquery.delay(
                payment_id=str(self.id),
                field_to_update='interest',
                new_value=float(self.interest)
            )
        except Exception as e:
            logger.error(
                f'Error occurred while updating interest on BigQuery in AFTER_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when='cost', has_changed=True)
    def _update_payment_cost_on_bigquery(self):
        from payment.tasks import update_payment_by_id_on_bigquery

        try:
            update_payment_by_id_on_bigquery.delay(
                payment_id=str(self.id),
                field_to_update='cost',
                new_value=float(self.cost)
            )
        except Exception as e:
            logger.error(
                f'Error occurred while updating cost on BigQuery in AFTER_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when='fee', has_changed=True)
    def _update_payment_fee_on_bigquery(self):
        from payment.tasks import update_payment_by_id_on_bigquery

        try:
            update_payment_by_id_on_bigquery.delay(
                payment_id=str(self.id),
                field_to_update='fee',
                new_value=float(self.fee)
            )
        except Exception as e:
            logger.error(
                f'Error occurred while updating fee on BigQuery in AFTER_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when='chargedbackAt', has_changed=True)
    def _update_payment_charged_back_at_field_on_bigquery(self):
        from payment.tasks import update_payment_by_id_on_bigquery

        try:
            update_payment_by_id_on_bigquery.delay(
                payment_id=str(self.id),
                field_to_update='chargedback_at',
                new_value=str(self.chargedbackAt)
            )
        except Exception as e:
            logger.error(
                f'Error occurred while updating chargedback_at on BigQuery in AFTER_UPDATE signal: {e}'  # noqa: E501
            )

    @hook(AFTER_UPDATE, when='amount', has_changed=True)
    def _update_payment_amount_field_on_bigquery(self):
        from payment.tasks import update_payment_by_id_on_bigquery

        try:
            update_payment_by_id_on_bigquery.delay(
                payment_id=str(self.id),
                field_to_update='amount',
                new_value=float(self.amount)
            )
        except Exception as e:
            logger.error(
                f'Error occurred while updating amount on BigQuery in AFTER_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when='status', has_changed=True, was='pending', is_now='paid')
    def _update_subscription_status_after_payment_is_paid(self):
        from payment.services.subscription_factory import SubscriptionFactory

        try:
            if self.subscription and self.paymentMethod in ['pix', 'boleto'] and \
                self.subscription.status == Subscription.STATUS_INACTIVE:
                self.subscription.status = Subscription.STATUS_ACTIVE
                self.subscription.save(update_fields=['status'])
                return

            for item in self.items:
                subscription_id = item.get("subscription")
                if subscription_id:
                    subscription = Subscription.objects.filter(id=subscription_id).first()
                    if subscription:
                        strategy = SubscriptionFactory.get_strategy('bulk')
                        strategy.execute_subscription(payment=self, subscription=subscription)

        except Exception as e:
            logger.error(f"Error updating subscription status in AFTER_UPDATE signal: {e}")

    @hook(AFTER_UPDATE, when='status', has_changed=True, is_now='refunded')
    def _update_antifraud_transaction_after_refund(self):
        if self.is_recurrent:
            return

        try:
            from payment.services.antifraud_factory import AntifraudStrategyFactory
            antifraud = AntifraudStrategyFactory.get_active_strategy_or_none()
            if antifraud:
                antifraud.update_transaction(self)
            else:
                print(
                    'No active antifraud found.'
                    ' Skipping _update_antifraud_transaction_after_refund.'
                )
        except Exception as e:
            logger.error(
                f'Error occurred while updating antifraud transaction in AFTER_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when='status', has_changed=True, is_now='chargedback')
    def _update_antifraud_transaction_after_chargedback(self):
        if self.is_recurrent:
            return
        try:
            from payment.services.antifraud_factory import AntifraudStrategyFactory
            antifraud = AntifraudStrategyFactory.get_active_strategy_or_none()
            if antifraud:
                antifraud.update_transaction(self)
            else:
                print(
                    'No active antifraud found.'
                    ' Skipping _update_antifraud_transaction_after_chargedback.'
                )
        except Exception as e:
            logger.error(
                f'Error occurred while updating antifraud transaction in AFTER_UPDATE signal: {e}'
            )

    @hook(AFTER_UPDATE, when='status', has_changed=True, was='paid', is_now='chargedback')
    def _update_payment_status_from_chargeback_to_med_if_payment_method_is_pix(self):
        """
        This hook is used to update the payment status from 'chargedback' to 'MED' if the payment method is 'pix'.
        @return:
        """
        if not self.paymentMethod == 'pix' or not self.status == 'chargedback':
            return

        self.status = 'MED'
        self.save(update_fields=['status'], skip_hooks=True)


    @hook(AFTER_UPDATE, when='status', has_changed=True, was='scheduled', is_now='canceled')
    def _fire_webhook_when_payment_canceled(self):
        from payment.tasks import handleWebhooks

        logger.info(
            f"firing webhook after payment canceled, payment_id={self.id}, payment_subscription_id={self.subscription_id}"
        )

        handleWebhooks.delay(
            payment=self,
            subscription=None,
            event_type='transaction.canceled',
        )


class Split(models.Model):
    user = models.ForeignKey('user.User', on_delete=models.CASCADE, related_name='user_splits')
    type = models.CharField(max_length=255, choices=(
        ('producer', 'Produtor'),
        ('coproducer', 'Coprodutor'),
        ('affiliate', 'Affiliate'),
    ), default='coproducer')
    payment = models.ForeignKey('payment.Payment', on_delete=models.CASCADE, related_name='payment_splits', null=True,
                                blank=True)
    pendingBalance = models.ForeignKey('gateway.PendingBalance', on_delete=models.CASCADE, null=True, blank=True)
    pendingBalanceReserve = models.ForeignKey('gateway.PendingBalance', on_delete=models.CASCADE, null=True, blank=True,
                                              related_name='split_pending_balance_reserve')
    rawPercentage = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    percentage = models.DecimalField(max_digits=10, decimal_places=2)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    amountReserve = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    def __str__(self):
        return f'{self.user.email} - {self.percentage}%'


class Boleto(models.Model):
    barcode = models.CharField(max_length=255)
    boletoUrl = models.CharField(max_length=255)
    expirationDate = models.CharField(max_length=255)

    def __str__(self):
        return self.barcode


class Pix(models.Model):
    qrCode = models.TextField()
    expirationDate = models.CharField(max_length=255)

    def __str__(self):
        return self.qrCode


class PicPay(models.Model):
    qrCode = models.TextField()
    paymentURL = models.TextField(null=True)
    expirationDate = models.CharField(max_length=255)

    def __str__(self):
        return self.qrCode


class NuPay(models.Model):
    URL = models.TextField()

    def __str__(self):
        return self.URL


class GooglePay(models.Model):
    signature = models.TextField()
    signedMessage = models.TextField()

    def __str__(self):
        return self.signature


class ApplePay(models.Model):
    signature = models.TextField()
    signedMessage = models.TextField()

    def __str__(self):
        return self.signature


class OpenFinanceNubank(models.Model):
    URL = models.TextField()

    def __str__(self):
        return self.URL


class MercadoPago(models.Model):
    deviceId = models.TextField()

    def __str__(self):
        return self.deviceId


class RefusedPaymentLog(models.Model):
    payment = models.ForeignKey('payment.Payment', on_delete=models.CASCADE, related_name='refusedPayments',
                                help_text='The payment that was refused')
    acquirer = models.ForeignKey('gateway.Acquirer', on_delete=models.CASCADE, related_name='refusedPayments',
                                 help_text='The acquirer that refused the payment')
    amount = models.DecimalField(max_digits=10, decimal_places=2, help_text='The amount that was refused')
    reason = models.CharField(max_length=255, help_text='The reason why the payment was refused', null=True, blank=True)
    status = models.CharField(max_length=255, help_text='The payment status', default='refused')
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text='The date and time when the refused payment was created'
    )

    def __str__(self):
        return self.reason


class ReportDownload(models.Model):
    STATUS_PENDING = 'pending'
    STATUS_PROCESSING = 'processing'
    STATUS_COMPLETED = 'completed'
    STATUS_FAILED = 'failed'
    STATUS_CANCELED = 'canceled'

    STATUS_CHOICES = [
        (STATUS_PENDING, 'Pending'),
        (STATUS_PROCESSING, 'Processing'),
        (STATUS_COMPLETED, 'Completed'),
        (STATUS_FAILED, 'Failed'),
        (STATUS_CANCELED, 'Canceled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_PENDING)
    file = models.FileField(upload_to='reports/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    email_sent = models.BooleanField(default=False)

    @property
    def is_ready_for_download(self):
        return self.status == self.STATUS_COMPLETED and self.file

    def __str__(self):
        return f"Report {self.id} - {self.status}"


class PaymentDisputeArea(models.Model):
    payment = models.ForeignKey(
        'payment.Payment',
        on_delete=models.CASCADE,
        related_name='paymentDispute',
        db_index=True,
        help_text='The payment that was disputed'
    )
    description = models.CharField(
        max_length=255,
        help_text='The description of the dispute'
    )
    status = models.CharField(
        db_index=True,
        max_length=255,
        choices=(
            ('pendente', 'Pendente'),
            ('enviado', 'Enviado')
        ),
        default='pendente',
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Payment Dispute Area'


# TODO: Test me
class PaymentDisputeUploadedFile(models.Model):
    payment_dispute = models.ForeignKey(
        'payment.PaymentDisputeArea',
        on_delete=models.CASCADE,
        related_name='uploaded_files',
        db_index=True,
        help_text='The payment dispute that the file is related to',
    )
    file = models.FileField(upload_to='documents/')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.file.name

class Subscription(LifecycleModelMixin, models.Model):
    STATUS_ACTIVE = 'active'
    STATUS_PAUSED = 'paused'
    STATUS_CANCELED = 'canceled'
    STATUS_INACTIVE = 'inactive'

    STATUS_CHOICES = [
        (STATUS_ACTIVE, 'Active'),
        (STATUS_PAUSED, 'Paused'),
        (STATUS_CANCELED, 'Canceled'),
        (STATUS_INACTIVE, 'inactive'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_ACTIVE)
    paymentMethod = models.CharField(max_length=255, db_index=True, choices=(
        ('credit_card', 'Credit Card'),
        ('boleto', 'Boleto'),
        ('pix', 'Pix'),
    ), default='credit_card')
    user = models.ForeignKey('user.User', on_delete=models.CASCADE, related_name='subscriptions')
    card = models.ForeignKey('customer.Card', on_delete=models.SET_NULL, null=True, blank=True)
    recurrence_period = models.IntegerField(null=False, blank=False)
    quantity_recurrences = models.IntegerField(null=False, blank=False)
    trial_days = models.IntegerField(null=True, blank=True)
    max_retries = models.IntegerField(default=3)
    retry_interval = models.IntegerField(default=1)
    retry_count = models.IntegerField(default=0)
    next_payment_date = models.DateTimeField(null=True, blank=True)
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='Valor a ser cobrado através das recorrências'
    )
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'{self.user.email} - {self.status}'


    def cancel(self):
        from payment.tasks import handleWebhooks

        self.status = Subscription.STATUS_CANCELED
        self.save(update_fields=['status'])

        self.payments.filter(status='scheduled').update(status='canceled')
        canceled_payments = self.payments.filter(status='canceled')

        for canceled_payment in canceled_payments:
            handleWebhooks.delay(payment=canceled_payment)

        logger.info(
            f"Subscription canceled successfully. Total canceled payments: {canceled_payments.count()}, Subscription ID: {self.id}"
        )


    @property
    def first_subscription_payment(self):
        return self.payments.order_by('createdAt').first()

    @hook(AFTER_UPDATE, when='status', has_changed=True)
    def _fire_webhook_after_subscription_status_change(self):
        from payment.tasks import handleWebhooks

        logger.info(
            f"Webhook triggered after subscription status change: subscription_id={self.id}, new_status={self.status}"
        )

        handleWebhooks.delay(
            payment=None,
            subscription=self,
            event_type='subscription.subscription_status_changed',
        )

    @hook(AFTER_CREATE)
    def _fire_webhook_after_subscription_creation(self):
        from payment.tasks import handleWebhooks

        logger.info(
            f"Webhook triggered for subscription creation: subscription_id={self.id}"
        )

        handleWebhooks.delay(
            payment=None,
            subscription=self,
            event_type='subscription.subscription_created',
        )

class PaymentStatusChangeQuerySet(models.QuerySet):
    def log_payment_created(self, payment: Payment):
        self.create(
            payment=payment,
            previous_status=None,
            changed_to_status=payment.status,
            description='Pagamento criado',
        )

    def log_payment_refused(self, payment: Payment):
        previous_status = Payment.objects.filter(id=payment.id).values_list('status', flat=True).first()

        self.create(
            payment=payment,
            previous_status=previous_status,
            changed_to_status=payment.status,
            reason=payment.reason,
            description=f'Recusado por {payment.reason}',
        )

    def log_payment_approved(self, payment: Payment):
        previous_status = Payment.objects.filter(id=payment.id).values_list('status', flat=True).first()

        description = f'Verificado com {payment.acquirer.name} e aprovado'
        self.create(
            payment=payment,
            previous_status=previous_status,
            changed_to_status=payment.status,
            description=description,
        )

    def log_payment_charged_back(self, payment: Payment):
        previous_status = Payment.objects.filter(id=payment.id).values_list('status', flat=True).first()

        self.create(
            payment=payment,
            previous_status=previous_status,
            changed_to_status=payment.status,
            description='Chargeback solicitado',
        )

    def log_payment_canceled(self, payment: Payment):
        previous_status = Payment.objects.filter(id=payment.id).values_list('status', flat=True).first()

        self.create(
            payment=payment,
            previous_status=previous_status,
            changed_to_status=payment.status,
            description='Pagamento cancelado',
        )


class PaymentStatusChangeLog(models.Model):
    payment = models.ForeignKey(
        'Payment',
        on_delete=models.CASCADE,
        related_name='status_change_logs',
        help_text='The payment that had its status changed',
    )
    user = models.ForeignKey(
        'user.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text='The user that changed the status of the payment',
        related_name='payment_status_changed_logs',
    )
    previous_status = models.CharField(
        max_length=255,
        help_text='The previous status of the payment',
        null=True,
        blank=True,
    )
    changed_to_status = models.CharField(
        max_length=255,
        help_text='The new status of the payment',
    )
    description = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        help_text='The metadata of the status change',
    )
    reason = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        help_text='The reason for the status change',
    )
    system = models.BooleanField(
        default=True,
        help_text='If the status change was made by the system',
    )
    created_at = models.DateTimeField(auto_now_add=True, help_text='The date and time when the status was changed')
    object = PaymentStatusChangeQuerySet.as_manager()

    def __str__(self):
        return f'{self.payment.id} changed from {self.previous_status} to {self.changed_to_status} at {self.created_at}'

    class Meta:
        verbose_name = 'Payment Status Change Log'
        verbose_name_plural = 'Payment Status Change Logs'
        indexes = [
            models.Index(fields=['payment', 'created_at', 'user']),
        ]
        ordering = ['-created_at']


class Position(models.Model):
    x = models.FloatField(help_text='Posição X na tela')
    y = models.FloatField(help_text='Posição Y na tela')


class FlowActions(LifecycleModel):
    type = models.CharField(help_text='Tipo da ação', max_length=255)
    options = models.JSONField(default=list, help_text='Opções do fluxo')
    position = models.ForeignKey(
        "Position",
        help_text="posição da action na tela do usuário",
        on_delete=models.CASCADE,
        related_name='action_position',
        null=True,
        blank=True,
    )

    def _apply_risk_dilution_percentages_from_options(self):
        for option in self.options:
            result = Acquirer.objects.filter(id=option['acquirerId']).update(
                risk_dilution_percentage=option['percentage']
            )
            if not result:
                logger.error(
                    f'Failed to update risk dilution percentage for acquirer {option["acquirerId"]}'
                )

    @hook(AFTER_CREATE)
    def _apply_risk_dilution_percentage_to_acquirer(self):
        if not self.type == 'randomizer':
            return

        self._apply_risk_dilution_percentages_from_options()

    @hook(AFTER_UPDATE, when='options', has_changed=True)
    def _update_risk_dilution_percentage_to_acquirer(self):
        if not self.type == 'randomizer':
            return

        self._apply_risk_dilution_percentages_from_options()


class AdditionalFlows(LifecycleModel):
    settings = models.JSONField(default=dict, help_text='Configurações do fluxo')
    triggerType = models.CharField(help_text='Tipo de acionamento', max_length=255)
    actions = models.ManyToManyField('FlowActions', related_name='payment_flow_actions', help_text='Ações do fluxo')
    type = models.CharField(help_text='Tipo do fluxo', blank=True, null=True, max_length=255)
    status = models.CharField(
        help_text='Status do fluxo',
        blank=True,
        null=True,
        choices=(("active", "Ativo"), ("disabled", "Desativado")),
        default='active',
        max_length=255
    )
    name = models.CharField(help_text='Nome do fluxo', blank=True, null=True, max_length=255)
    position = models.ForeignKey(
        "Position",
        help_text="posição do trigger na tela do usuário",
        on_delete=models.CASCADE,
        related_name='trigger_position',
        null=True,
        blank=True,
    )


class PaymentReprocessingHistory(LifecycleModel):
    payment = models.ForeignKey(
        'Payment',
        on_delete=models.CASCADE,
        related_name='payment_reprocessing_history',
        help_text="Pagamento que foi reprocessado"
    )

    acquirer = models.ForeignKey(
        'gateway.Acquirer',
        on_delete=models.CASCADE,
        related_name='payment_reprocessingPayment_history',
        help_text='Adquirente onde o pagamento precisou ser reprocessado'
    )

    external_payment_id = models.CharField(
        max_length=255,
        null=False,
        blank=False,
        help_text='ID do pagamento na adquirente'
    )

    status = models.CharField(
        max_length=255,
        choices=(
            ('pending', 'Pending'),
            ('completed', 'Completed'),
            ('failed', 'Failed'),
        ),
        default='pending',
        help_text='Status do reprocessamento do pagamento'
    )
    reason = models.TextField(
        null=True,
        blank=True,
        help_text='Motivo do reprocessamento'
    )
    user = models.ForeignKey(
        'user.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text='Usuário que iniciou o reprocessamento'
    )
    log = models.JSONField(
        default=dict,
        help_text='logs do reprocessamento'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text='data e hora da criação do reprocessamento'
    )
    last_updated_at = models.DateTimeField(
        auto_now=True,
        help_text='data e hora da última atualização do reprocessamento'
    )

    def __str__(self):
        return f"Reprocessing for Payment {self.payment.id} - {self.status}"


    class Meta:
        permissions = [
            ("manage_paymentreprocessing", "Can manage Payment Reprocessing History"),
        ]

