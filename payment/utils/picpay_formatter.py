from re import M
from payment.models import Payment
from django.conf import settings
from datetime import timedelta
from django.utils import timezone
from unidecode import unidecode

class PicPayFormatter:
    @staticmethod
    def transform_to_format(model: Payment) -> dict:
        picpay_data = {}

        if model.paymentMethod == "credit_card":
            picpay_data = {
                "paymentSource": "GATEWAY",
                "merchantChargeId": str(model.id),
                "customer": {
                    "name": model.customer.name,
                    "email": model.customer.email,
                    "document": model.customer.docNumber,
                    "documentType": "CPF" if model.customer.docType == "cpf" else "CNPJ",
                    "phone": {
                        "countryCode": "55",
                        "areaCode": str(model.customer.phone)[:2],
                        "number": str(model.customer.phone)[2:],
                        "type": "MOBILE"
                    }
                },
                "transactions": [
                    {
                        "credit": {
                            "cardNumber": model.card.encryptedNumber,
                            "cvv": model.card.cvv,
                            "cardholderName": unidecode(model.card.holderName),
                            "cardholderDocument": model.customer.docNumber,
                            "expirationMonth": int(model.card.expMonth),
                            "expirationYear": int(f"20{str(model.card.expYear)[-2:]}"),
                            "billingAddress": {
                                "zipCode": (getattr(model.address, "zipcode") or "70000000").replace("-", ""),
                                "street": unidecode(getattr(model.address, "street") or "Avenida do Contorno"),
                                "number": getattr(model.address, "number") or "0",
                                "complement": unidecode(getattr(model.address, "complement") or ""),
                                "neighborhood": unidecode(getattr(model.address, "neighborhood") or "Centro"),
                                "city": unidecode(getattr(model.address, "city") or "Brasília"),
                                "state": getattr(model.address, "state") or "DF",
                                "country": "Brasil"
                            }
                        },
                        "paymentType": "CREDIT",
                        "amount": int(model.amount * 100),
                        "softDescriptor": model.items[0].get("title", "COMPRA"),
                    }
                ]
            }
        elif model.paymentMethod == "pix":
            picpay_data = {
                "paymentSource": "GATEWAY",
                "merchantChargeId": str(model.id),
                "customer": {
                    "name": model.customer.name,
                    "email": model.customer.email,
                    "document": model.customer.docNumber,
                    "documentType": "CPF" if model.customer.docType == "cpf" else "CNPJ",
                    "phone": {
                        "countryCode": "55",
                        "areaCode": str(model.customer.phone)[:2],
                        "number": str(model.customer.phone)[2:],
                        "type": "MOBILE"
                    }
                },
                "transactions": [
                    {
                        "amount": int(model.amount * 100),
                        "pix": {
                            "expiration": 3600
                        }
                    }
                ]
            }
        elif model.paymentMethod == "picpay":
            picpay_data = {
                "paymentSource": "GATEWAY",
                "merchantChargeId": str(model.id),
                "customer": {
                    "name": model.customer.name,
                    "email": model.customer.email,
                    "document": model.customer.docNumber,
                    "documentType": "CPF" if model.customer.docType == "cpf" else "CNPJ",
                    "phone": {
                        "countryCode": "55",
                        "areaCode": str(model.customer.phone)[:2],
                        "number": str(model.customer.phone)[2:],
                        "type": "MOBILE"
                    }
                },
                "transactions": [
                    {
                        "paymentType": "WALLET",
                        "amount": int(model.amount * 100),
                        "softDescriptor": model.items[0].get("title", "COMPRA"),
                    }
                ]
            }

        return picpay_data
