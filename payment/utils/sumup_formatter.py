from payment.models import Payment
from django.conf import settings
from unidecode import unidecode

class SumupFormatter:
    @staticmethod
    def transform_to_checkout_format(model: Payment) -> dict:
        data = {
            "checkout_reference": str(model.id),
            "amount": model.amount,
            "currency": "BRL",
            "pay_to_email":  model.acquirer.keys.get('email'),
            "return_url":settings.BACKEND_URL + '/api/webhook/sumup/'
        }

        return data

    @staticmethod
    def transform_to_creditcard_format(model: Payment) -> dict:
        data = {
            "payment_type": "card",
            "card":{
                "number": model.card.encryptedNumber,
                "name": model.card.holderName,
                "last_4_digits": model.card.encryptedNumber[-4:],
                "expiry_month": model.card.expMonth.zfill(2),
                "expiry_year": model.card.expYear,
                "cvv": model.card.cvv,
                "type":"MASTERCARD"
            }
        }

        return data
