from payment.models import Payment
from django.conf import settings

class MediusFormatter:
    @staticmethod
    def transform_to_format(model: Payment) -> dict:
        data = {
            "amount": int(model.amount * 100),
            "postbackUrl": settings.BACKEND_URL + '/api/webhook/mediuspag/',
            "items":[],
            "customer": {
                "name": model.customer.name,
                "email": model.customer.email,
                "phone": model.customer.phone,
                "document":{
                    "number": model.customer.docNumber,
                    "type": "CPF"
                }
            },
            "shipping":{
                "street": model.address.street,
                "streetNumber": model.address.number,
                "complement": model.address.complement,
                "zipCode": model.address.zipcode,
                "neighborhood": "CENTRO",
                "city": model.address.city,
                "state": model.address.state,
                "country": "BR"
            }
        }

        if model.paymentMethod == "credit_card":
            data["installments"] = model.installments
            data["paymentMethod"] = 'CARD'
            data["card"] = {
                "number": model.card.encryptedNumber,
                "holderName": model.card.holderName,
                "expirationMonth": int(model.card.expMonth),
                "expirationYear": int( model.card.expYear),
                "cvv": model.card.cvv
            }
        elif model.paymentMethod == "boleto":
            data["paymentMethod"] = 'BOLETO'
            data["boleto"] = {
                "expireInDays":45
            }
        elif model.paymentMethod == "pix":
            data["paymentMethod"] = 'PIX'
            data["pix"] = {
                "expireInDays":45
            }

        for item in model.items:
            data["items"].append({
                "title": item.get('title'),
                "unitPrice": int(item.get('unitPrice') * 100),
                "quantity": item.get('quantity'),
                "externalRef": item.get('externalRef')
            })
        print(data)
        return data
