from re import M
from payment.models import Payment
from django.conf import settings
from datetime import timedelta
from django.utils import timezone
from unidecode import unidecode

class PagBankFormatter:
    @staticmethod
    def transform_to_format(model: Payment) -> dict:
        pagbank_data = {}

        if model.paymentMethod == 'credit_card':
            pagbank_data = {
                'reference_id': str(model.id),
                'customer': {
                    'name': model.customer.name,
                    'email': model.customer.email,
                    'tax_id': model.customer.docNumber,
                    'phones': [
                        {
                            'country': '55',
                            'area': str(model.customer.phone)[:2],
                            'number': str(model.customer.phone)[2:],
                            'type': 'MOBILE'
                        }
                    ]
                },
                'items': [
                    {
                        'reference_id': '1',
                        'name': model.items[0].get("title"),
                        'quantity': 1,
                        'unit_amount': int(model.amount * 100)
                    }
                ],
                'charges': [
                    {
                        'reference_id': f'cob_{model.id}',
                        'description': model.items[0].get("title"),
                        'amount': {
                            'value': int(model.amount * 100),
                            'currency': 'BRL'
                        },
                        'payment_method': {
                            'type': 'CREDIT_CARD',
                            'installments': model.installments,
                            'capture': True,
                            'card': {
                                'number': model.card.encryptedNumber,
                                'exp_month': int(model.card.expMonth),
                                'exp_year': int(f"20{str(model.card.expYear)[-2:]}"),
                                'security_code': model.card.cvv,
                                'store': False,
                                'holder': {
                                    'name': unidecode(model.card.holderName),
                                    'tax_id': model.customer.docNumber
                                }
                            }
                        }
                    }
                ],
                'notification_urls': [
                    settings.BACKEND_URL + '/api/webhook/pagbank/'
                ]
            }
        elif model.paymentMethod == 'pix':
            pagbank_data = {
                'reference_id': str(model.id),
                'customer': {
                    'name': model.customer.name,
                    'email': model.customer.email,
                    'tax_id': model.customer.docNumber,
                    'phones': [
                        {
                            'country': '55',
                            'area': str(model.customer.phone)[:2],
                            'number': str(model.customer.phone)[2:],
                            'type': 'MOBILE'
                        }
                    ]
                },
                'items': [
                    {
                        'reference_id': '1',
                        'name': model.items[0].get("title"),
                        'quantity': 1,
                        'unit_amount': int(model.amount * 100)
                    }
                ],
                'qr_codes': [
                    {
                        'amount': {
                            'value': int(model.amount * 100)
                        }
                    }
                ],
                'notification_urls': [
                    settings.BACKEND_URL + '/api/webhook/pagbank/'
                ]
            }
        elif model.paymentMethod == 'boleto':
            pagbank_data = {
                'reference_id': str(model.id),
                'customer': {
                    'name': model.customer.name,
                    'email': model.customer.email,
                    'tax_id': model.customer.docNumber,
                    'phones': [
                        {
                            'country': '55',
                            'area': str(model.customer.phone)[:2],
                            'number': str(model.customer.phone)[2:],
                            'type': 'MOBILE'
                        }
                    ]
                },
                'items': [
                    {
                        'reference_id': '1',
                        'name': model.items[0].get("title"),
                        'quantity': 1,
                        'unit_amount': int(model.amount * 100)
                    }
                ],
                'charges': [
                    {
                        'reference_id': f'cob_{model.id}',
                        'description': model.items[0].get("title"),
                        'amount': {
                            'value': int(model.amount * 100),
                            'currency': 'BRL'
                        },
                        'payment_method': {
                            'type': 'BOLETO',
                            'boleto': {
                                'due_date': (timezone.now() + timedelta(days=365)).strftime("%Y-%m-%d"),
                                'holder': {
                                    'name': unidecode(model.customer.name),
                                    'tax_id': model.customer.docNumber,
                                    'email': model.customer.email,
                                    'address': {
                                        'street': unidecode(f"{getattr(model.address, 'street', 'Rua de exemplo')}"),
                                        'number': getattr(model.address, 'number', '0'),
                                        'locality': unidecode(getattr(model.address, 'neighborhood', 'Centro')),
                                        'city': unidecode(getattr(model.address, 'city', 'Brasília')),
                                        'region': unidecode(getattr(model.address, 'state', 'DF')),
                                        'region_code': unidecode(getattr(model.address, 'state', 'DF')),
                                        'country': 'BRA',
                                        'postal_code': getattr(model.address, 'zipcode', '********'),
                                    }
                                }
                            }
                        }
                    }
                ],
                'notification_urls': [
                    settings.BACKEND_URL + '/api/webhook/pagbank/'
                ]
            }

        return pagbank_data
