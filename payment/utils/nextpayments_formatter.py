from numbers import Number
from re import M
from payment.models import Payment
from django.conf import settings
from datetime import timedelta
from django.utils import timezone
from unidecode import unidecode

class NextPaymentsFormatter:
    @staticmethod
    def transform_to_format(model: Payment) -> dict:
        nextpayments_data = {}

        if model.paymentMethod == 'credit_card':
            nextpayments_data = {
                "amount": float(format(model.amount, '.2f')),
                "externalId": str(model.id),
                "cardNumber": model.card.encryptedNumber,
                "cardholderName": unidecode(model.card.holderName),
                "cardExpirationDateYymm": model.card.expYear + model.card.expMonth,
                "cvv": model.card.cvv,
                "tokenId": "",
                "purchaseInfo": {
                    "billTo": {
                        "address1": unidecode(f"{getattr(model.address, 'street', 'Rua de exemplo')}, {getattr(model.address, 'number', '0')}"),
                        "address2": "-",
                        "administrativeArea": unidecode(getattr(model.address, 'neighborhood', 'Centro')),
                        "countryCode": "BRA",
                        "city": unidecode(getattr(model.address, 'city', 'Brasília')),
                        "firstName": model.customer.name,
                        "lastName": "-",
                        "phoneNumber": str(model.customer.phone),
                        "postalCode": getattr(model.address, 'zipcode', '70000000')
                    },
                    "email": model.customer.email
                }
            }
        elif model.paymentMethod == 'pix':
            nextpayments_data = {
                "externalId": str(model.id),
                "amount": float(format(model.amount, '.2f')),
                "fixedAmount": True,
                "description": model.items[0].get("title"),
                "customer": {
                    "name": unidecode(model.customer.name),
                    "document": model.customer.docNumber,
                    "email": model.customer.email,
                    "mobilePhone": str(model.customer.phone)
                }
            }

        return nextpayments_data

    @staticmethod
    def transform_to_cashout_format(withdrawal) -> dict:
        nextpayments_data = {
            "id": str(withdrawal.idempotencyId.hex),
            "amount": format(withdrawal.amountReceived, '.2f'),
            "description": f"Saque ID: {withdrawal.id}",
            "remittanceInformation": f"Saque ID: {withdrawal.id}",
            "destinatary": {
                "dictKey": withdrawal.pixKey
            },
            "customer": {
                "name": unidecode(withdrawal.user.username),
                "document": withdrawal.user.docNumber,
                "email": withdrawal.user.email,
                "mobilePhone": str(withdrawal.user.cellphone)
            }
        }
        return nextpayments_data
