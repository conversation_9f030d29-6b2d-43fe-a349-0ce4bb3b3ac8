from payment.models import Payment
from django.utils import timezone
from django.conf import settings
from unidecode import unidecode
from datetime import timedelta

class MercadoPagoFormatter:
    @staticmethod
    def transform_to_format(model: Payment) -> dict:
        mercadopago_data = {}

        if model.paymentMethod == "credit_card":
            mercadopago_data = {
                "payment_method_id": model.card.get_brand_method(),
                "description": model.items[0].get("title", "Compra"),
                "installments": model.installments,
                "token": str(model.card.externalToken),
                "forward_data": {
                  "sub_merchant": {
                    "sub_merchant_id": model.user.id,
                    "mcc": "8299",
                    "country": "BRA",
                    "address_door_number": model.user.number,
                    "zip": model.user.cep,
                    "document_number": model.user.cnpj or model.user.cpf,
                    "city": model.user.city,
                    "address_street": model.user.address,
                    "legal_name": model.user.companyLegalName,
                    "region_code_iso": "BR-"+model.user.state,
                    "region_code": "BR",
                    "document_type": "CNPJ" if model.user.cnpj else "CPF",
                    "phone": model.user.cellphone,
                    "url": "https://cakto.com.br"
                  }
                }
            }
            # TODO - CICERO: DESCOMENTAR QUANDO A FLAG SUBIR
            # if model.user.creditCard3DSRequired:
            #     mercadopago_data["three_d_secure_mode"] = "optional"
        elif model.paymentMethod == "pix":
            dueDate = timezone.now() + timedelta(days=1)
            mercadopago_data = {
                "payment_method_id": "pix",
                "date_of_expiration": dueDate.isoformat(timespec='milliseconds').replace('000+', '000-04:')
            }
        elif model.paymentMethod == "boleto":
            mercadopago_data = {
                "payment_method_id": "bolbradesco",
                "description": model.items[0].get("title", "Compra"),
            }

        mercadopago_data["transaction_amount"] = float(model.amount)

        mercadopago_data["payer"] = {
            "email": model.customer.email,
            "first_name": model.customer.name.split(" ", 1)[0],
            "last_name": model.customer.name.split(" ", 1)[1] if " " in model.customer.name else "",
            "identification": {
                "type": "CPF" if model.customer.docType == "cpf" else "CNPJ",
                "number": model.customer.docNumber
            },
            "address": {
                "zip_code": getattr(model.address, "zipcode", "70000000"),
                "street_name": unidecode(getattr(model.address, "street", "Avenida do Contorno")),
                "street_number": getattr(model.address, "number", "0"),
                "neighborhood": unidecode(getattr(model.address, "neighborhood", "Centro")),
                "city": unidecode(getattr(model.address, "city", "Brasília")),
                "federal_unit": getattr(model.address, "state", "DF")
            }
        }
        mercadopago_data["additional_info"] = {
            "items": [
                {
                    "id": 1,
                    "title": item.get("title", None),
                    "unit_price": float(model.amount),
                    "quantity": 1
                }
                for item in model.items
            ][:1]
        }
        mercadopago_data["external_reference"] = str(model.id)
        mercadopago_data["notification_url"] = settings.BACKEND_URL + "/api/webhook/mercadopago/"

        return mercadopago_data

    @staticmethod
    def transform_to_credit_card(model: Payment) -> dict:
        return {
            "site_id": "MLB",
            "card_number": model.card.encryptedNumber.replace(' ', ''),
            "expiration_year": f'20{str(model.card.expYear)[-2:]}',
            "expiration_month": model.card.expMonth,
            "security_code": model.card.cvv,
            "cardholder": {
                "identification": {
                    "type": "CPF" if model.customer.docType == "cpf" else "CNPJ",
                    "number": model.customer.docNumber
                },
                "name": model.card.holderName
            }
        }

    @staticmethod
    def transform_to_refund_format(model: Payment) -> dict:
        mercadopago_data = {
            "amount": float(model.amount)
        }

        return mercadopago_data
