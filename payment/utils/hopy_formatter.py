from payment.models import Payment
from datetime import datetime, timedelta
from . import filter_common_characters, check_phone_arkama, distribute_amount_evenly
from customer.models import Customer
from payment.utils import identify_pix_key_type
from django.utils import timezone
from django.conf import settings
import os
import re

class FirebankingFormatter:
    @staticmethod
    def transform_to_firebanking_format(model: Payment, card={}) -> dict:
        # Get the required fields
        fire_data = {
            'type': model.paymentMethod.upper(),
            'value': float(model.amount),
        }

        # Customer data
        fire_data['payer'] = {
            'fullName': model.customer.name,
            'document': model.customer.docNumber,
        }
        fire_data['transaction'] = {
            'value': float(model.amount),
            'dueDate': (timezone.now() + timedelta(days=365)).strftime("%Y-%m-%d"),
            'externalId': str(model.id),
        }

        return fire_data

    @staticmethod
    def transform_to_firebanking_cashout_format(withdraw) -> dict:
        keyType = identify_pix_key_type(withdraw.user.pixKey)
        if keyType == 'evp':
            keyType = 'RANDOM_KEY'
        elif keyType == 'cpf' or 'cnpj':
            keyType = 'DOCUMENT'
        elif keyType == 'email':
            keyType = 'EMAIL'
        elif keyType == 'phone':
            keyType = 'PHONE'

        firebanking_data = {
            "type":"PIX",
            "value":float(withdraw.amountReceived),
            "details":{
                "key":str(withdraw.user.pixKey),
                "keyType":keyType,
                "name": withdraw.user.get_full_name(),
                "document": withdraw.user.cnpj or withdraw.user.cpf,
            },
            "externalId":str(withdraw.id),
        }

        return firebanking_data

class AbmexFormatter:
    @staticmethod
    def transform_to_abmex_format(model: Payment, card={}) -> dict:
        # Get the required fields
        hopysplit_data = {
            'payment_method': model.paymentMethod,
        }

        if model.paymentMethod == 'credit_card':
            hopysplit_data['credit_card'] = {
                "number": model.card.encryptedNumber,
                "verification_value": model.card.cvv,
                "first_name": model.customer.name.split(' ')[0] if len(model.customer.name.split(' ')) > 1 else model.customer.name,
                "last_name": model.customer.name.split(' ')[1] if len(model.customer.name.split(' ')) > 1 else '',
                "month": str(model.card.expMonth),
                "year": str(model.card.expYear),
            }

        # Customer data
        hopysplit_data['customer'] = {
            'name': model.customer.name,
            'email': model.customer.email,
            'phone_prefix': model.customer.phone[:2],
            'phone': model.customer.phone[2:],
            'document': model.customer.docNumber
        }

        # Items data
        items = []
        for item in model.items:
            item_data = {
                'description': item.get('title', None),
                'price': int(model.amount * 100),
                'quantity': 1,
            }
            items.append(item_data)
            break
        hopysplit_data['items'] = items
        hopysplit_data['webhook'] = settings.BACKEND_URL + '/api/webhook/abmex/'
        return hopysplit_data

class HopyFormatter:
    @staticmethod
    def transform_to_hopysplit_format(model: Payment, card={}) -> dict:
        # Get the required fields
        hopysplit_data = {
            'amount': int(model.amount * 100),
            'paymentMethod': model.paymentMethod,
            'postbackUrl': settings.BACKEND_URL + '/api/webhook/hopypay/',
        }
        # Card data
        if model.paymentMethod == 'credit_card':
            expYear = model.card.expYear[2:]
            hopysplit_data['card'] = {
                'holderName': model.card.holderName.replace(' ', ''),
                'number': model.card.encryptedNumber,
                'expirationMonth': int(model.card.expMonth),
                'expirationYear': int(f"20{expYear}"),
                'cvv': model.card.cvv,
            }
        # Set the installments
        if model.installments:
            hopysplit_data['installments'] = model.installments

        customer_hopysplit_document = {
            'type': model.customer.docType,
            'number': model.customer.docNumber,
        }

        # Customer data
        hopysplit_data['customer'] = {
            # 'id': model.customer.id,
            'name': model.customer.name,
            'email': model.customer.email,
            'document': customer_hopysplit_document,
            'phone': model.customer.phone,
            # 'externalRef': 'model.customer.externalRef',
        }

        # Pix data
        if model.paymentMethod == 'pix':
            hopysplit_data['pix'] = {'expiresInDays': model.expiresInDays}

        # Bill data
        if model.paymentMethod == 'boleto':
            hopysplit_data['boleto'] = {'expiresInDays': model.expiresInDays}

        # Items data
        items = []
        for item in model.items:
            item_data = {
                'title': item.get('title', None),
                'unitPrice': int(item.get('unitPrice',0) * 100),
                'quantity': item.get('quantity', 1),
                'externalRef': item.get('externalRef', None),
                'tangible': item.get('tangible', False),
            }
            items.append(item_data)
        hopysplit_data['items'] = items
        return hopysplit_data


class OwemFormatter:
    @staticmethod
    def transform_to_owempay_format(model: Payment, card={}) -> dict:
        # Get the required fields
        owempay_data = {
            'amount': int(model.amount * 100),
            'paymentMethod': model.paymentMethod,
            'blockSms': True,
            'blockEmail': True,
            'shopName': model.user.commercialName,
            'shopUrl': model.checkoutUrl or model.user.site,
            'subsellerCep': model.user.cep,
            'subsellerCnpj': model.user.cnpj,
            'subsellerName': model.user.get_full_name(),
            'subsellerId': model.user.id,
            'postbackUrl': settings.BACKEND_URL + '/api/webhook/owempay/',
            'origin': settings.OWEMPAY_ORIGIN or 'OTHER'
        }
        # Card data
        if model.paymentMethod == 'credit_card':
            owempay_data['card'] = {
                'holderName': card.holderName,
                'number': card.encryptedNumber.replace(' ', ''),
                'expirationMonth': card.expMonth,
                'expirationYear': card.expYear,
                'cvv': card.cvv,
            }

        # Set the installments
        if model.installments:
            owempay_data['installments'] = model.installments

        customer_owem = {
            'type': model.customer.docType,
            'number': model.customer.docNumber,
        }

        # Customer data
        owempay_data['customer'] = {
            'name': model.customer.name,
            'email': model.customer.email,
            'document': customer_owem,
            'phone': model.customer.phone,
        }

        owempay_data['shipping'] = {
            'fee': 0,
            'address': {
                "street": model.address.street if model.address.street else 'Rua Desconhecida',
                "streetNumber": model.address.number if model.address.number else '0',
                "complement":model.address.complement if model.address.complement else 'Sem complemento',
                "zipCode":model.address.zipcode if model.address.zipcode else '********',
                "neighborhood":model.address.neighborhood if model.address.neighborhood else 'Bairro Desconhecido',
                "city":model.address.city if model.address.city else 'Cidade Desconhecida',
                "state":model.address.state if model.address.state else 'XX',
            }
        }

        # Items data
        items = []
        for item in model.items:
            item_data = {
                'title': item.get('title', None),
                'unitPrice': int(item.get('unitPrice',0) * 100),
                'quantity': item.get('quantity', 1),
                'externalRef': item.get('externalRef', None),
                'tangible': item.get('tangible', False),
            }
            items.append(item_data)
        owempay_data['items'] = items
        return owempay_data

class PayupFormatter:
    @staticmethod
    def transform_to_payup_format(model: Payment, card={}) -> dict:
        # Get the required fields
        payup_data = {
            'amount': float(model.amount),
            'paymentMethod': "CARD",
            'pan': card.encryptedNumber.replace(' ', ''),
            'cardHolderName': card.holderName,
            'expiryMonth': card.expMonth,
            'expiryYear': card.expYear,
            'securityCode': card.cvv,
        }

        payup_data['saleDetails'] = {
            'referenceId': str(model.id)[:20],
            'itemDescription': filter_common_characters(model.items[0].get('title', ''))[:200],
            'softDescription': os.getenv('COMPANY_DESCRIPTOR', '') + filter_common_characters(model.items[0].get('title', 'COMPRA'))[:20],
            'buyerDocType': model.customer.docType.upper(),
            'buyerDocNumber': model.customer.docNumber,
            'buyerName': model.customer.name,
            'buyerMail': model.customer.email,
        }

        # Set the installments
        if model.installments:
            payup_data['installmentNumber'] = model.installments

        return payup_data

class PixcredFormatter:
    @staticmethod
    def transform_to_pixcred_format(model: Payment, card={}) -> dict:
        pixcred_data = {
            'value': float(model.amount),
            'url_notify': settings.BACKEND_URL + '/api/webhook/pixcred/',
            'order_id': str(model.id),
            'payer_name': model.customer.name,
            'payer_doc': model.customer.docNumber,
            'expiration_time': 86400
        }

        return pixcred_data

    @staticmethod
    def transform_to_pixcred_cashout_format(withdraw) -> dict:
        pixcred_data = {
            'pixKey': withdraw.pixKey,
            'value': str(withdraw.amountReceived),
            # 'order_id': str(withdraw.id),
            'document': withdraw.user.cnpj or withdraw.user.cpf,
            'url_notify': settings.BACKEND_URL + '/api/webhook/pixcred/',
        }

        return pixcred_data

class TransfeeraFormatter:
    @staticmethod
    def transform_to_transfeera_format(model: Payment, card={}) -> dict:
        # Get the required fields
        transfeera_data = {
            'amount': model.amount,
            'payment_methods': [model.paymentMethod],
            'due_date': (timezone.now() + timedelta(days=365)).isoformat(),
            'expiration_date': (timezone.now() + timedelta(days=365)).strftime("%Y-%m-%d"),
        }

        # Customer data
        transfeera_data['payer'] = {
            'name': model.customer.name,
            'tax_id': model.customer.docNumber,
        }
        print(transfeera_data)
        return transfeera_data

class SaqjaFormatter:
    @staticmethod
    def transform_to_saqja_format(model: Payment, card={}) -> dict:
        if model.paymentMethod == 'pix':
            # Get the required fields
            saqja_data = {
                'valor': model.amount,
                'telefone': model.customer.phone,
                'cpf': model.customer.docNumber,
                'nomeCliente': model.customer.name
            }
        elif model.paymentMethod == 'credit_card':
            # Get the required fields
            saqja_data = {
              "amount": model.amount,
              "chargeDay": timezone.now().strftime("%Y-%m-%d"),
              'creditCardDataPayment': {},
              # "creditCardLinkPayment": {
              #   "customer": {
              #     "name": "string",
              #     "documentNumber": "string",
              #     "phoneNumber": "string"
              #   }
              # },
              # "additionalInfo": "string"
            }

            saqja_data['creditCardDataPayment']['customer'] = {
                "name": model.customer.name,
                "documentNumber": model.customer.docNumber,
                "phoneNumber": model.customer.phone
            }

            saqja_data['creditCardDataPayment']['creditCard'] = {
                "number": card.encryptedNumber.replace(' ', ''),
                "holder": card.holderName,
                "expiresAt": card.expMonth + '/' + card.expYear,
                "cvv": card.cvv
            }
        elif model.paymentMethod == 'boleto':
            # Get the required fields
            saqja_data = {
              "amount": model.amount,
              "dueDate": (timezone.now() + timedelta(days=365)).isoformat(),
              "document": model.customer.docNumber,
              "name": model.customer.name,
            }
        return saqja_data

class AsaasFormatter:
    @staticmethod
    def transform_to_asaas_format(model: Payment, customerId, card={}) -> dict:
        asaas_data = {}
        if model.paymentMethod == 'credit_card':
            billingType = 'CREDIT_CARD'

            # Get the required fields
            asaas_data = {
                'value': float(model.amount),
                'dueDate': (timezone.now() + timedelta(days=365)).isoformat(),
                'customer': customerId,
                'name': model.customer.name,
                'description': model.items[0].get('title', 'COMPRA'),
                'billingType': billingType,
            }

            if model.installments > 1:
                asaas_data['installmentCount'] = model.installments
                asaas_data['installmentValue'] = float(model.amount) / model.installments

        elif model.paymentMethod == 'boleto':
            billingType = 'BOLETO'

            # Get the required fields
            asaas_data = {
                'value': float(model.amount),
                'dueDate': (timezone.now() + timedelta(days=40)).isoformat(),
                'customer': customerId,
                'name': model.customer.name,
                'description': model.items[0].get('title', 'COMPRA'),
                'billingType': billingType
            }

        elif model.paymentMethod == 'pix':
            billingType = 'PIX'

            # Get the required fields
            asaas_data = {
                'value': float(model.amount),
                'dueDate': (timezone.now() + timedelta(days=365)).isoformat(),
                'customer': customerId,
                'name': model.customer.name,
                'description': model.items[0].get('title', 'COMPRA'),
                'billingType': billingType,
                'externalReference': str(model.id)
            }
        if model.user.pagarmeAccountId:
            if model.user.pagarmeStatus == 'active':
                if model.acquirer.keys.get('walletid'):
                    asaas_data['split'] = []
                    for split in model.splits.all():
                        if split.user.asaasWalletId:
                            item = {
                                'walletId':split.user.asaasWalletId,
                            }
                            if model.installments > 1:
                                item['totalFixedValue'] = float(split.amount)
                            else:
                                item['fixedValue'] = float(split.amount)

                            asaas_data['split'].append(item)
        return asaas_data

    @staticmethod
    def transform_to_asaas_customer(model: Customer) -> dict:
        # Get the required fields
        asaas_data = {
            'name': model.name,
            'cpfCnpj': model.docNumber,
            'notificationDisabled':True
        }

        return asaas_data

    @staticmethod
    def transform_to_asaas_cashout_format(withdraw) -> dict:
        pixAddressKeyType = identify_pix_key_type(withdraw.pixKey)
        asaas_data = {
            'value': float(withdraw.amountReceived),
            'operationType': 'PIX',
            'pixAddressKey': withdraw.pixKey,
            'pixAddressKeyType': pixAddressKeyType.upper(),
            'description': f'ID: {withdraw.id}',
        }

        return asaas_data

    @staticmethod
    def transform_to_asaas_subaccount_format(user) -> dict:
        asaas_data = {
            'name': user.get_full_name(),
            'email': user.generate_email(),
            'cpfCnpj': user.cnpj or user.cpf,
            'mobilePhone': user.cellphone,
            'incomeValue':user.averageRevenue,
            'address':user.address,
            'addressNumber':user.number,
            'province':user.state,
            'city':user.city,
            'postalCode':user.cep,
            "webhooks": [
                {
                  	"name": "Webhook para cobranças",
                    "url": "https://api.pay.cakto.com.br/api/webhook/asaas/",
                    "email": "<EMAIL>",
                  	"sendType": "SEQUENTIALLY",
                    "interrupted": False,
                    "enabled": True,
                    "apiVersion": 3,
                    "authToken": "ztWBAbqXNxjSWFJcEvjy4mEEkbzMetNLLyN8BF7anFp2x56P29W5oBYSy7WHYonG",
                    "events": [
                        "PAYMENT_AUTHORIZED",
                        "PAYMENT_APPROVED_BY_RISK_ANALYSIS",
                        "PAYMENT_CREATED", "PAYMENT_CONFIRMED",
                        "PAYMENT_DELETED", "PAYMENT_REFUNDED",
                        "PAYMENT_REFUND_DENIED",
                        "PAYMENT_CHARGEBACK_REQUESTED",
                        "PAYMENT_AWAITING_CHARGEBACK_REVERSAL",
                        "PAYMENT_DUNNING_REQUESTED",
                        "PAYMENT_PARTIALLY_REFUNDED",
                        "PAYMENT_AWAITING_RISK_ANALYSIS",
                        "PAYMENT_REPROVED_BY_RISK_ANALYSIS",
                        "PAYMENT_RECEIVED",
                        "PAYMENT_RESTORED",
                        "PAYMENT_REFUND_IN_PROGRESS",
                        "PAYMENT_RECEIVED_IN_CASH_UNDONE",
                        "PAYMENT_CHARGEBACK_DISPUTE",
                        "PAYMENT_DUNNING_RECEIVED",
                        'ACCOUNT_STATUS_BANK_ACCOUNT_INFO_APPROVED',
                        'ACCOUNT_STATUS_BANK_ACCOUNT_INFO_AWAITING_APPROVAL',
                        'ACCOUNT_STATUS_BANK_ACCOUNT_INFO_PENDING',
                        'ACCOUNT_STATUS_BANK_ACCOUNT_INFO_REJECTED',
                        'ACCOUNT_STATUS_COMMERCIAL_INFO_APPROVED',
                        'ACCOUNT_STATUS_COMMERCIAL_INFO_AWAITING_APPROVAL',
                        'ACCOUNT_STATUS_COMMERCIAL_INFO_PENDING',
                        'ACCOUNT_STATUS_COMMERCIAL_INFO_REJECTED',
                        'ACCOUNT_STATUS_DOCUMENT_APPROVED',
                        'ACCOUNT_STATUS_DOCUMENT_AWAITING_APPROVAL',
                        'ACCOUNT_STATUS_DOCUMENT_PENDING',
                        'ACCOUNT_STATUS_DOCUMENT_REJECTED',
                        'ACCOUNT_STATUS_GENERAL_APPROVAL_APPROVED',
                        'ACCOUNT_STATUS_GENERAL_APPROVAL_AWAITING_APPROVAL',
                        'ACCOUNT_STATUS_GENERAL_APPROVAL_PENDING',
                        'ACCOUNT_STATUS_GENERAL_APPROVAL_REJECTED']
                }
            ]

        }
        if not user.cnpj and user.cpf:
            asaas_data['birthDate'] = user.birthDate.strftime('%Y-%m-%d')
        elif user.cnpj:
            asaas_data['companyType'] = user.companyType

        return asaas_data

class ArkamaFormatter:
    @staticmethod
    def transform_to_arkama_format(model: Payment) -> dict:

        arkama_data = {
            'value':float(model.amount),
            'paymentMethod': model.paymentMethod,
            'customer': {
                'name': model.customer.name.encode('iso-8859-1').decode('iso-8859-1'),
                'email': model.customer.email,
                'document': model.customer.docNumber,
                'cellphone':check_phone_arkama(model.customer.phone),
            },
            'shipping':{
                'address':{
                    'cep': model.address.zipcode or '********',
                    'street': model.address.street or 'Não informado',
                    'neighborhood': model.address.neighborhood or 'Sem Bairro',
                    'number': model.address.number or '0',
                    'city': model.address.city or 'Cidade não informada',
                    'state': model.address.state or 'XX',
                    'complement': model.address.complement or 'Sem complemento'
                }
            },
            'ip':"127.0.0.1"
        }

        items = []
        for item in model.items:
            item_data = {
                'title': item.get('title', None),
                'unitPrice': item.get('unitPrice',0),
                'quantity': item.get('quantity', 1),
                'isDigital': False
            }
            items.append(item_data)
        arkama_data['items'] = items
        return arkama_data
