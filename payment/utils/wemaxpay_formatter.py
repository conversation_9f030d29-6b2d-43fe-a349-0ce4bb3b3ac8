from payment.models import Payment

class WemaxPayFormatter:
    @staticmethod
    def transform_to_format(model: Payment) -> dict:
        items = [{
            'description': model.items[0].get('title'),
            'quantity': 1,
            'price_cents': int(model.amount*100)
        }]
        return {
            'number': model.card.encryptedNumber,
            'verification_value': model.card.cvv,
            'first_name': model.customer.name.split(' ')[0] if len(model.customer.name.split(' ')) > 1 else model.customer.name,
            'last_name': model.customer.name.split(' ')[-1] if len(model.customer.name.split(' ')) > 1 else '',
            'month': model.card.expMonth,
            'year': model.card.expYear,
            'email': model.customer.email,
            'items': items,
            'order_id': str(model.id),
            'months': model.installments,
            'seller':{
                'name': model.user.get_full_name(),
                'cpf_cnpj': model.user.cnpj or model.user.cpf,
                'phone_prefix': model.user.phone[:2],
                "physical_products": True,
                "business_type": model.user.business_type,
                "address": {
                  "zip_code": model.user.cep,
                  "street": model.user.address,
                  "district": model.user.neighborhood,
                  "city": model.user.city,
                  "state": model.user.state,
                  "number": model.user.number,
                  "complement": model.user.complement
                },
                "bank": "Bradesco",
                "bank_ag": "1234-3",
                "bank_cc": "1234567-8"
            },
            'payer': {
                'cpf_cnpj': model.customer.docNumber,
                'phone_prefix': model.customer.phone[:2],
                'phone_number': model.customer.phone[2:],
                'email': model.customer.email,
                'address': {
                    'street': model.address.street,
                    'number': model.address.number,
                    'city': model.address.city,
                    'state': model.address.state,
                    'country':'BR',
                    'zip_code': model.address.zipcode
                }
            }
        }

    @staticmethod
    def validate_card(model: Payment) -> dict:
        return {
          "number": model.card.encryptedNumber,
          "verification_value": model.card.cvv,
          "first_name": model.customer.name.split(' ')[0] if len(model.customer.name.split(' ')) > 1 else model.customer.name,
          "last_name": model.customer.name.split(' ')[-1] if len(model.customer.name.split(' ')) > 1 else '',
          "month": model.card.expMonth,
          "year": model.card.expYear,
        }
