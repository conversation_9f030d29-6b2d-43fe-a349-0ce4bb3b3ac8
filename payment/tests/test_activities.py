from datetime import timedelta
from decimal import Decimal
from unittest.mock import patch, MagicMock

import pytest
from django.utils import timezone

from customer.models import Customer
from gateway.models import Fee
from payment.models import Payment, Subscription
from payment.activities import schedule_retry, schedule_next_recurrent_payment
from payment.strategies.card_strategy import CardStrategy
from user.models import User


@pytest.mark.django_db
@patch("payment.activities.schedule_payment")
def test_schedule_retry_successful_retry(
    mock_schedule_payment,
    regular_user: User,
    regular_customer: Customer,
    scheduled_payment: Payment,
    refused_payment: Payment,
    regular_user_with_subscription: Subscription,
):
    assert not refused_payment.retry_scheduled
    assert not refused_payment.retry_count
    assert refused_payment.status == "refused"

    result = schedule_retry(refused_payment, regular_user_with_subscription)

    refused_payment.refresh_from_db()
    regular_user_with_subscription.refresh_from_db()

    assert refused_payment.retry_count == 1
    assert refused_payment.status == "scheduled"
    assert refused_payment.retry_scheduled is True
    assert refused_payment.due_date.date() == (timezone.now() + timedelta(days=regular_user_with_subscription.retry_interval)).date()
    assert result == f"Payment {refused_payment.id} failed, retrying with new due date {refused_payment.due_date} (attempt {refused_payment.retry_count})"

    mock_schedule_payment.assert_called_once_with(refused_payment, fire_webhook=False)


@pytest.mark.django_db
@patch("payment.activities.schedule_payment")
def test_schedule_retry_max_retries_reached(
    mock_schedule_payment,
    regular_user: User,
    regular_customer: Customer,
    scheduled_payment: Payment,
    regular_user_with_subscription: Subscription,
    scheduled_payment_with_max_retries_reached: Payment,
):
    subscription = regular_user_with_subscription

    assert scheduled_payment.retry_scheduled
    assert scheduled_payment.retry_count == subscription.max_retries

    assert subscription.status == Subscription.STATUS_ACTIVE

    result = schedule_retry(scheduled_payment, subscription)

    scheduled_payment.refresh_from_db()
    subscription.refresh_from_db()

    assert scheduled_payment.status == 'failed'
    assert subscription.status == Subscription.STATUS_CANCELED
    assert result == f"Payment {scheduled_payment.id} failed permanently after {scheduled_payment.retry_count} retries"

    mock_schedule_payment.assert_not_called()

@pytest.mark.django_db
@patch('payment.activities.schedule_payment')
def test_schedules_next_payment_and_updates_fields(
    mock_schedule_payment,
    regular_user: User,
    regular_customer: Customer,
    default_fee_for_calculate_interest: Fee,
    subscription_with_active_status_unlimited_recurrences: Subscription,
    first_paid_subscription_payment: Payment,
):
    payment = first_paid_subscription_payment
    subscription = subscription_with_active_status_unlimited_recurrences

    payment_original_amount = payment.amount

    assert Payment.objects.filter(subscription_id=subscription.id, amount=payment.amount, status="paid").count() == 1
    assert payment.installments > 1

    interest = payment.user.getInterest(payment, payment.acquirer.id)
    payment.interest = Decimal(payment.amount) * Decimal(interest / 100)
    payment.amount = Decimal(payment.amount) + payment.interest
    payment.save(skip_hooks=True)

    result = schedule_next_recurrent_payment(payment, subscription)

    assert Payment.objects.filter(subscription_id=subscription.id, amount=payment.amount, status="scheduled").count() == 1

    original_payment = Payment.objects.get(subscription_id=subscription.id, status="paid")
    duplicated_payment = Payment.objects.get(subscription_id=subscription.id, amount=payment.amount, status="scheduled")

    assert duplicated_payment.status == "scheduled"
    assert duplicated_payment.amount == payment_original_amount
    assert duplicated_payment.amount < original_payment.amount

    mock_schedule_payment.assert_called()
    assert "Next payment scheduled for" in result

@pytest.mark.django_db
@patch('payment.activities.schedule_payment')
def test_handles_interest_zero(mock_schedule_payment, payment, subscription):
    payment.interest = 0
    payment.amount = 100
    schedule_next_recurrent_payment(payment, subscription)
    assert payment.amount == 100
    mock_schedule_payment.assert_called()

