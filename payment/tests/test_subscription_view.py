import uuid

import pytest
from rest_framework.response import Response
from rest_framework import status
from django.urls import reverse
from rest_framework.test import APIClient

from customer.models import Card, Customer
from payment.models import Subscription, Payment
from payment.views import SubscriptionUpdateView
from user.models import User


@pytest.mark.django_db
def test_subscription_not_found(
    logged_in_as_regular_user: APIClient,
):
    url = reverse('subscription-update', args=[999])
    response = logged_in_as_regular_user.patch(url, {})
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.data['detail'] == 'Assinatura não encontrada.'


@pytest.mark.django_db
def test_subscription_cancel(
    logged_in_as_regular_user: APIClient,
    regular_user_with_subscription: Subscription,
    scheduled_payments: list[Payment],
):
    subscription = regular_user_with_subscription

    assert subscription.status == Subscription.STATUS_ACTIVE
    assert subscription.payments.count() == 10

    assert all(payment.status == 'scheduled' for payment in scheduled_payments)
    assert all(payment.status == 'scheduled' for payment in subscription.payments.all())

    url = reverse('subscription-update', args=[subscription.id])
    response = logged_in_as_regular_user.patch(url, {'status': Subscription.STATUS_CANCELED})

    subscription.refresh_from_db()
    assert response.status_code == status.HTTP_200_OK
    assert response.data['detail'] == 'Assinatura e pagamentos futuros cancelados com sucesso.'
    assert subscription.status == Subscription.STATUS_CANCELED

    assert all(payment.status == 'canceled' for payment in subscription.payments.all())


@pytest.mark.django_db
def test_update_payment_method(
    logged_in_as_regular_user: APIClient,
    regular_user_with_subscription: Subscription,
    scheduled_payments: list[Payment],
):
    subscription = regular_user_with_subscription

    subscription.status = Subscription.STATUS_ACTIVE

    assert all(payment.paymentMethod == 'pix' for payment in scheduled_payments)
    assert all(payment.paymentMethod == 'pix' for payment in subscription.payments.all())

    url = reverse('subscription-update', args=[subscription.id])
    response = logged_in_as_regular_user.patch(url, {'paymentMethod': 'credit_card'})

    assert response.status_code == status.HTTP_200_OK
    assert response.data['detail'] == 'Assinatura atualizada com sucesso.'

    assert all(payment.paymentMethod == 'credit_card' for payment in subscription.payments.all())


@pytest.mark.django_db
def test_update_card(
    logged_in_as_regular_user: APIClient,
    regular_user_with_subscription: Subscription,
    scheduled_payments: list[Payment],
    regular_customer: Customer,
):
    subscription = regular_user_with_subscription

    subscription.status = Subscription.STATUS_ACTIVE

    assert subscription.card is None
    assert all(payment.card is None for payment in scheduled_payments)
    assert all(payment.card is None for payment in subscription.payments.all())


    new_token = uuid.uuid4()

    new_card = Card.objects.create(
        token=new_token,
        customer=regular_customer,
        number='1234567890123456',
        cvv='123',
        lastDigits='3456',
        expMonth='12',
        expYear='2023',
        brand='visa',
        holderName='John Doe'
    )
    url = reverse('subscription-update', args=[subscription.id])
    response = logged_in_as_regular_user.patch(
        path=url,
        data={
            'paymentMethod': 'credit_card',
            'card': {
                'token': new_card.token,
                'customer': regular_customer.id,
                'number': new_card.encryptedNumber,
                'cvv': new_card.cvv,
                'lastDigits': new_card.lastDigits,
                'expMonth': new_card.expMonth,
                'expYear': new_card.expYear,
                'brand': new_card.brand,
                'holderName': new_card.holderName
            }
        },
        format='json'
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.data['detail'] == 'Assinatura atualizada com sucesso.'

    subscription.refresh_from_db()
    assert subscription.card.token == new_card.token
    assert all(payment.card.token == new_card.token for payment in subscription.payments.all())


@pytest.mark.django_db
def test_invalid_card_token(
    logged_in_as_regular_user: APIClient,
    regular_user_with_subscription: Subscription,
    scheduled_payments: list[Payment],
    regular_customer: Customer,
):
    subscription = regular_user_with_subscription
    invalid_token = uuid.uuid4()
    url = reverse('subscription-update', args=[subscription.id])
    response = logged_in_as_regular_user.patch(
        path=url,
        data={'paymentMethod': 'credit_card', 'card': {'token': invalid_token, 'customer': regular_customer.id,}},
        format='json'
    )
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.data['detail'] == 'Cartão não encontrado.'


@pytest.mark.django_db
def test_nothing_to_update(
    logged_in_as_regular_user: APIClient,
    regular_user_with_subscription: Subscription,
    scheduled_payments: list[Payment],
    regular_customer: Customer,
):
    subscription = regular_user_with_subscription

    url = reverse('subscription-update', args=[subscription.id])
    response = logged_in_as_regular_user.patch(url, {})
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.data['detail'] == 'Nada para atualizar.'

@pytest.mark.django_db
def test_get_subscription_found_for_superuser(
    regular_user: User,
    regular_user_with_subscription: Subscription,
    admin_user: User,
):
    assert regular_user_with_subscription.user == regular_user
    assert not regular_user_with_subscription.user == admin_user

    assert admin_user.is_superuser

    view = SubscriptionUpdateView()
    response = view.get_subscription(regular_user_with_subscription.pk, admin_user)
    assert isinstance(response, Subscription)
    assert response.pk == regular_user_with_subscription.pk

@pytest.mark.django_db
def test_get_subscription_found_for_regular_user(
    regular_user: User,
    regular_user_with_subscription: Subscription,
):
    assert regular_user_with_subscription.user == regular_user
    assert not regular_user_with_subscription.user.is_superuser

    view = SubscriptionUpdateView()
    response = view.get_subscription(regular_user_with_subscription.pk, regular_user)
    assert isinstance(response, Subscription)
    assert response.pk == regular_user_with_subscription.pk

@pytest.mark.django_db
def test_get_subscription_not_found_for_regular_user(
    regular_user: User
):
    assert not Subscription.objects.filter(user=regular_user).exists()
    assert not Subscription.objects.filter(pk=999).exists()

    view = SubscriptionUpdateView()
    response = view.get_subscription(999, regular_user)
    assert isinstance(response, Response)
    assert response.status_code == 404

@pytest.mark.django_db
def test_get_subscription_not_found_for_superuser(
    admin_user: User
):
    assert Subscription.objects.all().exists() is False
    assert not Subscription.objects.filter(pk=999).exists()

    view = SubscriptionUpdateView()
    response = view.get_subscription(999, admin_user)
    assert isinstance(response, Response)
    assert response.status_code == 404
