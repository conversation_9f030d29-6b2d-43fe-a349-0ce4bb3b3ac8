import pytest
from unittest.mock import patch, MagicMock

from customer.models import Customer
from payment.models import Payment, Subscription
from user.models import User


@pytest.mark.django_db
def test_updates_subscription_status_to_active_when_payment_is_paid(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    regular_user_with_inactive_subscription: Subscription,
    pending_payment_with_subscription_pix_payment_method: Payment,
):
    payment = pending_payment_with_subscription_pix_payment_method
    subscription = regular_user_with_inactive_subscription

    assert subscription.status == Subscription.STATUS_INACTIVE
    assert payment.status == "pending"
    assert payment.subscription
    assert payment.subscription == subscription

    payment.status = "paid"
    payment.save(update_fields=["status"])

    subscription.refresh_from_db()
    assert subscription.status == Subscription.STATUS_ACTIVE


@pytest.mark.django_db
def test_does_not_update_subscription_status_if_not_inactive(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    pending_payment_with_subscription_pix_payment_method: Payment,
):
    payment = pending_payment_with_subscription_pix_payment_method
    subscription = regular_user_with_subscription

    assert subscription.status == Subscription.STATUS_ACTIVE
    assert payment.status == "pending"
    assert payment.subscription
    assert payment.subscription == subscription

    payment.status = "paid"
    payment.save(update_fields=["status"])

    subscription.refresh_from_db()
    assert subscription.status == Subscription.STATUS_ACTIVE


@pytest.mark.django_db
def test_does_not_update_when_payment_method_not_pix(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_inactive_subscription: Subscription,
    pending_payment_with_subscription_credit_card_payment_method: Payment,
):
    payment = pending_payment_with_subscription_credit_card_payment_method
    subscription = regular_user_with_inactive_subscription

    assert subscription.status == Subscription.STATUS_INACTIVE
    assert payment.status == "pending"
    assert payment.subscription
    assert payment.subscription == subscription

    payment.status = "paid"
    payment.save(update_fields=["status"])

    subscription.refresh_from_db()
    assert subscription.status == Subscription.STATUS_INACTIVE


@pytest.mark.django_db
def test_executes_bulk_strategy_for_items_with_subscription(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_inactive_subscription: Subscription,
    pending_payment_with_subscription_credit_card_payment_method: Payment,
    pending_payment_with_subscription_items: Payment,
):
    payment = pending_payment_with_subscription_credit_card_payment_method
    subscription = regular_user_with_inactive_subscription

    assert subscription.status == Subscription.STATUS_INACTIVE
    assert payment.status == "pending"
    assert payment.items[0].get('subscription') == subscription.id

    with patch('payment.services.subscription_factory.SubscriptionFactory.get_strategy') as mock_strategy:
        mock_execute = MagicMock()
        mock_strategy.return_value.execute_subscription = mock_execute

        payment.status = 'paid'
        payment.save()

        mock_strategy.assert_called_once_with('bulk')
        mock_execute.assert_called_once_with(payment=payment, subscription=subscription)


@pytest.mark.django_db
def test_does_not_execute_strategy_if_no_subscription_in_items(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_inactive_subscription: Subscription,
    pending_payment_with_subscription_credit_card_payment_method: Payment,
):
    payment = pending_payment_with_subscription_credit_card_payment_method
    subscription = regular_user_with_inactive_subscription

    assert subscription.status == Subscription.STATUS_INACTIVE
    assert payment.status == "pending"
    assert not payment.items[0].get('subscription')

    with patch('payment.services.subscription_factory.SubscriptionFactory.get_strategy') as mock_strategy:
        payment.status = 'paid'
        payment.save()

        mock_strategy.assert_not_called()


@pytest.mark.django_db
def test_handles_exception_during_subscription_update_gracefully(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    regular_user_with_inactive_subscription: Subscription,
    pending_payment_with_subscription_pix_payment_method: Payment,
):
    payment = pending_payment_with_subscription_pix_payment_method
    subscription = regular_user_with_subscription

    assert subscription.status == Subscription.STATUS_INACTIVE
    assert payment.status == "pending"
    assert payment.subscription
    assert payment.subscription == subscription

    with patch('payment.models.Subscription.save', side_effect=Exception("Test Exception")) as mock_save:
        with patch('payment.models.logger.error') as mock_logger:
            payment.status = 'paid'
            payment.save()

            subscription.refresh_from_db()
            assert subscription.status == Subscription.STATUS_INACTIVE

            mock_logger.assert_any_call(
                f"Error updating subscription status in AFTER_UPDATE signal: Test Exception")
