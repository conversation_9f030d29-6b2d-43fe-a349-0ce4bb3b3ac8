from unittest.mock import patch, MagicMock

import pytest
from datetime import datetime, timedelta

from django.test import override_settings
from rest_framework import status
from rest_framework.response import Response
from waffle.testutils import override_switch

from customer.models import Customer, Card
from payment.models import Payment, Subscription
from payment.strategies.subscription.base import SubscriptionStrategy
from django.utils import timezone

from payment.strategies.subscription.bulk import BulkSubscriptionStrategy
from user.models import User


@pytest.mark.django_db
def test_duplicate_payment_creates_new_payment(
    regular_user: User,
    regular_customer: Customer,
    payment: Payment
):
    next_due_date = datetime.now() + timezone.timedelta(days=30)
    new_payment = SubscriptionStrategy.duplicate_payment(next_due_date, payment)

    assert new_payment.id is not None
    assert new_payment.due_date == next_due_date
    assert new_payment.status == 'scheduled'
    assert new_payment.amount == payment.amount
    assert new_payment.user == payment.user
    assert new_payment.paymentMethod == payment.paymentMethod


@pytest.mark.django_db
def test_duplicate_payment_with_amount_override(
    regular_user: User,
    regular_customer: Customer,
    payment: Payment
):
    original_payment_id = payment.id
    original_payment_pk = payment.pk
    original_payment_amount = payment.amount

    next_due_date = datetime.now() + timezone.timedelta(days=30)
    new_amount = 150.0
    new_payment = SubscriptionStrategy.duplicate_payment(next_due_date, payment, new_amount)

    assert new_payment.id is not None
    assert new_payment.id != original_payment_id
    assert new_payment.pk != original_payment_pk
    assert new_payment.due_date == next_due_date
    assert new_payment.status == 'scheduled'
    assert new_payment.amount == new_amount
    assert new_payment.amount != original_payment_amount
    assert new_payment.paymentMethod == payment.paymentMethod


@pytest.mark.django_db
def test_duplicate_payment_without_amount_override(
    regular_user: User,
    regular_customer: Customer,
    payment: Payment
):
    original_payment_due_date = payment.due_date

    next_due_date = datetime.now() + timezone.timedelta(days=30)
    new_payment = SubscriptionStrategy.duplicate_payment(next_due_date, payment)

    assert new_payment.id is not None
    assert new_payment.due_date == next_due_date
    assert new_payment.due_date != original_payment_due_date
    assert new_payment.status == 'scheduled'
    assert new_payment.amount == payment.amount


@patch("payment.strategies.subscription.base.schedule_payment")
@pytest.mark.django_db
def test_schedule_initial_payment_schedules_payment_correctly(
    mock_schedule_payment,
    regular_user: User,
    regular_customer: Customer,
    payment: Payment,
    regular_user_with_subscription: Subscription,
):
    next_due_date = timezone.now() + timezone.timedelta(days=1)

    SubscriptionStrategy.schedule_initial_payment(regular_user_with_subscription, payment, next_due_date)

    payment.refresh_from_db()
    assert payment.status == 'scheduled'
    assert payment.due_date == next_due_date
    assert payment.subscription == regular_user_with_subscription
    mock_schedule_payment.assert_called_once_with(payment)


@patch("payment.strategies.subscription.base.schedule_payment")
@pytest.mark.django_db
def test_schedule_initial_payment_handles_none_next_due_date(
    mock_schedule_payment,
    regular_user: User,
    regular_customer: Customer,
    payment: Payment,
    regular_user_with_subscription: Subscription,
):
    next_due_date = None

    SubscriptionStrategy.schedule_initial_payment(regular_user_with_subscription, payment, next_due_date)
    payment.refresh_from_db()

    assert payment.status == 'scheduled'
    assert payment.due_date is None
    assert payment.subscription == regular_user_with_subscription

    mock_schedule_payment.assert_called_once_with(payment)


@patch("payment.strategies.subscription.base.schedule_payment")
@pytest.mark.django_db
def test_schedule_initial_payment_handles_none_payment(
    mock_schedule_payment,
    regular_user: User,
    regular_customer: Customer,
    payment: Payment,
    regular_user_with_subscription: Subscription,
):
    payment = None
    next_due_date = timezone.now() + timezone.timedelta(days=1)

    with pytest.raises(AttributeError):
        SubscriptionStrategy.schedule_initial_payment(regular_user_with_subscription, payment, next_due_date)

    mock_schedule_payment.assert_not_called()


@patch("payment.strategies.subscription.base.schedule_payment")
@pytest.mark.django_db
def test_schedule_initial_payment_handles_none_subscription(
    mock_schedule_payment,
    regular_user: User,
    regular_customer: Customer,
    payment: Payment,
    regular_user_with_subscription: Subscription,
):
    subscription = None
    next_due_date = timezone.now() + timezone.timedelta(days=1)

    SubscriptionStrategy.schedule_initial_payment(subscription, payment, next_due_date)
    payment.refresh_from_db()

    assert payment.status == 'scheduled'
    assert payment.due_date == next_due_date
    assert payment.subscription is None

    mock_schedule_payment.assert_called_once_with(payment)


@pytest.mark.django_db
def test_create_future_payment_valid(
    regular_user: User,
    regular_customer: Customer,
    payment: Payment,
    regular_user_with_subscription: Subscription,
):
    payment_id = payment.id
    payment_pk = payment.pk

    assert payment.subscription is None

    next_due_date = datetime.now()
    future_payment = SubscriptionStrategy.create_future_payment(regular_user_with_subscription, payment, next_due_date)

    assert future_payment.id is not None
    assert future_payment.pk is not None
    assert future_payment.id != payment_id
    assert future_payment.pk != payment_pk
    assert future_payment.due_date == next_due_date
    assert future_payment.subscription == regular_user_with_subscription
    assert future_payment.amount == payment.amount
    assert future_payment.status == 'scheduled'


@pytest.mark.django_db
def test_handle_failed_payment_cancels_subscription(
    regular_user: User,
    regular_customer: Customer,
    payment_refused: Payment,
    regular_user_with_subscription: Subscription,
):
    response = SubscriptionStrategy.handle_failed_payment(regular_user_with_subscription, payment_refused)

    regular_user_with_subscription.refresh_from_db()
    assert regular_user_with_subscription.status == Subscription.STATUS_CANCELED
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.data["error"] == "Erro ao processar o pagamento inicial."
    assert response.data["reason"] == "Insufficient funds"
    assert response.data["reason"] == payment_refused.reason


@pytest.mark.django_db
def test_handle_failed_payment_with_no_reason(
    regular_user: User,
    regular_customer: Customer,
    payment_refused: Payment,
    regular_user_with_subscription: Subscription,
):
    payment_refused.reason = ""
    response = SubscriptionStrategy.handle_failed_payment(regular_user_with_subscription, payment_refused)

    regular_user_with_subscription.refresh_from_db()
    assert regular_user_with_subscription.status == Subscription.STATUS_CANCELED
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.data["error"] == "Erro ao processar o pagamento inicial."
    assert response.data["reason"] == payment_refused.reason


@pytest.mark.django_db
def test_handle_failed_payment_return_payment_data(
    regular_user: User,
    regular_customer: Customer,
    payment_refused: Payment,
    payment_refused_with_subscription: Payment,
    regular_user_with_subscription: Subscription,
):
    assert regular_user_with_subscription.status == Subscription.STATUS_ACTIVE
    response = SubscriptionStrategy.handle_failed_payment(regular_user_with_subscription, payment_refused)

    regular_user_with_subscription.refresh_from_db()

    assert "failed_payment_data" in response.data
    payment_data_from_response = response.data["failed_payment_data"]

    assert payment_data_from_response['status'] == payment_refused.status
    assert payment_data_from_response['reason'] == payment_refused.reason
    assert payment_data_from_response['amount'] == payment_refused.amount
    assert payment_data_from_response['due_date'] == payment_refused.due_date
    assert payment_data_from_response['id'] == str(payment_refused.id)
    assert payment_data_from_response['subscription']['id'] == regular_user_with_subscription.id
    assert payment_data_from_response['subscription']['status'] == regular_user_with_subscription.status


@pytest.mark.django_db
def test_handle_failed_payment_calls_cancel_subscription_method(
    regular_user: User,
    regular_customer: Customer,
    payment_refused: Payment,
    regular_user_with_subscription: Subscription,
):
    with patch('payment.tasks.handleWebhooks.delay') as mock_webhook, patch('logging.Logger.info') as mock_logger:
        SubscriptionStrategy.handle_failed_payment(regular_user_with_subscription, payment_refused)
        regular_user_with_subscription.refresh_from_db()
        assert regular_user_with_subscription.status == Subscription.STATUS_CANCELED

        assert regular_user_with_subscription.payments.filter(status='scheduled').exists() is False

        # there is one call to the webhook for each scheduled payment and one for the cancellation
        mock_logger.assert_any_call(
            f"Subscription canceled successfully. Total canceled payments: 0, Subscription ID: {regular_user_with_subscription.id}"
        )

        mock_logger.assert_any_call(
            f"Webhook triggered after subscription status change: subscription_id={regular_user_with_subscription.id}, new_status={regular_user_with_subscription.status}"
        )

        mock_webhook.assert_any_call(
            payment=None,
            subscription=regular_user_with_subscription,
            event_type='subscription.subscription_status_changed'
        )


def test_validate_subscription_fields_valid_data():
    request_data = {
        'recurrence_period': 30,
        'quantity_recurrences': 10,
        'trial_days': 0,
        'max_retries': 3,
        'retry_interval': 1
    }
    result = SubscriptionStrategy.validate_subscription_fields(request_data)
    assert result == (30, 10, 0, 3, 1)


def test_validate_subscription_fields_missing_fields():
    request_data = {
        'trial_days': 0,
        'max_retries': 3,
        'retry_interval': 1
    }
    response = SubscriptionStrategy.validate_subscription_fields(request_data)
    assert isinstance(response, Response)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.data['message'] == 'Campos obrigatórios para assinatura não foram fornecidos.'
    assert response.data['status'] == 'error'


def test_validate_subscription_fields_exceed_quantity_recurrences():
    request_data = {
        'recurrence_period': 30,
        'quantity_recurrences': 101,
        'trial_days': 0,
        'max_retries': 3,
        'retry_interval': 1
    }
    response = SubscriptionStrategy.validate_subscription_fields(request_data)
    assert isinstance(response, Response)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.data['message'] == 'Número máximo de recorrências atingido.'
    assert response.data['status'] == 'error'


def test_validate_subscription_fields_exceed_trial_days():
    request_data = {
        'recurrence_period': 30,
        'quantity_recurrences': 10,
        'trial_days': 366,
        'max_retries': 3,
        'retry_interval': 1
    }
    response = SubscriptionStrategy.validate_subscription_fields(request_data)
    assert isinstance(response, Response)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.data['message'] == 'Número máximo de dias de teste atingido.'
    assert response.data['status'] == 'error'


def test_validate_subscription_fields_retry_interval_default_value():
    request_data = {
        'recurrence_period': 30,
        'quantity_recurrences': 10,
        'trial_days': 0,
        'max_retries': 3
    }
    _, _, _, _, retry_interval = SubscriptionStrategy.validate_subscription_fields(request_data)
    assert retry_interval == 1


@pytest.mark.django_db
def test_create_subscription_creates_active_subscription(
    regular_user: User,
    regular_customer: Customer,
    payment: Payment,
):
    recurrence_period = 30
    quantity_recurrences = 12
    trial_days = 0
    max_retries = 3
    retry_interval = 1676

    subscription = SubscriptionStrategy.create_subscription(
        payment=payment,
        current_user=regular_user,
        recurrence_period=recurrence_period,
        quantity_recurrences=quantity_recurrences,
        trial_days=trial_days,
        max_retries=max_retries,
        retry_interval=retry_interval
    )

    assert subscription.user == regular_user
    assert subscription.status == Subscription.STATUS_ACTIVE
    assert subscription.recurrence_period == recurrence_period
    assert subscription.quantity_recurrences == quantity_recurrences
    assert subscription.trial_days == trial_days
    assert subscription.max_retries == max_retries
    assert subscription.retry_interval == retry_interval
    assert subscription.next_payment_date <= timezone.now()
    assert subscription.paymentMethod == payment.paymentMethod
    assert subscription.card == payment.card


@pytest.mark.django_db
def test_create_subscription_with_trial_days(
    regular_user: User,
    regular_customer: Customer,
    payment: Payment,
):
    recurrence_period = 30
    quantity_recurrences = 12
    trial_days = 10
    max_retries = 3
    retry_interval = 1676

    subscription = SubscriptionStrategy.create_subscription(
        payment=payment,
        current_user=regular_user,
        recurrence_period=recurrence_period,
        quantity_recurrences=quantity_recurrences,
        trial_days=trial_days,
        max_retries=max_retries,
        retry_interval=retry_interval
    )

    assert subscription.next_payment_date > timezone.now()
    assert subscription.next_payment_date <= timezone.now() + SubscriptionStrategy.get_time_delta_from_interval(
        time_interval=trial_days)


@pytest.mark.django_db
def test_create_subscription_with_non_credit_card_payment(
    regular_user: User,
    regular_customer: Customer,
    payment: Payment,
):
    payment.paymentMethod = 'pix'

    recurrence_period = 30
    quantity_recurrences = 12
    trial_days = 0
    max_retries = 3
    retry_interval = 1676

    subscription = SubscriptionStrategy.create_subscription(
        payment=payment,
        current_user=regular_user,
        recurrence_period=recurrence_period,
        quantity_recurrences=quantity_recurrences,
        trial_days=trial_days,
        max_retries=max_retries,
        retry_interval=retry_interval
    )

    assert subscription.card is None


@pytest.mark.django_db
def test_validate_payment_method_allows_pix():
    payment = Payment(paymentMethod='pix')
    response = SubscriptionStrategy.get_payment_method_validation_error(payment)
    assert response is None


@pytest.mark.django_db
def test_validate_payment_method_allows_credit_card():
    payment = Payment(paymentMethod='credit_card')
    response = SubscriptionStrategy.get_payment_method_validation_error(payment)
    assert response is None


@pytest.mark.django_db
def test_validate_payment_method_allows_boleto():
    payment = Payment(paymentMethod='boleto')
    response = SubscriptionStrategy.get_payment_method_validation_error(payment)
    assert response is None


@pytest.mark.django_db
def test_validate_payment_method_disallows_invalid_method():
    payment = Payment(paymentMethod='invalid_method')
    response = SubscriptionStrategy.get_payment_method_validation_error(payment)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.data['message'] == 'Método de pagamento não permitido para assinaturas.'


@pytest.mark.django_db
def test_validate_payment_method_disallows_credit_card_with_non_credit_method(credit_card: Card):
    payment = Payment(paymentMethod='pix', card=credit_card)
    response = SubscriptionStrategy.get_payment_method_validation_error(payment)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.data['message'] == 'Cartão de crédito não é permitido para esse método de pagamento.'


@pytest.mark.django_db
@override_switch('subscription_test_mode', active=True)
def test_time_delta_when_test_mode_is_active():
    result = SubscriptionStrategy.get_time_delta_from_interval(10)
    assert result == timedelta(minutes=10)


@pytest.mark.django_db
@override_switch('subscription_test_mode', active=False)
def test_time_delta_when_test_mode_is_inactive():
    result = SubscriptionStrategy.get_time_delta_from_interval(10)
    assert result == timedelta(days=10)


@pytest.mark.django_db
@override_switch('subscription_feature_is_active', active=False)
def test_feature_not_active():
    result = SubscriptionStrategy.check_feature_and_configuration()
    assert result.status_code == status.HTTP_412_PRECONDITION_FAILED
    assert result.data['message'] == 'Feature de assinaturas não está ativa.'

@pytest.mark.django_db
@override_switch('subscription_feature_is_active', active=True)
@override_settings(DEBUG=False)
def mode_active_in_production():
    with patch('logging.WARN') as mock_warn:
        SubscriptionStrategy.check_feature_and_configuration()
        mock_warn.assert_called_once_with('Subscription test mode is active, on production this switch should be disabled.')

@pytest.mark.django_db
@override_switch('subscription_feature_is_active', active=True)
def test_feature_active_and_test_mode_inactive():
    result = SubscriptionStrategy.check_feature_and_configuration()
    assert result is None


@pytest.mark.django_db
def test_process_subscription_payments_with_no_trial_days(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    payment_without_trial_days: Payment
):
    subscription = regular_user_with_subscription
    payment = payment_without_trial_days

    assert subscription.trial_days == 0

    strategy = BulkSubscriptionStrategy()
    strategy._process_payment_without_trial = MagicMock()

    strategy.process_subscription_payments(subscription, payment)

    strategy._process_payment_without_trial.assert_called_once_with(subscription, payment)

@pytest.mark.django_db
def test_process_subscription_payments_with_trial_days(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription_with_trial_days: Subscription,
    scheduled_payment_for_after_trial_days: Payment
):
    subscription = regular_user_with_subscription_with_trial_days
    assert subscription.trial_days == 10

    payment = scheduled_payment_for_after_trial_days
    assert payment.status == 'scheduled'

    strategy = BulkSubscriptionStrategy()
    strategy.handle_first_payment_after_trial_ends = MagicMock()

    strategy.process_subscription_payments(subscription, payment)

    strategy.handle_first_payment_after_trial_ends.assert_called_once_with(subscription, payment)


@patch("payment.strategies.card_strategy.CardStrategy.executePayment")
@patch("payment.strategies.subscription.base.SubscriptionStrategy._handle_payment_status")
@pytest.mark.django_db
def test_process_payment_without_trial_saves_subscription_to_payment(
    mock_handle_payment_status,
    mock_execute_payment,
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    payment_without_trial_days: Payment
):
    subscription = regular_user_with_subscription
    payment = payment_without_trial_days

    mock_execute_payment.return_value = payment

    SubscriptionStrategy._process_payment_without_trial(SubscriptionStrategy, subscription, payment)

    assert payment.subscription == subscription
    mock_execute_payment.assert_called_once_with(payment, payment.card)
    mock_handle_payment_status.assert_called_once_with(subscription, payment)

@patch("payment.strategies.card_strategy.CardStrategy.executePayment")
@patch("payment.strategies.subscription.base.SubscriptionStrategy._handle_payment_status")
@pytest.mark.django_db
def test_process_payment_without_trial_executes_payment_strategy(
    mock_handle_payment_status,
    mock_execute_payment,
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    payment_without_trial_days: Payment
):
    subscription = regular_user_with_subscription
    payment = payment_without_trial_days
    assert payment.paymentMethod == 'credit_card'

    mock_execute_payment.return_value = payment

    SubscriptionStrategy._process_payment_without_trial(SubscriptionStrategy, subscription, payment)

    mock_execute_payment.assert_called_once_with(payment, payment.card)


@patch("payment.strategies.card_strategy.CardStrategy.executePayment")
@patch("payment.strategies.subscription.base.SubscriptionStrategy._handle_payment_status")
@pytest.mark.django_db
def test_process_payment_without_trial_handles_payment_status(
    mock_handle_payment_status,
    mock_execute_payment,
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    payment_without_trial_days: Payment
):
    subscription = regular_user_with_subscription
    payment = payment_without_trial_days

    mock_execute_payment.return_value = payment

    SubscriptionStrategy._process_payment_without_trial(SubscriptionStrategy, subscription, payment)

    mock_handle_payment_status.assert_called_once_with(subscription, payment)

@patch("payment.strategies.subscription.base.SubscriptionStrategy.handle_pending_payment")
@pytest.mark.django_db
def test_handle_payment_status_with_pending_pix_or_boleto_calls_handle_pending_payment(
    mock_handle_pending_payment,
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    payment_without_trial_days: Payment,
    payment_without_trial_days_pix_payment_method: Payment,
):
    subscription = regular_user_with_subscription
    created_payment = payment_without_trial_days_pix_payment_method
    assert created_payment.paymentMethod == 'pix'
    assert created_payment.status == 'pending'

    SubscriptionStrategy._handle_payment_status(SubscriptionStrategy, subscription, created_payment)

    mock_handle_pending_payment.assert_called_once_with(subscription, created_payment)

@patch("payment.strategies.subscription.base.SubscriptionStrategy.handle_paid_payment")
@pytest.mark.django_db
def test_handle_payment_status_with_paid_status_calls_handle_paid_payment(
    mock_handle_paid_payment,
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    paid_payment_with_subscription: Payment
):
    subscription = regular_user_with_subscription
    created_payment = paid_payment_with_subscription
    assert created_payment.status == 'paid'

    SubscriptionStrategy._handle_payment_status(SubscriptionStrategy, subscription, created_payment)

    mock_handle_paid_payment.assert_called_once_with(subscription, created_payment)


@patch("payment.strategies.subscription.base.SubscriptionStrategy.handle_failed_payment")
@pytest.mark.django_db
def test_handle_payment_status_with_failed_status_calls_handle_failed_payment(
    mock_handle_failed_payment,
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    failed_payment_with_subscription: Payment,
):
    subscription = regular_user_with_subscription
    created_payment = failed_payment_with_subscription
    assert created_payment.paymentMethod == 'credit_card'
    assert created_payment.status == 'failed'

    SubscriptionStrategy._handle_payment_status(SubscriptionStrategy, subscription, created_payment)

    mock_handle_failed_payment.assert_called_once_with(subscription, created_payment)
