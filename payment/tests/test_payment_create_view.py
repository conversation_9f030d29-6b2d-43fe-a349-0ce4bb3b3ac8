from unittest.mock import patch, MagicMock

import pytest
from rest_framework.response import Response
from rest_framework.test import APIClient

from gateway.company.utils import PaymentException
from gateway.models import Acquirer
from payment.strategies.card_strategy import CardStrategy
from payment.strategies.subscription.base import SubscriptionStrategy
from payment.views import PaymentCreateView
from payment.models import Subscription, Payment


def refuse_payment(payment_data, gateway, card):
    payment_data.status = 'refused'
    payment_data.reason = 'credit card refused'
    return payment_data

class TestPaymentCreateView:
    # TODO (scpaes): Refactor this test
    @pytest.mark.skip(reason="We refactored the code and this test is no longer valid")
    @pytest.mark.django_db
    @patch('payment.strategies.card_strategy.CardStrategy.getGatewaysByPriority')
    @patch('payment.strategies.card_strategy.handleBalance')
    @patch('payment.strategies.card_strategy.CardStrategy._create_antifraud_transaction')
    @patch('payment.strategies.card_strategy.CardStrategy.associateFraud')
    @patch('payment.strategies.card_strategy.CardStrategy.checkFraud')
    @patch('payment.views.PaymentCreateView.schedule_future_payments')
    def test_process_subscription_payments_error_message(
        self,
        mock_schedule_future_payments: callable,
        mock_check_fraud: callable,
        mock_associate_fraud: callable,
        mock_create_antifraud_transaction: callable,
        mock_handle_balance: callable,
        mock_get_gateways_by_priority: callable,
        acquirer: Acquirer,
        mocker,
        logged_in_as_regular_user: APIClient,
        regular_user_with_subscription: Subscription,
        payment_without_trial_days: Payment,
    ):
        mock_check_fraud.return_value = False
        mock_get_gateways_by_priority.return_value = [acquirer]

        mocker.patch.object(CardStrategy, "processPayment", side_effect=PaymentException('credit card refused', 'error', 'refused'))

        assert regular_user_with_subscription.status == Subscription.STATUS_ACTIVE

        payment_create_view = PaymentCreateView()
        response = payment_create_view.process_subscription_payments(regular_user_with_subscription, payment_without_trial_days)

        mock_check_fraud.assert_called_with(payment_without_trial_days)
        mock_associate_fraud.assert_called_with(payment_without_trial_days)
        mock_create_antifraud_transaction.assert_called_with(payment_without_trial_days)
        mock_handle_balance.assert_called_with(payment_without_trial_days)

        assert isinstance(response, Response)


    def test_returns_bulk_strategy_when_called(self,):
        result = PaymentCreateView._get_subscription_bulk_strategy()
        assert isinstance(result, SubscriptionStrategy)


    def test_raises_error_when_bulk_strategy_is_unavailable(self,):
        with patch('payment.services.subscription_factory.SubscriptionFactory.get_strategy',
                   side_effect=Exception("Strategy not found")):
            with pytest.raises(Exception, match="Strategy not found"):
                PaymentCreateView._get_subscription_bulk_strategy()

    @pytest.mark.django_db
    def test_updates_item_with_subscription_id_on_successful_creation(self, payment: Payment):
        item = {
            "title": "Boneca Gatucha Pet",
            "unitPrice": 42.0,
            "quantity": 1,
            "tangible": True,
            "externalRef": "1737",
            "subscription": {
                "recurrence_period": 3,
                "quantity_recurrences": 2,
                "trial_days": 0,
                "max_retries": 3,
                "retry_interval": 1,
                "amount": 42
            }
        }

        subscription_data = item.get("subscription")
        strategy = PaymentCreateView()

        strategy._process_subscription_item(item, subscription_data, payment.user, payment)

        subscription = Subscription.objects.get(id=item["subscription"])

        assert subscription.trial_days == subscription_data["trial_days"]
        assert subscription.quantity_recurrences == subscription_data["quantity_recurrences"]
        assert subscription.recurrence_period == subscription_data["recurrence_period"]
        assert subscription.max_retries == subscription_data["max_retries"]
        assert subscription.retry_interval == subscription_data["retry_interval"]
        assert subscription.amount == subscription_data["amount"]



    @pytest.mark.django_db
    def test_does_not_update_item_when_subscription_creation_fails(self, payment: Payment):
        item = {
            "title": "Boneca Gatucha Pet",
            "unitPrice": 42.0,
            "quantity": 1,
            "tangible": True,
            "externalRef": "1737",
            "subscription": {
                "recurrence_period": 3,
                "quantity_recurrences": 2,
                "trial_days": 0,
                "max_retries": 3,
                "retry_interval": 1,
                "amount": 42
            }
        }
        subscription_data = item["subscription"]
        user = payment.user
        strategy = PaymentCreateView()

        with patch.object(strategy, '_get_subscription_bulk_strategy') as mock_strategy:
            mock_subscription_strategy = MagicMock()
            mock_subscription_strategy.create_subscription_from_item.return_value = None
            mock_strategy.return_value = mock_subscription_strategy

            strategy._process_subscription_item(item, subscription_data, user, payment)

            assert item["subscription"] is None
            mock_subscription_strategy.create_subscription_from_item.assert_called_once_with(
                subscription_data=subscription_data, payment=payment, user=user
            )
