from unittest.mock import patch

import pytest
from rest_framework.response import Response

from customer.models import Customer
from payment.models import Subscription, Payment
import datetime
from user.models import User
from payment.strategies.subscription.bulk import BulkSubscriptionStrategy


@pytest.mark.django_db
def test_updates_next_payment_date_correctly(
    regular_user: User,
    regular_user_with_subscription: Subscription,
):
    subscription = regular_user_with_subscription
    original_next_payment_date = subscription.next_payment_date

    with patch('payment.strategies.subscription.base.SubscriptionStrategy.get_time_delta_from_interval') as mock_time_delta:
        mock_time_delta.return_value = datetime.timedelta(days=30)

        BulkSubscriptionStrategy.calculate_next_payment_data(subscription)

        subscription.refresh_from_db()
        assert subscription.next_payment_date == original_next_payment_date + datetime.timedelta(days=30)


@pytest.mark.django_db
def handles_missing_recurrence_period_gracefully():
    subscription = Subscription.objects.create(
        next_payment_date=datetime.date(2023, 10, 1),
        recurrence_period=None
    )

    with pytest.raises(TypeError):
        BulkSubscriptionStrategy.calculate_next_payment_data(subscription)


@pytest.mark.django_db
def does_not_update_next_payment_date_on_save_failure():
    subscription = Subscription.objects.create(
        next_payment_date=datetime.date(2023, 10, 1),
        recurrence_period='monthly'
    )

    with patch('payment.models.Subscription.save', side_effect=Exception("Save failed")) as mock_save, \
         patch('payment.strategies.subscription.base.SubscriptionStrategy.get_time_delta_from_interval') as mock_time_delta:
        mock_time_delta.return_value = datetime.timedelta(days=30)

        with pytest.raises(Exception, match="Save failed"):
            BulkSubscriptionStrategy.calculate_next_payment_data(subscription)

        subscription.refresh_from_db()
        assert subscription.next_payment_date == datetime.date(2023, 10, 1)


@pytest.mark.django_db
def test_updates_payment_amount_to_match_subscription_amount(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
    pending_payment_with_subscription_pix_payment_method: Payment,
):
    subscription = regular_user_with_subscription
    payment = pending_payment_with_subscription_pix_payment_method

    assert not payment.amount == subscription.amount

    updated_amount_payment = BulkSubscriptionStrategy.adjust_payment_amount_to_match_subscription(payment, subscription)

    assert updated_amount_payment.amount == subscription.amount


@pytest.mark.django_db
def does_not_fail_when_subscription_amount_is_zero():
    subscription = Subscription.objects.create(amount=0.0)
    payment = Payment.objects.create(amount=50.0)

    updated_payment = BulkSubscriptionStrategy.adjust_payment_amount_to_match_subscription(payment, subscription)

    assert updated_payment.amount == 0.0


@pytest.mark.django_db
def handles_none_subscription_gracefully():
    payment = Payment.objects.create(amount=50.0)

    with pytest.raises(AttributeError):
        BulkSubscriptionStrategy.adjust_payment_amount_to_match_subscription(payment, None)


@pytest.mark.django_db
def test_returns_none_when_subscription_data_is_invalid(payment: Payment):
    subscription_data = {}
    strategy = BulkSubscriptionStrategy()

    with patch.object(strategy, '_validate_subscription_data', return_value=None):
        result = strategy.create_subscription_from_item(subscription_data, payment.user, payment)
        assert result is None


@pytest.mark.django_db
def test_returns_none_when_payment_is_invalid(payment_with_picpay_as_payment_method: Payment):
    payment = payment_with_picpay_as_payment_method
    subscription_data = {
        "amount": 100.0,
        "recurrence_period": 30,
        "quantity_recurrences": 12,
        "trial_days": 0,
        "max_retries": 3,
        "retry_interval": 7,
    }
    strategy = BulkSubscriptionStrategy()

    assert not payment.paymentMethod in ["pix", "credit_card", "boleto"]

    result = strategy.create_subscription_from_item(subscription_data, User(), payment)
    assert result is None


@pytest.mark.django_db
def test_creates_subscription_and_returns_id_on_valid_data(payment: Payment):
    subscription_data = {
        "amount": 100.0,
        "recurrence_period": 30,
        "quantity_recurrences": 12,
        "trial_days": 0,
        "max_retries": 3,
        "retry_interval": 7,
    }

    assert Subscription.objects.exists() is False

    strategy = BulkSubscriptionStrategy()

    result = strategy.create_subscription_from_item(subscription_data, payment.user, payment)

    assert Subscription.objects.exists() is True

    created_subscription = Subscription.objects.get(id=result)
    assert created_subscription.amount == subscription_data["amount"]
    assert created_subscription.recurrence_period == subscription_data["recurrence_period"]
    assert created_subscription.quantity_recurrences == subscription_data["quantity_recurrences"]
    assert created_subscription.trial_days == subscription_data["trial_days"]
    assert created_subscription.max_retries == subscription_data["max_retries"]
    assert created_subscription.retry_interval == subscription_data["retry_interval"]


@patch('payment.strategies.subscription.bulk.logger.error')
@pytest.mark.django_db
def test_returns_none_when_subscription_fields_are_invalid(mock_log, payment: Payment):
    subscription_data = {"amount": 100.0}
    strategy = BulkSubscriptionStrategy()

    with patch.object(strategy, 'validate_subscription_fields', return_value=Response(status=400)):
        result = strategy._validate_subscription_data(subscription_data, payment)
        assert result is None

    mock_log.assert_called_with(
        f"Failed to create subscription from payment {payment.id}: invalid subscription fields"
    )


@pytest.mark.django_db
def test_returns_tuple_with_subscription_data_on_valid_fields(payment: Payment):
    subscription_data = {
        "amount": 100.0,
        "recurrence_period": 30,
        "quantity_recurrences": 12,
        "trial_days": 0,
        "max_retries": 3,
        "retry_interval": 7,
    }
    strategy = BulkSubscriptionStrategy()

    result = strategy._validate_subscription_data(subscription_data, payment)
    assert isinstance(result, tuple)
    assert len(result) == 6
    recurrence_period, quantity_recurrences, trial_days, max_retries, retry_interval, amount = result
    assert recurrence_period == subscription_data["recurrence_period"]
    assert quantity_recurrences == subscription_data["quantity_recurrences"]
    assert trial_days == subscription_data["trial_days"]
    assert max_retries == subscription_data["max_retries"]
    assert retry_interval == subscription_data["retry_interval"]
    assert amount == subscription_data["amount"]


@patch('payment.strategies.subscription.bulk.logger.error')
@pytest.mark.django_db
def test_returns_false_when_payment_method_is_invalid(
    mock_log,
    payment_with_picpay_as_payment_method: Payment,
):
    payment = payment_with_picpay_as_payment_method

    assert not payment.paymentMethod in ["pix", "credit_card", "boleto"]

    strategy = BulkSubscriptionStrategy()

    result = strategy._validate_payment(payment)
    assert result is False

    mock_log.assert_called_with(
        f"Failed to create subscription from payment {payment.id}: invalid payment method"
    )


@patch('payment.strategies.subscription.bulk.logger.error')
@pytest.mark.django_db
def test_returns_true_when_payment_method_is_valid(
    mock_log,
    payment: Payment
):
    assert payment.paymentMethod in ["pix", "credit_card", "boleto"]

    strategy = BulkSubscriptionStrategy()

    result = strategy._validate_payment(payment)
    assert result is True

    mock_log.assert_not_called()