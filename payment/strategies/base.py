import random
from abc import ABC, abstractmethod

from gateway.models import Acquirer
from payment.models import Payment, Split, AdditionalFlows
from decimal import Decimal

from user.models import Withdrawal
from customer.models import Customer
from django.conf import settings
from django.utils import timezone


class PaymentStrategy(ABC):
    @abstractmethod
    def executePayment(self, payment_data: Payment, card={}) -> Payment:
        pass

    @abstractmethod
    def executeRefund(self, payment_data: Payment) -> Payment:
        pass

    def convert_status(self, status):
        obj = {
            'processing': 'pending',
            'authorized': 'pending',
            'generated': 'pending',
            'active': 'pending',
            'paid': 'paid',
            'approved': 'paid',
            'refunded': 'refunded',
            'partially_refunded': 'refunded',
            'pre_authorized': 'pending',
            'unauthorized': 'declined',
            'waiting_payment': 'pending',
            'awaiting_payment': 'pending',
            'refused': 'declined',
            'not_authorized': 'declined',
            'failed': 'declined',
            'with_error': 'declined',
            'error': 'declined',
            'voided': 'declined',
            'chargedback': 'chargedback',
            'chargeback': 'chargedback',
            'canceled': 'canceled',
            'in_protest': 'in_protest',
            'partially_paid': 'partially_paid',
            'confirmed': 'paid',
            'received': 'paid',
            'captured': 'paid',
            'declined': 'declined',
            'rejected': 'declined',
            'denied': 'declined',
            'in_analysis': 'pending',
            'waiting': 'pending',
            'concluded': 'paid',
            'expired': 'canceled',
            'deleted_by_receiving_user': 'canceled',
            'deleted_by_psp': 'canceled'
        }
        return obj.get(status.lower(), 'pending')

    def convert_status_efi(self, status):
        obj = {
            'unpaid': 'refused',
            'paid': 'paid',
            'approved': 'paid',
            'waiting': 'pending',
            'refunded': 'refunded',
            'contested': 'chargedback',
            'canceled': 'canceled',
            'settled': 'paid',
        }
        return obj.get(status, 'pending')

    def convert_status_cielo(self, status):
        obj = {
            '0': 'pending',
            '1': 'pending',
            '2': 'paid',
            '3': 'declined',
            '10': 'canceled',
            '11': 'refunded',
            '12': 'pending',
            '20': 'pending',
        }
        return obj.get(status, 'pending')

    def setInterest(self, payment_data: Payment) -> Payment:
        if payment_data.installments > 1:
            interest = payment_data.user.getInterest(payment_data, payment_data.acquirer.id)
            payment_data.interest = Decimal(payment_data.amount) * Decimal(interest / 100)
            payment_data.amount = Decimal(payment_data.amount) + payment_data.interest
        return payment_data

    def setGatewayFee(self, payment_data: Payment):
        if settings.GATEWAY_FEE_PERCENTAGE:
            payment_data.gatewayFee = Decimal(payment_data.amount) * (Decimal(settings.GATEWAY_FEE_PERCENTAGE) / 100)

        return payment_data

    def setAcquirerCost(self, payment_data: Payment, fee):
        payment_data.fixedCost = fee.get('costFixed')
        payment_data.rawPercentageCost = fee.get('costPercentage')
        payment_data.percentageCost = Decimal(payment_data.amount) * (Decimal(payment_data.rawPercentageCost) / 100)
        payment_data.cost = payment_data.fixedCost + payment_data.percentageCost

        return payment_data

    @staticmethod
    def update_payment_splits(payment: Payment, release_amount: float, reverse_amount: float):
        if splits := payment.splits.all():
            for split in splits:
                split.amount = release_amount * split.percentage / 100
                split.amountReserve = reverse_amount * split.percentage / 100
            Split.objects.bulk_update(splits, ['amount', 'amountReserve'])

    @staticmethod
    def has_additional_flows():
        return AdditionalFlows.objects.filter(status='active').exists()


    @staticmethod
    def has_risk_dilution_flow():
        return AdditionalFlows.objects.filter(actions__type='randomizer', status='active').exists()

    @staticmethod
    def map_from_payment_method_to_acquirer_filter_field(payment_method: str):
        return {
            'credit_card': 'creditCardEnabled',
            'boleto': 'ticketEnabled',
            'pix': 'pixEnabled',
            'picpay': 'picpayEnabled',
            'nupay': 'nupayEnabled',
            'googlepay': 'googlepayEnabled',
            'applepay': 'applepayEnabled',
        }.get(payment_method, 'creditCard')


    def get_acquirer_randomly_for_risk_dilution(self, payment: Payment) -> list[Acquirer] | None:
        """
        Get a list of acquirers that can be used for risk dilution based on the payment method and the acquirer's
        risk dilution percentage.
        """
        acquirer_filter_by_payment_method = self.map_from_payment_method_to_acquirer_filter_field(
            payment_method=payment.paymentMethod
        )
        acquirers = [
            acquirer for acquirer in
            Acquirer.objects.filter(
                **{acquirer_filter_by_payment_method: True},
                risk_dilution_percentage__gt=0
            ).order_by("risk_dilution_percentage")
        ]
        weights = [acquirer.risk_dilution_percentage for acquirer in acquirers]

        if acquirers:
            return random.choices(acquirers, weights=weights, k=1)

        return None



    def setFee(self, payment_data: Payment, gateway):
        payment_data.fee = 0
        fee = payment_data.user.getFeeByPaymentMethod(gateway.id, payment_data.paymentMethod, payment_data.installments)

        # Calculate fixed fee
        payment_data.fixedFee = fee.get('fixed')
        if payment_data.feeByItem:
            payment_data.fixedFee = payment_data.fixedFee * len(payment_data.items)
        payment_data.fee += payment_data.fixedFee

        # Percentage fee
        payment_data.percentageFee = Decimal(payment_data.baseAmount) * Decimal(fee.get('percentage') / 100)
        payment_data.fee += payment_data.percentageFee

        # Calculate liquid amount
        payment_data.liquidAmount = Decimal(payment_data.baseAmount) - payment_data.fee

        # Set gateway fee
        payment_data = self.setAcquirerCost(payment_data, fee)

        payment_data = self.setGatewayFee(payment_data)

        payment_data.profit = payment_data.fee + payment_data.interest - payment_data.cost

        # Full reserve and release amount
        reserve = payment_data.user.getReserveByPaymentMethod(payment_data.paymentMethod)
        reserveAmount = payment_data.liquidAmount * reserve.get('reserve') / 100
        releaseAmount = payment_data.liquidAmount - reserveAmount

        self.update_payment_splits(
            payment=payment_data,
            release_amount=releaseAmount,
            reverse_amount=reserveAmount
        )

        self.to_split_balance(payment_data)

        return payment_data

    def to_split_balance(self, payment) -> Payment:
        # Full reserve and release amount
        reserve = payment.user.getReserveByPaymentMethod(payment.paymentMethod)
        reserveAmount = payment.liquidAmount * reserve.get('reserve') / 100
        releaseAmount = payment.liquidAmount - reserveAmount

        splitedAmount = 0
        splitedReserveAmount = 0

        if payment.affiliateSplit:
            releaseAffiliateAmount = releaseAmount * payment.affiliateSplit.percentage / 100
            releaseAffiliateReserveAmount = reserveAmount * payment.affiliateSplit.percentage / 100
            payment.affiliateSplit.amount = releaseAffiliateAmount
            payment.affiliateSplit.amountReserve = releaseAffiliateReserveAmount
            payment.affiliateSplit.percentage = (
                                                        releaseAffiliateAmount + releaseAffiliateReserveAmount) / payment.liquidAmount * 100
            payment.affiliateSplit.save()
            releaseAmount -= releaseAffiliateAmount
            reserveAmount -= releaseAffiliateReserveAmount

        if payment.splits.count() > 0:
            totalSplits = 0
            totalReserveSplits = 0
            for split in payment.splits.filter(type='coproducer'):
                split.amount = releaseAmount * split.percentage / 100
                split.amountReserve = reserveAmount * split.percentage / 100
                split.percentage = (split.amount + split.amountReserve) / payment.liquidAmount * 100
                split.save()
                totalSplits += split.amount
                totalReserveSplits += split.amountReserve
            releaseAmount -= totalSplits
            reserveAmount -= totalReserveSplits

        if payment.producerSplit:
            releaseMainAmount = releaseAmount
            releaseReserveAmount = reserveAmount
            payment.producerSplit.amount = releaseMainAmount
            payment.producerSplit.amountReserve = releaseReserveAmount
            payment.producerSplit.percentage = (releaseMainAmount + releaseReserveAmount) * 100 / payment.liquidAmount
            payment.producerSplit.rawPercentage = (
                                                          releaseMainAmount + releaseReserveAmount) * 100 / payment.liquidAmount
            payment.producerSplit.save()

        return payment

    def associateFraud(self, payment: Payment):
        if payment.status == 'declined':
            # check if any customer with same ip is blocked
            if payment.customer.ip:
                customers = Customer.objects.filter(
                    ip=payment.customer.ip,
                )
                # check if any customer with same ip is blocked
                # if so, block this customer as well
                for c in customers:
                    if c.blocked_until:
                        if c.blocked_until > timezone.now():
                            payment.customer.blocked_until = c.blocked_until or timezone.now() + timezone.timedelta(
                                hours=24)
                            payment.customer.blockedTimes = c.blockedTimes
                            payment.customer.save()
                            payment.status = 'blocked'
                            payment.internalReason = f'Cliente bloqueado por excesso de tentativas, bloqueado até {payment.customer.blocked_until.strftime("%d/%m/%Y %H:%M")}'
                            payment.refusedByAntifraud = True
                            payment.save()
                            return True

            return payment

    def perform_anti_fraud_checks(self, payment: Payment):
        if payment.customer.blocked_until and payment.customer.blocked_until > timezone.now():
            self.set_payment_blocked_by_antifraud(
                payment,
                f'Cliente bloqueado por excesso de tentativas, bloqueado até {payment.customer.blocked_until.strftime("%d/%m/%Y %H:%M")}'
            )

            return True

        # Check if the customer has 5 failed payments in the last 24 hours
        last24hour = Payment.objects.filter(
            customer=payment.customer,
            paymentMethod='credit_card',
            createdAt__gte=timezone.now() - timezone.timedelta(days=1)
        ).order_by('-createdAt')

        last1hour = last24hour.filter(createdAt__gte=timezone.now() - timezone.timedelta(hours=1))

        # Limita tentativas de pagamento com cartão
        if self.checkCardRetryLimit(payment, last24hour):
            return True
        # Limita uso de cartões diferentes
        if self.checkCardLimit(payment, last24hour):
            return True
        # Limita endereços diferentes por hora
        # if self.checkAddressLimit(payment, last1hour):
        #     return True

        failedPaymentsFollowed = 0
        for p in last24hour:
            if p.status == 'declined':
                failedPaymentsFollowed += 1
                if failedPaymentsFollowed > 10:
                    if payment.customer.blockedTimes == 1:
                        payment.customer.blocked_until = timezone.now() + timezone.timedelta(hours=24)
                    elif payment.customer.blockedTimes >= 2:
                        payment.customer.blocked_until = timezone.now() + timezone.timedelta(years=1000)
                    payment.customer.blockedTimes += 1
                    payment.customer.save()
                    self.set_payment_blocked_by_antifraud(
                        payment,
                        f'Cliente bloqueado por excesso de tentativas, bloqueado até {payment.customer.blocked_until.strftime("%d/%m/%Y %H:%M")}'
                    )
                    return True
            else:
                failedPaymentsFollowed = 0
        return False

    def checkFraud(self, payment: Payment):
        if payment.paymentMethod == 'credit_card' and not payment.is_recurrent:
            return self.perform_anti_fraud_checks(payment)
        return False

    # Same card
    def checkCardRetryLimit(self, payment: Payment, last24hour, limit=10):
        if payment.paymentMethod == 'credit_card':
            retries = sum(p.card.encryptedNumber == payment.card.encryptedNumber for p in last24hour)
            if retries > limit:
                self.set_payment_blocked_by_antifraud(
                    payment,
                    'Limite de tentativas excedido, por favor tente novamente mais tarde'
                )
                return True
        return False

    # Different card retries
    def checkCardLimit(self, payment: Payment, last24hour, limit=3):
        if payment.paymentMethod == 'credit_card':
            # make this retries code check how many card are different in the list
            cards = []
            for p in last24hour:
                if p.card.encryptedNumber not in cards:
                    cards.append(p.card.encryptedNumber)
            retries = len(cards)
            if retries > 3:
                self.set_payment_blocked_by_antifraud(
                    payment,
                    'Limite de tentativas excedido, por favor tente novamente mais tarde'
                )
                return True
        return False

    def checkAddressLimit(self, payment: Payment, last1hour, limit=5):
        if payment.paymentMethod == 'credit_card':
            retries = sum(p.address == payment.address for p in last1hour)
            if retries > 5:
                self.set_payment_blocked_by_antifraud(
                    payment,
                    'Limite de tentativas excedido, por favor tente novamente mais tarde'
                )
                return True
        return False

    def set_payment_blocked_by_antifraud(self, payment: Payment, reason: str):
        payment.status = 'blocked'
        payment.internalReason = reason
        payment.refusedByAntifraud = True
        payment.save()


class CashoutBase(ABC):
    @abstractmethod
    def executeCashout(self, withdrawal: Withdrawal) -> Withdrawal:
        pass
