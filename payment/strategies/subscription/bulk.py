import logging

from django.utils import timezone
from rest_framework.response import Response

from payment.models import Payment
from payment.strategies.subscription.base import SubscriptionStrategy
from user.models import User

logger = logging.getLogger(__name__)


class BulkSubscriptionStrategy(SubscriptionStrategy):
    @staticmethod
    def calculate_next_payment_data(subscription):
        subscription.next_payment_date = subscription.next_payment_date or timezone.now()
        subscription.next_payment_date += SubscriptionStrategy.get_time_delta_from_interval(
            subscription.recurrence_period
        )
        subscription.save(update_fields=['next_payment_date'])

    @staticmethod
    def update_subscription_status(subscription):
        from payment.models import Subscription
        subscription.status = Subscription.STATUS_ACTIVE
        subscription.save(update_fields=['status'])

    @staticmethod
    def adjust_payment_amount_to_match_subscription(payment, subscription):
        """
        Adjusts the payment amount to align with the subscription's defined amount.
        When creating a subscription in bulk from a single payment, the payment amount must be updated
        to ensure consistency before scheduling the recurrences.
        Args:
            payment (Payment): The payment object to be updated.
            subscription (Subscription): The subscription object containing the correct amount.

        Returns:
            Payment: The updated payment object with the adjusted amount.
        """
        if subscription.amount:
            payment.amount = subscription.amount
        return payment

    def execute_subscription(self, payment, current_user: User = None, request_data: dict = None, subscription=None):
        self.check_feature_and_configuration()

        return self.process_subscription(
            payment=payment,
            current_user=current_user,
            request_data=request_data,
            subscription=subscription
        )

    def process_subscription(self, payment, current_user: User = None, request_data: dict = None, subscription=None):
        self.calculate_next_payment_data(subscription)
        self.update_subscription_status(subscription)

        payment = self.adjust_payment_amount_to_match_subscription(payment, subscription)

        subscription.refresh_from_db()

        self.schedule_future_payments(subscription, payment)

    def create_subscription_from_item(self, subscription_data: dict, user: User, payment: Payment) -> int | None:
        result = self._validate_subscription_data(subscription_data, payment)
        if not result:
            return None

        recurrence_period, quantity_recurrences, trial_days, max_retries, retry_interval, amount = result

        if not self._validate_payment(payment):
            return None

        subscription = self._create_subscription_instance(
            payment, user, recurrence_period, quantity_recurrences, trial_days, max_retries, retry_interval, amount
        )

        return subscription.id

    def _validate_subscription_data(self, subscription_data: dict, payment: Payment) -> tuple | None:
        result = self.validate_subscription_fields(subscription_data)
        if isinstance(result, Response):
            logger.error(f"Failed to create subscription from payment {payment.id}: invalid subscription fields")
            return None
        return *result, subscription_data["amount"]

    def _validate_payment(self, payment: Payment) -> bool:
        if self.get_payment_method_validation_error(payment):
            logger.error(f"Failed to create subscription from payment {payment.id}: invalid payment method")
            return False
        return True

    def _create_subscription_instance(
        self,
        payment: Payment,
        user: User,
        recurrence_period: int,
        quantity_recurrences: int,
        trial_days: int,
        max_retries: int,
        retry_interval: int,
        amount: float
    ):
        from payment.models import Subscription

        return self.create_subscription(
            payment=payment,
            current_user=user,
            recurrence_period=recurrence_period,
            quantity_recurrences=quantity_recurrences,
            trial_days=trial_days,
            max_retries=max_retries,
            retry_interval=retry_interval,
            amount=amount,
            initial_status=Subscription.STATUS_INACTIVE,
        )
