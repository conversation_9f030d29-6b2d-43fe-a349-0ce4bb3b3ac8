from __future__ import annotations

import logging
from abc import ABC, abstractmethod
from rest_framework import status
from rest_framework.response import Response
from datetime import timedelta, datetime
from django.utils import timezone
from waffle import switch_is_active

from payment.models import Payment, Subscription
from payment.serializers import SubscriptionSerializer, PaymentSerializer
from payment.services.payment_factory import PaymentStrategyFactory
from payment.tasks import schedule_payment_check, schedule_payment
from user.models import User
from django.conf import settings


class SubscriptionStrategy(ABC):
    @staticmethod
    def get_time_delta_from_interval(time_interval: int) -> timedelta:
        """
        Get the timedelta based on the time interval parameter and the subscription_test_mode switch
        """
        if switch_is_active('subscription_test_mode'):
            return timedelta(minutes=time_interval)
        return timedelta(days=time_interval)

    @staticmethod
    def duplicate_payment(
        next_due_date: datetime,
        payment: Payment,
        amount: float = None,
        subscription: Subscription = None
    ) -> Payment:
        payment.id = None
        payment.pk = None
        payment._state.adding = True
        payment.due_date = next_due_date
        payment.status = 'scheduled'

        if amount:
            payment.amount = amount
        if subscription:
            payment.subscription = subscription

        payment.save()
        return payment

    def schedule_recurrent_subscription(self, next_due_date: datetime, payment: Payment, amount: float = None) -> None:
        duplicated = self.duplicate_payment(next_due_date, payment, amount)
        schedule_payment(duplicated)

    @staticmethod
    def schedule_initial_payment(
        subscription: Subscription,
        payment: Payment,
        next_due_date: datetime
    ) -> None:
        payment.status = 'scheduled'
        payment.due_date = next_due_date
        payment.subscription = subscription
        payment.save()
        schedule_payment(payment)

    @staticmethod
    def create_future_payment(
        subscription: Subscription,
        payment: Payment,
        next_due_date: datetime
    ) -> Payment:
        return SubscriptionStrategy.duplicate_payment(
            next_due_date=next_due_date,
            payment=payment,
            subscription=subscription
        )

    def schedule_recurring_payments(
        self,
        subscription: Subscription,
        payment: Payment,
        next_due_date: datetime
    ) -> None:
        for _ in range(subscription.quantity_recurrences):
            future_payment = self.create_future_payment(subscription, payment, next_due_date)
            next_due_date += self.get_time_delta_from_interval(time_interval=subscription.recurrence_period)
            schedule_payment(future_payment)
        subscription.quantity_recurrences += 1

    def schedule_future_payments(self, subscription: Subscription, payment: Payment) -> None:
        next_due_date = subscription.next_payment_date

        if subscription.quantity_recurrences == -1 and subscription.trial_days == 0:
            return self.schedule_recurrent_subscription(next_due_date, payment)

        if subscription.trial_days > 0:
            self.schedule_initial_payment(subscription, payment, next_due_date)
            next_due_date += self.get_time_delta_from_interval(time_interval=subscription.recurrence_period)
            # we need to decrease the quantity_recurrences by 1 because the first payment was already scheduled
            # when the subscription was created, but we also didn't save the subscription to have the correct
            # number of recurrences
            if not subscription.quantity_recurrences == -1:
                subscription.quantity_recurrences -= 1

        self.schedule_recurring_payments(subscription, payment, next_due_date)
        return None

    def handle_pending_payment(self, subscription: Subscription, created_payment: Payment) -> Response:
        subscription.status = Subscription.STATUS_INACTIVE
        subscription.save(update_fields=['status'])
        schedule_payment_check(created_payment)
        self.schedule_future_payments(subscription, created_payment)
        subscription_serializer = SubscriptionSerializer(subscription)
        return Response(
            {
                "message": "Aguardando o primeiro pagamento para ativação da assinatura, futuras cobranças agendadas.",
                "subscription": subscription_serializer.data,
            },
            status=status.HTTP_201_CREATED
        )

    def handle_paid_payment(self, subscription: Subscription, payment: Payment) -> Response:
        subscription.next_payment_date += self.get_time_delta_from_interval(time_interval=subscription.recurrence_period)
        subscription.save()
        self.schedule_future_payments(subscription, payment)
        subscription_serializer = SubscriptionSerializer(subscription)
        return Response(
            {
                "message": "Pagamento inicial realizado e assinaturas futuras agendadas.",
                "subscription": subscription_serializer.data,
            },
            status=status.HTTP_201_CREATED
        )

    @staticmethod
    def handle_failed_payment(subscription, created_payment) -> Response:
        subscription.cancel()
        created_payment.refresh_from_db()
        serialized_payment = PaymentSerializer(created_payment).data
        return Response(
            {
                "error": "Erro ao processar o pagamento inicial.",
                "reason": f"{created_payment.reason}",
                "failed_payment_data": serialized_payment,
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    def handle_first_payment_after_trial_ends(self, subscription: Subscription, payment: Payment) -> Response:
        self.schedule_future_payments(subscription, payment)
        subscription_serializer = SubscriptionSerializer(subscription)
        return Response(
            {
                "message": "Assinatura criada e todos os pagamentos agendados após o período de teste.",
                "subscription": subscription_serializer.data,
            },
            status=201)

    def _handle_payment_status(self, subscription: Subscription, created_payment: Payment) -> Response:
        if created_payment.paymentMethod in ['pix', 'boleto'] and created_payment.status == 'pending':
            return self.handle_pending_payment(subscription, created_payment)
        elif created_payment.status == 'paid':
            return self.handle_paid_payment(subscription, created_payment)
        else:
            return self.handle_failed_payment(subscription, created_payment)

    def _process_payment_without_trial(self, subscription: Subscription, payment: Payment) -> Response:
        payment.subscription = subscription
        payment.save(update_fields=['subscription'])

        payment_strategy = PaymentStrategyFactory.get_strategy(payment.paymentMethod)
        payment = payment_strategy.executePayment(payment, payment.card)

        payment.save()

        return self._handle_payment_status(subscription, payment)

    def process_subscription_payments(self, subscription: Subscription, payment: Payment) -> Response:
        trial_days = subscription.trial_days

        if trial_days == 0:
            return self._process_payment_without_trial(subscription, payment)
        else:
            return self.handle_first_payment_after_trial_ends(subscription, payment)

    @staticmethod
    def validate_subscription_fields(request_data: dict) -> tuple | Response:
        recurrence_period = request_data.get('recurrence_period')
        quantity_recurrences = request_data.get('quantity_recurrences')
        trial_days = request_data.get('trial_days', 0)
        max_retries = request_data.get('max_retries', 3)
        retry_interval = request_data.get('retry_interval', 1)

        if not recurrence_period or not quantity_recurrences:
            return Response({'status': 'error', 'message': 'Campos obrigatórios para assinatura não foram fornecidos.'},
                            status=status.HTTP_400_BAD_REQUEST)

        if quantity_recurrences > 100:
            return Response({'status': 'error', 'message': 'Número máximo de recorrências atingido.'},
                            status=status.HTTP_400_BAD_REQUEST)

        if trial_days > 365:
            return Response({'status': 'error', 'message': 'Número máximo de dias de teste atingido.'},
                            status=status.HTTP_400_BAD_REQUEST)
        return recurrence_period, quantity_recurrences, trial_days, max_retries, retry_interval

    @staticmethod
    def get_payment_method_validation_error(payment: Payment) -> Response | None:
        if payment.paymentMethod not in ['pix', 'credit_card', 'boleto']:
            return Response({'status': 'error', 'message': 'Método de pagamento não permitido para assinaturas.'},
                            status=status.HTTP_400_BAD_REQUEST)

        if payment.paymentMethod != 'credit_card' and payment.card:
            return Response(
                {'status': 'error', 'message': 'Cartão de crédito não é permitido para esse método de pagamento.'},
                status=status.HTTP_400_BAD_REQUEST)
        return None

    @staticmethod
    def create_subscription(
        payment: Payment,
        current_user: User,
        recurrence_period: int,
        quantity_recurrences: int,
        trial_days: int,
        max_retries: int,
        retry_interval: int,
        amount: float = None,
        initial_status: str = Subscription.STATUS_ACTIVE,
    ) -> Subscription:
        next_payment_date = timezone.now()
        if trial_days > 0:
            next_payment_date += SubscriptionStrategy.get_time_delta_from_interval(time_interval=trial_days)

        amount = amount if amount else payment.amount
        return Subscription.objects.create(
            user=current_user,
            status=initial_status,
            recurrence_period=recurrence_period,
            quantity_recurrences=quantity_recurrences,
            trial_days=trial_days,
            max_retries=max_retries,
            retry_interval=retry_interval,
            next_payment_date=next_payment_date,
            paymentMethod=payment.paymentMethod,
            card=payment.card if payment.paymentMethod == 'credit_card' else None,
            amount=amount,
        )

    @staticmethod
    def check_feature_and_configuration():
        if not switch_is_active('subscription_feature_is_active'):
            return Response(
                {'status': 'error', 'message': 'Feature de assinaturas não está ativa.'},
                status=status.HTTP_412_PRECONDITION_FAILED
            )

        if not settings.DEBUG and switch_is_active('subscription_test_mode'):
            logging.WARN('Subscription test mode is active, on production this switch should be disabled.')

    @abstractmethod
    def process_subscription(
        self,
        payment: Payment,
        current_user: User = None,
        request_data: dict = None,
        subscription: Subscription = None
    ):
        pass

    @abstractmethod
    def execute_subscription(
        self,
        payment: Payment,
        current_user: User = None,
        request_data: dict = None,
        subscription: Subscription = None
    ):
        pass
