from rest_framework.response import Response

from payment.models import Payment
from payment.strategies.subscription.base import SubscriptionStrategy
from user.models import User


class DefaultSubscriptionStrategy(SubscriptionStrategy):
    def execute_subscription(self, payment: Payment, current_user: User = None, request_data: dict = None,
                             subscription=None):
        self.check_feature_and_configuration()

        return self.process_subscription(payment=payment, current_user=current_user, request_data=request_data)

    def process_subscription(self, payment: Payment, current_user: User = None, request_data: dict = None, subscription=None):
        result = self.validate_subscription_fields(request_data=request_data)

        if isinstance(result, Response):
            return result
        else:
            recurrence_period, quantity_recurrences, trial_days, max_retries, retry_interval = result

        validation_error = self.get_payment_method_validation_error(payment=payment)
        if validation_error:
            return validation_error

        subscription = self.create_subscription(
            payment=payment,
            current_user=current_user,
            recurrence_period=recurrence_period,
            quantity_recurrences=quantity_recurrences,
            trial_days=trial_days,
            max_retries=max_retries,
            retry_interval=retry_interval
        )

        return self.process_subscription_payments(subscription, payment)