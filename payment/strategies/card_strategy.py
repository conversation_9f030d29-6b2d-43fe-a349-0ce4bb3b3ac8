import logging
from typing import Callable
from uuid import uuid4

from customer.models import Card
from gateway.company.utils import AntifraudPaymentError, PaymentException
from payment.utils.card import get_card_brand
from .base import PaymentStrategy
from gateway.company import Asaas, Hopypay, Payup, Efi, Abmex, Pagarme, Sumup, NextPayments, PagBank, PicPay, WemaxPay, \
    Medius, MercadoPago
from payment.models import Payment
from ..utils.hopy_formatter import <PERSON>y<PERSON>ormatter, OwemFormatter, Payup<PERSON>ormatter, AsaasFormatter, AbmexFormatter
from ..utils import (
    EfiFormatter,
    PagarmeFormatter,
    SumupFormatter,
    NextPaymentsFormatter,
    PagBankFormatter,
    PicPayFormatter,
    WemaxPayFormatter,
    MediusFormatter,
    MercadoPagoFormatter,
)
from gateway.company.owempay import Owempay
from user.models import User
from gateway.models import Acquirer
from payment.tasks import handleBalance
from payment.strategies.antifraud import (
    BaseAntifraudStrategy,
    Recomendation
)
from payment.services.antifraud_factory import AntifraudStrategyFactory
from django.core.cache import cache
from django.conf import settings


logger = logging.getLogger(__name__)


CREDIT_CARD_TEST_NUMBER = '****************'

class CardStrategy(PaymentStrategy):
    def executeRefund(self, payment_data: Payment) -> Payment:
        return super().executeRefund(payment_data)

    def getGatewaysByPriority(self, payment_data: Payment) -> list[Acquirer]:
        if credit_card_acquirer := payment_data.user.creditCardAcquirer:
            creditCardAcquirerUser = cache.get(f'credit_card_acquirer_{credit_card_acquirer.id}')
            if creditCardAcquirerUser:
                return creditCardAcquirerUser
            cache.set(
                f'credit_card_acquirer_{payment_data.user.creditCardAcquirer.id}',
                [credit_card_acquirer], 60
            )
            return [credit_card_acquirer]

        creditCardAcquirers = cache.get('credit_card_acquirers')
        if creditCardAcquirers:
            return creditCardAcquirers
        else:
            acquirers = [
                acquirer for acquirer in
                Acquirer.objects.filter(enabled=True, creditCardEnabled=True).order_by(
                    "creditCardPriority",
                    "-createdAt"
                )
            ]
            cache.set('credit_card_acquirers', acquirers, 60)
            return acquirers

    def processPayment(self, payment_data: Payment, gateway: Acquirer, card: dict = {}) -> Payment:
        process_methods: dict[str, Callable] = {
            'hopypay': self.process_with_hopypay,
            'owempay': self.process_with_owempay,
            'payup': self.process_with_payup,
            'asaas': self.process_with_asaas,
            'efi': self.process_with_efi,
            'abmex': self.process_with_abmex,
            'sumup': self.process_with_sumup,
            'pagarme': self.process_with_pagarme,
            'nextpayments': self.process_with_nextpayments,
            'pagbank': self.process_with_pagbank,
            'picpay': self.process_with_picpay,
            'wemaxpay': self.process_with_wemaxpay,
            'medius': self.process_with_medius,
            'mercadopago': self.process_with_mercadopago,
        }

        if process_func := process_methods.get(gateway.acquirer_gateway):
            return process_func(payment_data, card)
        return payment_data

    def _antifraud_integration(self) -> BaseAntifraudStrategy | None:
        if cache.get('antifraud_strategy'):
            return cache.get('antifraud_strategy')

        antifraud_strategy = AntifraudStrategyFactory.get_active_strategy_or_none()
        cache.set('antifraud_strategy', antifraud_strategy, 60)

        return antifraud_strategy

    def _request_antifraud_recommendation(self, payment_data: Payment) -> Recomendation | None:
        antifraud = self._antifraud_integration()

        if antifraud is None:
            logger.info(f'Fail to request antifraud recommendation for payment ID: {payment_data.id}')
            return None

        try:
            recommendation: Recomendation = antifraud.get_recomendation(
                payment_data=payment_data
            )
            logger.info(
                f'Antifraud recommendation for payment ID: {payment_data.id};'
                f' Recommendation ID: {recommendation.id} Recommendation OK: {recommendation.is_ok}'
            )
            payment_data.antifraud_recomendation_id = recommendation.id
            payment_data.save()
            return recommendation

        except PaymentException as e:
            logger.error(
                f'_request_antifraud_recommendation api response error: {e.message};'
                f' payment id: {payment_data.id}'
            )
            raise AntifraudPaymentError(e.message, payment_data.id)  # noqa: B904
        except Exception as e:
            logger.error(
                f'_request_antifraud_recommendation unknown error: {e};'
                f' payment id: {payment_data.id}'
            )
            raise AntifraudPaymentError(str(e), payment_data.id)  # noqa: B904



    def _create_antifraud_transaction(
            self,
            payment_data: Payment) -> None:

        antifraud = self._antifraud_integration()

        if antifraud is None:
            return

        if not payment_data.antifraud_recomendation_id:
            raise AntifraudPaymentError(
                'Antifraud recommendation id is null or empty',
                payment_data.id
            )

        try:
            transaction_id = antifraud.create_transaction(
                recomendation_id=payment_data.antifraud_recomendation_id,
                payment_data=payment_data
            )

            payment_data.antifraud_transaction_id = transaction_id
            payment_data.save()

        except PaymentException as e:
            logger.error(
                f'ERROR: _create_antifraud_transaction api response error: {e.message};'
                f' payment id: {payment_data.id}'
            )
            raise AntifraudPaymentError(e.message, payment_data.id)  # noqa: B904

        except Exception as e:
            logger.error(
                f'ERROR: _create_antifraud_transaction unknown error: {e};'
                f' payment id: {payment_data.id}'
            )
            raise AntifraudPaymentError(str(e), payment_data.id)  # noqa: B904

    def sandbox(self, payment: Payment) -> Payment:
        if self.has_additional_flows() and self.has_risk_dilution_flow():
            gateways = self.get_acquirer_randomly_for_risk_dilution(payment)
        else:
            gateways: list[Acquirer] = self.getGatewaysByPriority(payment)

        gateway = gateways[0]

        payment.acquirer = gateway
        payment = self.setInterest(payment)
        payment = self.setFee(payment, gateway)

        sand_box_id = uuid4()

        payment.externalId = f'sandbox_external_id_{sand_box_id}'
        payment.exId = f'sandbox_exId_{sand_box_id}'
        payment.chargeId = f'sandbox_chargeId_{sand_box_id}'
        payment.e2eId = f'sandbox_e2eId_{sand_box_id}'

        payment.status = 'paid'
        payment.save(
            update_fields=[
                'externalId',
                'exId',
                'chargeId',
                'e2eId',
                'status',
                'acquirer',
                'subscription',
            ]
        )
        handleBalance(payment)

        self.afterPayment(payment)

        return payment

    def executePayment(self, payment_data: Payment, card: Card = {}) -> Payment:
        if not payment_data.is_recurrent:
            logger.info(
                f'Starting antifraud process for payment ID: {payment_data.id}'
            )
            antifraud_strategy = self._antifraud_integration()
            if antifraud_strategy is not None:
                recomendation = self._request_antifraud_recommendation(payment_data=payment_data)
                if not recomendation.is_ok:
                    self.set_payment_blocked_by_antifraud(
                        payment_data,
                        f'Pagamento bloqueado pelo antifraude {antifraud_strategy.integration.type}.'
                    )
                    return payment_data
            else:
                logger.info(
                    f'Antifraud strategy not found. Payment ID: {payment_data.id}'
                )

            if self.checkFraud(payment_data):
                if antifraud_strategy is not None:
                    self._create_antifraud_transaction(payment_data)
                return payment_data

        if (
            settings.SANDBOX_ENABLED
            and isinstance(card, Card)
            and card.encryptedNumber.replace(" ", "") == CREDIT_CARD_TEST_NUMBER
        ):
            return self.sandbox(payment_data)

        if self.has_additional_flows() and self.has_risk_dilution_flow():
            gateways = self.get_acquirer_randomly_for_risk_dilution(payment_data)
        else:
            gateways: list[Acquirer] = self.getGatewaysByPriority(payment_data)

        original_amount = payment_data.amount  # Store the original payment amount
        acquirer_that_refused = None

        for gateway in gateways:
            # Save type of acquirer
            payment_data.acquirerType = gateway.acquirer_gateway
            payment_data.acquirer = gateway

            payment_data = self.setInterest(payment_data)

            payment_data = self.setFee(payment_data, gateway)

            try:
                payment_data = self.processPayment(payment_data, gateway, card)

                if payment_data.status != "paid":
                    raise PaymentException(
                        message=f"Pagamento recusado, por favor tente novamente com outro cartão ou meio de pagamento",
                        code=402,
                        type="payment_refused",
                    )
                elif payment_data.status == "paid":
                    break
            except PaymentException as e:
                acquirer_that_refused = gateway
                payment_data.status = 'declined'
                payment_data.reason = e.message
                payment_data.amount = original_amount
                payment_data.log_refused_payment()
            except Exception as e:
                payment_data.status = f'refused'
                payment_data.reason = 'Pagamento recusado, por favor tente novamente com outro cartão ou meio de pagamento'
                payment_data.amount = original_amount

                payment_data.log_refused_payment(
                    reason_message=str(e)
                )

                payment_data.acquirer = acquirer_that_refused
            else:
                # If the payment was successful, break the loop
                break

        payment_data.save()

        handleBalance(payment_data)

        self.afterPayment(payment_data)

        return payment_data

    def afterPayment(self, payment_data: Payment) -> None:
        try:
            if not payment_data.is_recurrent:
                self.associateFraud(payment_data)
                self._create_antifraud_transaction(payment_data)
        except Exception as e:
            logger.error(f'Payment ID: {payment_data.id}; Error in fraud association or transaction creation; message: {e}', exc_info=True)

    def get_gateway_name(self, payment_data: Payment) -> Acquirer:
        # Get the user gateway
        payment_user: User = payment_data.user
        payment_gateway = payment_user.creditCardAcquirer

        # If the user doesn't have a gateway, get the default one
        if not payment_gateway:
            payment_gateway = Acquirer.objects.filter(enabled=True, creditCardEnabled=True).first()

        return payment_gateway

    def process_with_medius(self, payment_data: Payment, card={}) -> Payment:
        medius_data = MediusFormatter.transform_to_format(payment_data)

        medius_api = Medius(payment_data.acquirer)
        response = medius_api.createTransaction(**medius_data)
        # payment_data.status = self.convert_status(response.get('status'))
        # payment_data.externalId = response.get('invoice_id')
        # payment_data.card.brand = response.get('brand', '').lower() if response.get('brand') else None
        # payment_data.card.save()
        return payment_data

    def process_with_wemaxpay(self, payment_data: Payment, card={}) -> Payment:
        wepay_data = WemaxPayFormatter.transform_to_format(payment_data)
        card_data = WemaxPayFormatter.validate_card(payment_data)

        wemax_api = WemaxPay(payment_data.acquirer)
        card_response = wemax_api.validateCard(**card_data)
        response = wemax_api.createCreditCard(**wepay_data)

        if response:
            payment_data.status = self.convert_status(response.get('status'))
            payment_data.externalId = response.get('invoice_id')
            payment_data.card.brand = response.get('brand', '').lower() if response.get('brand') else None
            payment_data.card.save()
        return payment_data

    def process_with_picpay(self, payment_data: Payment, card={}) -> Payment:
        picpay_data = PicPayFormatter.transform_to_format(payment_data)

        picpay_api = PicPay(payment_data.acquirer)
        data = picpay_api.createCreditCardTransaction(**picpay_data)

        transactions = data.get("transactions")[0]

        payment_data.externalId = str(data.get("id"))
        payment_data.status = self.convert_status(transactions.get("transactionStatus"))
        payment_data.reason = transactions.get("errorMessage", None)

        payment_data.card.brand = get_card_brand(payment_data.card.encryptedNumber)
        payment_data.card.save()

        return payment_data

    def process_with_nextpayments(self, payment_data: Payment, card={}) -> Payment:
        nextpayments_data = NextPaymentsFormatter.transform_to_format(payment_data)

        nextpayments_api = NextPayments(payment_data.acquirer)
        data = nextpayments_api.createCreditCardTransaction(**nextpayments_data)

        payment_data.externalId = str(data.get('id'))
        payment_data.chargeId = str(data.get('acquirerTransactionId'))
        payment_data.status = self.convert_status(data.get('status').lower())
        payment_data.reason = data.get('error', None)

        payment_data.card.brand = get_card_brand(payment_data.card.encryptedNumber)
        payment_data.card.save()

        return payment_data

    def process_with_pagbank(self, payment_data: Payment, card={}) -> Payment:
        pagbank_data = PagBankFormatter.transform_to_format(payment_data)

        pagbank_api = PagBank(payment_data.acquirer)
        data = pagbank_api.createTransaction(**pagbank_data)

        payment_data.externalId = str(data.get('id'))
        payment_data.chargeId = str(data.get('charges')[0].get('id'))
        payment_data.status = self.convert_status(data['charges'][0].get('status').lower())
        payment_data.reason = data['charges'][0].get('description', None)

        payment_data.card.brand = get_card_brand(payment_data.card.encryptedNumber)
        payment_data.card.save()

        return payment_data

    def process_with_sumup(self, payment_data: Payment, card={}) -> Payment:
        sumup_data = SumupFormatter.transform_to_checkout_format(payment_data)
        sumup_creditcard_data = SumupFormatter.transform_to_creditcard_format(payment_data)

        sumup_api = Sumup(payment_data.acquirer)
        checkout = sumup_api.createCheckout(**sumup_data)
        data = sumup_api.payWithCreditCard(checkout.get('id'), **sumup_creditcard_data)

        if data.get('next_step'):
            nextStep = data.get('next_step')
            current = data.get('next_step').get('current_transaction')
            payment_data.nextStep = str(nextStep.get('url'))
            payment_data.externalId = str(current.get('id'))
            payment_data.status = self.convert_status(current.get('status').lower())
            payment_data.reason = data.get('description', None)
        else:
            payment_data.externalId = str(data.get('id'))
            payment_data.status = self.convert_status(data.get('status').lower())
            payment_data.reason = data.get('description', None)

        payment_data.card.brand = get_card_brand(payment_data.card.encryptedNumber)
        payment_data.card.save()

        return payment_data

    def process_with_pagarme(self, payment_data: Payment, card={}) -> Payment:
        pagarme_data = PagarmeFormatter.transform_to_format(payment_data)

        pagarme_api = Pagarme(payment_data.acquirer)
        data = pagarme_api.createTransaction(**pagarme_data)
        payment_data.externalId = str(data.get('id'))
        payment_data.chargeId = str(data.get('charges')[0].get('id'))
        payment_data.status = self.convert_status(data.get('status').lower())
        payment_data.reason = data.get('description', None)

        payment_data.card.brand = get_card_brand(payment_data.card.encryptedNumber)
        payment_data.card.save()

        return payment_data

    def process_with_abmex(self, payment_data: Payment, card={}) -> Payment:
        abmex_data = AbmexFormatter.transform_to_abmex_format(payment_data)

        # Create the transaction on Abmex
        abmex_api = Abmex(payment_data.acquirer)
        resp = abmex_api.createTransaction(**abmex_data)
        data = resp.get('data')

        payment_data.externalId = str(data.get('id'))
        payment_data.status = self.convert_status(data.get('status').lower())
        payment_data.reason = data.get('description', None)

        payment_data.card.brand = get_card_brand(payment_data.card.encryptedNumber)
        payment_data.card.save()

        return payment_data

    def process_with_efi(self, payment_data: Payment, card={}) -> Payment:
        efi_data = EfiFormatter.transform_to_format(payment_data)
        # Create the transaction on Owempay
        efi_api = Efi(payment_data.acquirer)
        resp = efi_api.chargeOneStep(**efi_data)
        data = resp.get('data')
        payment_data.externalId = str(data.get('charge_id'))
        payment_data.status = self.convert_status_efi(data.get('status').lower())

        if data.get('refusal'):
            payment_data.reason = data.get('refusal').get('reason')

        payment_data.card.brand = get_card_brand(payment_data.card.encryptedNumber)
        payment_data.card.save()

        return payment_data

    def process_with_hopypay(self, payment_data: Payment, card={}) -> Payment:
        hopysplit_data = HopyFormatter.transform_to_hopysplit_format(payment_data, card)

        # Create the transaction on Hopysplit
        hopypay_api = Hopypay(payment_data.acquirer)
        resp = hopypay_api.createTransaction(**hopysplit_data)

        payment_data.externalId = str(resp.get('id'))
        payment_data.status = self.convert_status(resp.get('status'))
        if resp.get('refusedReason') == 'refused':
            payment_data.reason = resp.get('refusedReason').get('description')
            payment_data.refusedByAntifraud = resp.get('refusedReason').get('antifraud')
        # card
        card = payment_data.card
        card.holderName = resp.get('card', {}).get('holderName')
        card.lastDigits = resp.get('card', {}).get('lastDigits')
        card.brand = resp.get('card', {}).get('brand')
        card.save()

        return payment_data

    def process_with_asaas(self, payment_data: Payment, card={}) -> Payment:  # Create the transaction on Asaas
        asaas_api = Asaas(payment_data.acquirer)

        # Create the customer on Asaas
        asaas_customer_data = AsaasFormatter.transform_to_asaas_customer(payment_data.customer)
        customer = asaas_api.createCustomer(**asaas_customer_data)

        # Create the transaction on Asaas
        asaas_data = AsaasFormatter.transform_to_asaas_format(payment_data, customer.get('id'), card)
        resp = asaas_api.createTransaction(**asaas_data)

        capture = asaas_api.captureTransaction(resp.get('id'), card)
        payment_data.externalId = str(capture.get('id'))
        payment_data.status = self.convert_status(capture.get('status').lower())

        payment_data.card.brand = get_card_brand(payment_data.card.encryptedNumber)
        payment_data.card.save()

        return payment_data

    def process_with_owempay(self, payment_data: Payment, card={}) -> Payment:
        owempay_data = OwemFormatter.transform_to_owempay_format(payment_data, card)
        # Create the transaction on Owempay
        owempay_api = Owempay(payment_data.acquirer)
        resp = owempay_api.createTransaction(**owempay_data)

        payment_data.externalId = str(resp.get('id'))
        payment_data.status = self.convert_status(resp.get('status').lower())

        payment_data.card.brand = get_card_brand(payment_data.card.encryptedNumber)
        payment_data.card.save()

        return payment_data

    def process_with_payup(self, payment_data: Payment, card={}) -> Payment:
        payup_data = PayupFormatter.transform_to_payup_format(payment_data, card)

        # Create the transaction on Owempay
        owempay_api = Payup(payment_data.acquirer)
        resp = owempay_api.createTransaction(**payup_data)

        payment_data.externalId = str(resp.get('transactionId'))
        payment_data.status = 'paid' if resp.get('status').get('result') == 0 else 'declined'
        payment_data.reason = resp.get('status').get('msg')

        # card
        card = payment_data.card
        brand = resp.get('brand') or {}
        cc = resp.get('creditCard') or {}
        card.holderName = cc.get('holderName')
        card.lastDigits = cc.get('cardNumber', '')[-4:]
        card.brand = brand.get('name', '').lower()
        card.save()

        return payment_data

    def process_with_mercadopago(self, payment: Payment, card={}) -> Payment:
        mercadopago = MercadoPago(payment.acquirer)

        mercadopago_card_data = MercadoPagoFormatter.transform_to_credit_card(payment)
        card_response = mercadopago.createCardToken(**mercadopago_card_data)

        payment.card.externalToken = card_response.get('id')
        payment.card.brand = get_card_brand(payment.card.encryptedNumber)
        payment.card.save()

        mercadopago_data = MercadoPagoFormatter.transform_to_format(payment)
        resp = mercadopago.createTransaction(payment.id, payment.mercadopago.deviceId, **mercadopago_data)

        payment.externalId = str(resp.get("id"))
        payment.status = self.convert_status(resp.get('status').lower())

        return payment
