from gateway.company.mercadopago import MercadoPago
from gateway.models import Acquirer
from payment.utils.payment_utils import mark_payment_as_paid, mark_payment_with_status
from user.models import WebhookLog
from .base import WebhookStategy
from ...models import Payment

class MercadoPagoWebhook(WebhookStategy):
    def handle_webhook(self, data):
        WebhookLog.objects.create(data=data)

        if data.get("type") not in ["payment", "refund", "topic_chargebacks_wh"]:
            return

        paymentId = data.get("data", {}).get("id")
        if not paymentId:
            return

        mercadopago = MercadoPago(Acquirer.objects.get(acquirer_gateway='mercadopago'))
        paymentInfo = mercadopago.getPaymentInfo(paymentId)
        if not paymentInfo:
            return

        payment = Payment.objects.filter(externalId=paymentInfo.get("id")).first()
        if not payment:
            return

        if data.get("type") == "payment":
            if data.get("type") == "payment" and paymentInfo.get("status") == "approved":
                mark_payment_as_paid(payment, self.handle_balance)
            elif data.get("type") == "payment" and paymentInfo.get("status") == "refunded":
                mark_payment_with_status(payment, self.handle_balance, "refunded")
            elif data.get("type") == "payment" and paymentInfo.get("status") == "in_mediation":
                payment.chargeback()
        elif data.get("type") == "topic_chargebacks_wh":
            payment.chargeback()
