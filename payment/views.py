from rest_framework.decorators import action
from rest_framework.generics import ListAPIView
from rest_framework.permissions import AllowAny, IsA<PERSON>enticated, IsAdminUser

from rest_framework import filters, generics, pagination, status, viewsets
from rest_framework.views import APIView
from django.db.models import Q, Sum, Case, When, Value, F, Count, DecimalField, ExpressionWrapper

from gateway.models import Settings
from gateway.company.utils import AntifraudPaymentError, RefundException
from payment.services.webhook_factory import WebhookStrategyFactory

from user.permissions import AddPaymentsPermission, ChangePaymentsPermission, DeletePaymentsPermission, \
    DenyAllPermission, FullAccessPermission, ManageReportsPermission, ViewPaymentsPermission, \
    PaymentStatusChangeLogListViewPermission, ManagePaymentsPermission, SuperUserPermission, \
    ReprocessPaymentPermission
from .models import ApplePay, GooglePay, OpenFinanceNubank, Payment, Split, ReportDownload, PaymentDisputeArea, PaymentDisputeUploadedFile, \
    Subscription, AdditionalFlows, PaymentReprocessingHistory
from .serializers import PaymentAdminSerializer, PaymentSerializer, PaymentCreateSerializer, PaymentSellerSerializer, \
    PaymentReportSerializer, BillingSerializer, ReportDownloadSerializer, \
    PaymentDisputeSerializer, PaymentDisputeUploadedFileSerializer, SubscriptionSerializer, \
    SubscriptionUpdateSerializer, PaymentStatusChangeLogSerializer, AdditionalFlowSerializer, \
    PaymentReprocessingHistorySerializer
from rest_framework.response import Response
from customer.models import Customer, Card, Address
from .services.payment_factory import PaymentStrategyFactory
from rest_framework.viewsets import ReadOnlyModelViewSet
from django_filters.rest_framework import DjangoFilterBackend
from drf_excel.mixins import XLSXFileMixin
from user.models import User
from .services.subscription_factory import SubscriptionFactory
from .strategies.base import PaymentStrategy
from .filters import PaymentFilterset, PaymentReportFilter, SubscriptionFilter
import uuid
from drf_excel.renderers import XLSXRenderer
from rest_framework_csv.renderers import CSVRenderer
from rest_framework.exceptions import ValidationError
from rest_flex_fields.views import FlexFieldsMixin
from django.shortcuts import get_object_or_404
from payment.tasks import handleWebhooks, generate_report, schedule_payment, schedule_payment_check
from payment.models import MercadoPago
from django.http import HttpResponse

from django.core.cache import cache
from datetime import datetime
from rest_flex_fields import is_expanded
from customer.serializers import CardSerializer
from django.db import transaction

from .strategies.subscription.base import SubscriptionStrategy


class PaymentViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    ordering_fields = ['items', 'status', 'acquirerType', 'paymentMethod', 'createdAt', 'chargedbackAt', 'amount']
    filterset_class = PaymentFilterset

    search_fields = (
        'id',
        'externalId',
        'e2eId',
        'exId',
        'user__email',
        'customer__phone',
        'customer__docNumber',
        'customer__email',
    )

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = []
        if self.request.user.is_staff or self.request.user.is_superuser:
            if self.action in ['list', 'retrieve']:
                necessary_permissions = [
                    IsAdminUser(),
                    FullAccessPermission(),
                    ViewPaymentsPermission(),
                    ManagePaymentsPermission()
                ]
            elif self.action == 'create':
                necessary_permissions = [
                    IsAdminUser(),
                    FullAccessPermission(),
                    AddPaymentsPermission()
                ]
            elif self.action in ['update', 'refund']:
                necessary_permissions = [
                    IsAdminUser(),
                    FullAccessPermission(),
                    ChangePaymentsPermission(),
                    ManagePaymentsPermission()
                ]
            elif self.action == 'destroy':
                necessary_permissions = [
                    IsAdminUser(),
                    FullAccessPermission(),
                    DeletePaymentsPermission()
                ]

            has_permissions = [
                permission for permission in necessary_permissions
                if permission.has_permission(self.request, self)
            ]

            if has_permissions:
                permissions.extend(has_permissions)
            else:
                permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        if self.request.user.is_superuser or (self.request.user.is_staff and self.request.user.has_perm('payment.view_payments') or self.request.user.has_perm('payment.manage_payments')):
            if self.request.GET.get('user'):
                return Payment.objects.filter(
                    Q(user=self.request.GET.get('user')) |
                    Q(splits__user=self.request.GET.get('user'))
                ).order_by('-createdAt')
            return Payment.objects.all().order_by('-createdAt')
        return Payment.objects.filter(
            Q(user=self.request.user) |
            Q(splits__user=self.request.user)
        ).order_by('-createdAt')

    def get_serializer_class(self):
        if self.request.user.is_superuser:
            return PaymentAdminSerializer
        return PaymentSerializer

    def create(self, request, *args, **kwargs):
        return Response({'error': 'Method not allowed'}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response({'error': 'Method not allowed'}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @action(detail=True, methods=['post'])
    def refund(self, request, pk=None):
        payment = self.get_object()
        if not payment.can_refund():
            return Response({'detail': 'Este pagamento não pode ser reembolsado porque não foi pago'}, status=status.HTTP_400_BAD_REQUEST)

        if not payment.can_be_refunded():
            return Response({'detail': 'Este pagamento não pode ser reembolsado porque já foi reembolsado'}, status=status.HTTP_400_BAD_REQUEST)
        else:
            try:
                payment_strategy: PaymentStrategy = PaymentStrategyFactory.get_refund_strategy()
                payment_refunded = payment_strategy.executeRefund(payment)
            except RefundException as e:
                return Response({'detail': str(e.message)}, status=status.HTTP_400_BAD_REQUEST)

            if payment_refunded.status != 'refunded':
                return Response({'detail': 'Erro ao realizar reembolso'}, status=status.HTTP_400_BAD_REQUEST)

        return Response({'detail': 'Reembolso realizado com sucesso'}, status=status.HTTP_200_OK)


class PaymentCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        current_user = request.user
        request.data['user'] = current_user.id

        if current_user.is_superuser and (seller_id := request.data.get('seller_id')):
            request.data['user'] = seller_id
            current_user = User.objects.get(id=seller_id)

        app_settings = Settings.get_or_cache('settings')
        if current_user.status in ['approved', 'pre_approved'] or (
            current_user.status not in ['blocked', 'rejected'] and app_settings.canSellWithoutApproval
        ) or (
            current_user.status in ['gateway_pending', 'resubmission_requested'] and app_settings.canSellOnIntermediateStatus
        ):
            serializer = PaymentCreateSerializer(data=request.data)
            if serializer.is_valid():
                validated_data = serializer.validated_data
                payment_instance: Payment = self.to_payment_model(
                    validated_data=validated_data,
                    current_user=current_user
                )

                if (payment_instance.paymentMethod == 'credit_card' and request.user.creditCard ) or \
                    (payment_instance.paymentMethod == 'boleto' and request.user.boleto) or \
                    (payment_instance.paymentMethod == 'pix' and request.user.pix) or \
                    (payment_instance.paymentMethod == 'picpay' and request.user.picpay) or \
                    (payment_instance.paymentMethod == 'nupay' and request.user.nupay) or \
                    (payment_instance.paymentMethod == 'googlepay' and request.user.googlepay) or \
                    (payment_instance.paymentMethod == 'applepay' and request.user.applepay):
                    return Response(
                        {
                            'status': 'error',
                            'message': 'Esse método de pagamento não está disponível para esse vendedor.'
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

                payment_type = request.data.get('type', 'default')
                if payment_type == 'subscription':
                    subscription_strategy: SubscriptionStrategy = SubscriptionFactory.get_strategy(
                        'default'
                    )
                    return subscription_strategy.execute_subscription(
                        payment=payment_instance,
                        current_user=current_user,
                        request_data=request.data
                    )

                # In case of a regular payment, process it
                payment_strategy: PaymentStrategy = PaymentStrategyFactory.get_strategy(
                    payment_instance.paymentMethod
                )
                try:
                    created_payment = payment_strategy.executePayment(
                        payment_instance,
                        payment_instance.card
                    )
                except AntifraudPaymentError as e:
                    print(
                        f'AntifraudPaymentError: uuid: {e.error_uuid}; payment id: {e.payment_id};'
                        f' message: {e.message}'
                    )
                    return Response(
                        {
                            'status': 'error',
                            'message': (
                                f'Falha ao realizar o pagamento. Referência do erro: {e.error_uuid}'
                            )
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )


                payment_serializer = PaymentSerializer(created_payment)

                return Response(
                    {
                        'status': 'success',
                        'data': payment_serializer.data
                    },
                    status=status.HTTP_201_CREATED
                )

            return Response(
                {
                    'status': 'error',
                    'errors': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response(
            {
                'status': 'error',
                'message': 'Esse produto ainda não está disponível para venda.'
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    def process_items(self, items: list[dict], user: User, payment: Payment) -> list[dict]:
        for item in items:
            if subscription_data := item.get("subscription"):
                self._process_subscription_item(item, subscription_data, user, payment)

        return items

    @staticmethod
    def _get_subscription_bulk_strategy():
        from payment.services.subscription_factory import SubscriptionFactory
        return SubscriptionFactory.get_strategy('bulk')

    def _process_subscription_item(self, item: dict, subscription_data: dict, user: User, payment: Payment):
        subscription_strategy = self._get_subscription_bulk_strategy()
        subscription_id = subscription_strategy.create_subscription_from_item(
            subscription_data=subscription_data, payment=payment, user=user
        )
        item.update({"subscription": subscription_id})

    @staticmethod
    def get_or_create_customer_address(customer_address_data: dict, customer: Customer) -> Address:
        customer_address_data.pop('country', None)

        address, _ = Address.objects.get_or_create(
            customer=customer,
            defaults=customer_address_data
        )
        return address

    @staticmethod
    def update_or_create_customer(user, customer_data: dict, payment: Payment) -> Customer:
        doc_number = customer_data.get('docNumber')

        try:
            customer, _ = Customer.objects.update_or_create(
                user=user, docNumber=doc_number, defaults=customer_data
            )
        except Customer.MultipleObjectsReturned:
            Customer.objects.filter(user=user, docNumber=doc_number).update(**customer_data)
            customer = Customer.objects.filter(user=user, docNumber=doc_number).first()

        payment.customer = customer
        return customer

    def to_payment_model(self, validated_data, current_user: User):
        payment = Payment(
            user=current_user,
            amount=validated_data.get('amount'),
            baseAmount=validated_data.get('amount'),
            paymentMethod=validated_data.get('paymentMethod'),
            exId=validated_data.get('exId'),
            checkoutUrl=validated_data.get('checkoutUrl'),
            refererUrl=validated_data.get('refererUrl'),
            feeByItem=validated_data.get('feeByItem'),
            antifraud_profiling_attempt_reference=validated_data.get(
                'antifraud_profiling_attempt_reference'
            ),
        )

        # Customer data
        if customer_data := validated_data.get('customer'):
            customer = self.update_or_create_customer(
                user=self.request.user,
                customer_data=customer_data,
                payment=payment
            )

        # Card data
        if validated_data.get('paymentMethod') == 'credit_card' and validated_data.get('card'):
            if validated_data.get('card').get('token'):
                card = Card.objects.get(token=validated_data.get('card').get('token'))
            else:
                card, created = Card.objects.get_or_create(
                    customer=customer,
                    token=str(uuid.uuid4()),
                    defaults={
                        'holderName': validated_data.get('card').get('holderName'),
                        'number': validated_data.get('card').get('number'),
                        'encryptedNumber': validated_data.get('card').get('number'),
                        'cvv': validated_data.get('card').get('cvv'),
                        'lastDigits': validated_data.get('card').get('number')[-4:] if isinstance(validated_data.get('card').get('number'), str) else '',
                        'expMonth': validated_data.get('card').get('expMonth'),
                        'expYear': validated_data.get('card').get('expYear'),
                        'expirationDate': validated_data.get('card').get('expMonth') + '/' + validated_data.get('card').get('expYear'),
                        'externalToken': validated_data.get('card').get('externalToken'),
                    }
                )
            payment.card = card
            payment.installments = validated_data.get('installments')

        # GooglePay data
        if validated_data.get('paymentMethod') == 'googlepay':
            googlepay_data = validated_data.get('googlepay')
            payment.googlepay, created = GooglePay.objects.get_or_create(
                **googlepay_data,
            )

        # ApplePay data
        if validated_data.get('paymentMethod') == 'applepay':
            applepay_data = validated_data.get('applepay')
            payment.applepay, created = ApplePay.objects.get_or_create(
                **applepay_data,
            )

        # OpenFinance Nubank data
        if validated_data.get('paymentMethod') == 'openfinance_nubank':
            if openfinance_nubank_data := validated_data.get('openfinance_nubank'):
                payment.openFinanceNubank, created = OpenFinanceNubank.objects.get_or_create(
                    **openfinance_nubank_data,
                )

        # Mercado Pago data
        if mercadopago_data := validated_data.get('mercadopago'):
            payment.mercadopago, created = MercadoPago.objects.get_or_create(
                **mercadopago_data,
            )

        # Expiration date
        if validated_data.get('expiresInDays'):
            payment.expiresInDays = validated_data.get('expiresInDays')
        if validated_data.get('postbackUrl'):
            payment.postbackUrl = validated_data.get('postbackUrl')

        # Address data
        if address_data := validated_data.get('address'):
            address = self.get_or_create_customer_address(customer_address_data=address_data, customer=customer)
            payment.address = address

        # Items validation
        items = self.process_items(
            items=validated_data.get('items'),
            user=current_user,
            payment=payment
        )
        payment.items = items
        payment.save()

        affiliate = validated_data.get('affiliate')
        if payment.user.affiliate:
            affiliate = {
                'user': payment.user.affiliate,
                'percentage': payment.user.affiliate.percentage
            }

        if affiliate:
            affiliateAmount = payment.liquidAmount * (affiliate.get('percentage')/100)
            affiliateSplit = Split(
                payment=payment,
                user=affiliate.get('user'),
                type='affiliate',
                amount=affiliateAmount,
                amountReserve=affiliateAmount,
                rawPercentage=affiliate.get('percentage'),
                percentage=affiliate.get('percentage')
            )
            affiliateSplit.save()
            payment.affiliateSplit = affiliateSplit
            payment.splits.add(affiliateSplit)

        payment.producerSplit = Split(
            payment=payment,
            user=payment.user,
            type='producer',
            amount=0,
            amountReserve=0,
            percentage=0
        )
        payment.producerSplit.save()
        payment.splits.add(payment.producerSplit)

        if splits := validated_data.get('splits'):
            split_models = [
                Split(
                    payment=payment,
                    user_id=split['seller_id'],
                    type='coproducer',
                    amount=payment.liquidAmount * split['percentage'] / 100,
                    amountReserve=payment.liquidAmount * split['percentage'] / 100,
                    rawPercentage=split['percentage'],
                    percentage=split['percentage']
                )
                for split in splits
            ]
            Split.objects.bulk_create(split_models)
            payment.splits.add(*split_models)

        return payment


class PaymentStatusChangeLogViewSet(ListAPIView):
    serializer_class = PaymentStatusChangeLogSerializer
    permission_classes = [PaymentStatusChangeLogListViewPermission]

    def get_queryset(self):
        payment_id = self.kwargs.get('pk')
        payment = get_object_or_404(Payment, id=payment_id)
        return payment.status_change_logs.all()



class SellerSalesListView(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = PaymentSellerSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    ordering_fields = ['items', 'status', 'paymentMethod', 'createdAt', 'amount']
    filterset_class = PaymentFilterset

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = []

        if self.action in ['list', 'retrieve', 'update', 'partial_update', 'refund', 'chargeback']:
            necessary_permissions = [
                FullAccessPermission(),
                ViewPaymentsPermission(),
                ManagePaymentsPermission()
            ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        if self.request.user.is_superuser or self.request.user.is_staff:
            return Payment.objects.all().order_by('-createdAt')
        else:
            return Payment.objects.filter(Q(splits__user=self.request.user) | Q(user=self.request.user)).order_by('-createdAt')

    @staticmethod
    def refund_payment(payment: Payment):
        try:
            payment_strategy: PaymentStrategy = PaymentStrategyFactory.get_refund_strategy()
            payment_refunded = payment_strategy.executeRefund(payment)
        except RefundException as e:
            return Response({'message': str(e.message)}, status=status.HTTP_400_BAD_REQUEST)

        if payment_refunded.status != 'refunded':
            return Response({'message': 'Erro ao realizar reembolso'}, status=status.HTTP_400_BAD_REQUEST)

        return Response({'status': 'success', 'data': PaymentSellerSerializer(payment).data}, status=status.HTTP_200_OK)

    @staticmethod
    def chargeback_payment(
        newStatus: str,
        payment: Payment,
        amount: float,
        user: User,
        reason: str
    ):
        if not reason and newStatus == 'chargeback':
            return Response({'message': 'Motivo do chargeback é obrigatório'}, status=status.HTTP_400_BAD_REQUEST)

        payment.chargeback(amount)

        if payment.status not in ['chargedback', 'MED']:
            return Response({'message': 'Erro ao realizar chargeback'}, status=status.HTTP_400_BAD_REQUEST)

        chargeback_log = payment.status_change_logs.filter(changed_to_status__in=['chargedback', 'MED']).first()
        chargeback_log.user = user
        chargeback_log.reason = reason
        chargeback_log.system = False
        chargeback_log.save(update_fields=['user', 'reason', 'system'])

        return Response({'status': 'success', 'data': PaymentSellerSerializer(payment).data}, status=status.HTTP_200_OK)


    @action(detail=True, methods=['post'])
    def chargeback(self, request, pk=None):
        if request.user.is_superuser or request.user.is_staff:
            payment = get_object_or_404(Payment, id=pk)
        else:
            payment = get_object_or_404(Payment, id=pk, user=self.request.user)

        if request.data.get('status') == 'refunded' and payment.can_refund() and payment.can_be_refunded():
            return self.refund_payment(payment)
        elif request.data.get('status') == 'prechargeback' or request.data.get('status') == 'chargedback':
            return self.chargeback_payment(
                request.data.get('status'),
                payment=payment,
                amount=request.data.get('amount'),
                user=request.user,
                reason=request.data.get('reason')
            )
        return Response({'message': 'Ação inválida'}, status=status.HTTP_400_BAD_REQUEST)


class WebhookAPIView(APIView):
    permission_classes = [AllowAny]

    def post(self, request, acquirer):
        factory = WebhookStrategyFactory.get_strategy(acquirer)
        factory.set_headers(request.headers)
        factory.set_query_params(request.query_params)
        factory.handle_webhook(request.data)

        return Response({'status': 'ok'})


class ReportAPIView(XLSXFileMixin, ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = PaymentReportSerializer
    renderer_classes = (XLSXRenderer,)
    filename = 'report_transactions.xlsx'
    pagination_class = None

    column_header = {
        'titles': [
            'ID',
            'ID Externo',
            'Email do Vendedor',
            'Nome do Vendedor',
            'Valor',
            'Items',
            'Status',
            'Nome do Cliente',
            'Email do Cliente',
            'Telefone do Cliente',
            'Documento do Cliente',
            'Tipo do Documento',
            'Data de Criação',
        ]
    }

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            IsAdminUser(),
            FullAccessPermission(),
            ManageReportsPermission()
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        if self.request.user.is_superuser:
            user = self.request.user
            queryset = Payment.objects.all()

            status = self.request.query_params.get('status')
            if status:
                status_list = status.split(',')
                queryset = queryset.filter(status__in=status_list)

            if self.request.query_params.get('acquirerType'):
                queryset = queryset.filter(acquirerType=self.request.query_params.get('acquirerType'))
            if self.request.query_params.get('createdAt__gte'):
                queryset = queryset.filter(createdAt__gte=self.request.query_params.get('createdAt__gte'))
            if self.request.query_params.get('createdAt__lte'):
                queryset = queryset.filter(createdAt__lte=self.request.query_params.get('createdAt__lte'))

            return queryset


class ResendWebhookAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        payment = get_object_or_404(Payment, id=pk)
        handleWebhooks(payment)

        return Response({'status': 'ok'}, status=status.HTTP_200_OK)


class BillingViewSet(FlexFieldsMixin, ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]

    queryset = User.objects.all()
    serializer_class = BillingSerializer

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            IsAdminUser(),
            FullAccessPermission(),
            ManageReportsPermission()
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date is None or end_date is None:
            raise ValidationError("Start date and end date must be provided.")

        queryset = User.objects.annotate(
            total_value=Sum(Case(
                When(payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            total_count=Count('payments__id', filter=Q(payments__createdAt__date__range=[start_date, end_date])),
            card_value=Sum(Case(
                When(payments__paymentMethod='credit_card', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            card_count=Count('payments__id', filter=Q(payments__paymentMethod='credit_card', payments__createdAt__date__range=[start_date, end_date])),
            credit_card_percent=ExpressionWrapper(F('card_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            boleto_value=Sum(Case(
                When(payments__paymentMethod='boleto', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            boleto_count=Count('payments__id', filter=Q(payments__paymentMethod='boleto', payments__createdAt__date__range=[start_date, end_date])),
            boleto_percent=ExpressionWrapper(F('boleto_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            pix_value=Sum(Case(
                When(payments__paymentMethod='pix', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            pix_count=Count('payments__id', filter=Q(payments__paymentMethod='pix', payments__createdAt__date__range=[start_date, end_date])),
            pix_percent=ExpressionWrapper(F('pix_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            picpay_value=Sum(Case(
                When(payments__paymentMethod='picpay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            picpay_count=Count('payments__id', filter=Q(payments__paymentMethod='picpay', payments__createdAt__date__range=[start_date, end_date])),
            picpay_percent=ExpressionWrapper(F('picpay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            nupay_value=Sum(Case(
                When(payments__paymentMethod='nupay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            nupay_count=Count('payments__id', filter=Q(payments__paymentMethod='nupay', payments__createdAt__date__range=[start_date, end_date])),
            nupay_percent=ExpressionWrapper(F('nupay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            googlepay_value=Sum(Case(
                When(payments__paymentMethod='googlepay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            googlepay_count=Count('payments__id', filter=Q(payments__paymentMethod='googlepay', payments__createdAt__date__range=[start_date, end_date])),
            googlepay_percent=ExpressionWrapper(F('googlepay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            applepay_value=Sum(Case(
                When(payments__paymentMethod='applepay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            applepay_count=Count('payments__id', filter=Q(payments__paymentMethod='applepay', payments__createdAt__date__range=[start_date, end_date])),
            applepay_percent=ExpressionWrapper(F('applepay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            openfinance_nubank_value=Sum(Case(
                When(payments__paymentMethod='openfinance_nubank', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            openfinance_nubank_count=Count('payments__id', filter=Q(payments__paymentMethod='openfinance_nubank', payments__createdAt__date__range=[start_date, end_date])),
            openfinance_nubank_percent=ExpressionWrapper(F('openfinance_nubank_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            chargeback_value=Sum(Case(
                When(payments__status='chargedback', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            chargeback_count=Count('payments__id', filter=Q(payments__status='chargedback', payments__createdAt__date__range=[start_date, end_date])),
            chargeback_percent=ExpressionWrapper(F('chargeback_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            average_ticket=Sum(Case(
                When(payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )) / Count('payments__id', filter=Q(payments__createdAt__date__range=[start_date, end_date])),
            refusal_value=Sum(Case(
                When(payments__status='refused', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            refusal_count=Count('payments__id', filter=Q(payments__status='refused', payments__createdAt__date__range=[start_date, end_date])),
            refusal_percent=ExpressionWrapper(F('refusal_value') * 100.0 / F('total_value'), output_field=DecimalField()),
        )

        return queryset


class BillingExportCSVView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    queryset = User.objects.all()
    serializer_class = BillingSerializer
    renderer_classes = [CSVRenderer]
    pagination_class = None

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            IsAdminUser(),
            FullAccessPermission(),
            ManageReportsPermission()
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date is None or end_date is None:
            raise ValidationError("Start date and end date must be provided.")

        queryset = User.objects.annotate(
            total_value=Sum(Case(
                When(payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            total_count=Count('payments__id', filter=Q(payments__createdAt__date__range=[start_date, end_date])),
            card_value=Sum(Case(
                When(payments__paymentMethod='credit_card', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            card_count=Count('payments__id', filter=Q(payments__paymentMethod='credit_card', payments__createdAt__date__range=[start_date, end_date])),
            credit_card_percent=ExpressionWrapper(F('card_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            boleto_value=Sum(Case(
                When(payments__paymentMethod='boleto', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            boleto_count=Count('payments__id', filter=Q(payments__paymentMethod='boleto', payments__createdAt__date__range=[start_date, end_date])),
            boleto_percent=ExpressionWrapper(F('boleto_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            pix_value=Sum(Case(
                When(payments__paymentMethod='pix', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            pix_count=Count('payments__id', filter=Q(payments__paymentMethod='pix', payments__createdAt__date__range=[start_date, end_date])),
            pix_percent=ExpressionWrapper(F('pix_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            picpay_value=Sum(Case(
                When(payments__paymentMethod='picpay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            picpay_count=Count('payments__id', filter=Q(payments__paymentMethod='picpay', payments__createdAt__date__range=[start_date, end_date])),
            picpay_percent=ExpressionWrapper(F('picpay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            nupay_value=Sum(Case(
                When(payments__paymentMethod='nupay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            nupay_count=Count('payments__id', filter=Q(payments__paymentMethod='nupay', payments__createdAt__date__range=[start_date, end_date])),
            nupay_percent=ExpressionWrapper(F('nupay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            googlepay_value=Sum(Case(
                When(payments__paymentMethod='googlepay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            googlepay_count=Count('payments__id', filter=Q(payments__paymentMethod='googlepay', payments__createdAt__date__range=[start_date, end_date])),
            googlepay_percent=ExpressionWrapper(F('googlepay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            applepay_value=Sum(Case(
                When(payments__paymentMethod='applepay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            applepay_count=Count('payments__id', filter=Q(payments__paymentMethod='applepay', payments__createdAt__date__range=[start_date, end_date])),
            applepay_percent=ExpressionWrapper(F('applepay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            openfinance_nubank_value=Sum(Case(
                When(payments__paymentMethod='openfinance_nubank', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            openfinance_nubank_count=Count('payments__id', filter=Q(payments__paymentMethod='openfinance_nubank', payments__createdAt__date__range=[start_date, end_date])),
            openfinance_nubank_percent=ExpressionWrapper(F('openfinance_nubank_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            chargeback_value=Sum(Case(
                When(payments__status='chargedback', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            chargeback_count=Count('payments__id', filter=Q(payments__status='chargedback', payments__createdAt__date__range=[start_date, end_date])),
            chargeback_percent=ExpressionWrapper(F('chargeback_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            average_ticket=Sum(Case(
                When(payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )) / Count('payments__id', filter=Q(payments__createdAt__date__range=[start_date, end_date])),
            refusal_value=Sum(Case(
                When(payments__status='refused', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            refusal_count=Count('payments__id', filter=Q(payments__status='refused', payments__createdAt__date__range=[start_date, end_date])),
            refusal_percent=ExpressionWrapper(F('refusal_value') * 100.0 / F('total_value'), output_field=DecimalField()),
        )

        return queryset

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        response['Content-Disposition'] = 'attachment; filename="company_billing_report.csv"'
        return response


class BillingExportXLSXView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    queryset = User.objects.all()
    serializer_class = BillingSerializer
    renderer_classes = [XLSXRenderer]
    pagination_class = None

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            IsAdminUser(),
            FullAccessPermission(),
            ManageReportsPermission()
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date is None or end_date is None:
            raise ValidationError("Start date and end date must be provided.")

        queryset = User.objects.annotate(
            total_value=Sum(Case(
                When(payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            total_count=Count('payments__id', filter=Q(payments__createdAt__date__range=[start_date, end_date])),
            card_value=Sum(Case(
                When(payments__paymentMethod='credit_card', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            card_count=Count('payments__id', filter=Q(payments__paymentMethod='credit_card', payments__createdAt__date__range=[start_date, end_date])),
            credit_card_percent=ExpressionWrapper(F('card_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            boleto_value=Sum(Case(
                When(payments__paymentMethod='boleto', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            boleto_count=Count('payments__id', filter=Q(payments__paymentMethod='boleto', payments__createdAt__date__range=[start_date, end_date])),
            boleto_percent=ExpressionWrapper(F('boleto_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            pix_value=Sum(Case(
                When(payments__paymentMethod='pix', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            pix_count=Count('payments__id', filter=Q(payments__paymentMethod='pix', payments__createdAt__date__range=[start_date, end_date])),
            pix_percent=ExpressionWrapper(F('pix_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            picpay_value=Sum(Case(
                When(payments__paymentMethod='picpay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            picpay_count=Count('payments__id', filter=Q(payments__paymentMethod='picpay', payments__createdAt__date__range=[start_date, end_date])),
            picpay_percent=ExpressionWrapper(F('picpay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            nupay_value=Sum(Case(
                When(payments__paymentMethod='nupay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            nupay_count=Count('payments__id', filter=Q(payments__paymentMethod='nupay', payments__createdAt__date__range=[start_date, end_date])),
            nupay_percent=ExpressionWrapper(F('nupay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            googlepay_value=Sum(Case(
                When(payments__paymentMethod='googlepay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            googlepay_count=Count('payments__id', filter=Q(payments__paymentMethod='googlepay', payments__createdAt__date__range=[start_date, end_date])),
            googlepay_percent=ExpressionWrapper(F('googlepay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            applepay_value=Sum(Case(
                When(payments__paymentMethod='applepay', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            applepay_count=Count('payments__id', filter=Q(payments__paymentMethod='applepay', payments__createdAt__date__range=[start_date, end_date])),
            applepay_percent=ExpressionWrapper(F('applepay_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            openfinance_nubank_value=Sum(Case(
                When(payments__paymentMethod='openfinance_nubank', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            openfinance_nubank_count=Count('payments__id', filter=Q(payments__paymentMethod='openfinance_nubank', payments__createdAt__date__range=[start_date, end_date])),
            openfinance_nubank_percent=ExpressionWrapper(F('openfinance_nubank_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            chargeback_value=Sum(Case(
                When(payments__status='chargedback', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            chargeback_count=Count('payments__id', filter=Q(payments__status='chargedback', payments__createdAt__date__range=[start_date, end_date])),
            chargeback_percent=ExpressionWrapper(F('chargeback_value') * 100.0 / F('total_value'), output_field=DecimalField()),
            average_ticket=Sum(Case(
                When(payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )) / Count('payments__id', filter=Q(payments__createdAt__date__range=[start_date, end_date])),
            refusal_value=Sum(Case(
                When(payments__status='refused', payments__createdAt__date__range=[start_date, end_date], then=F('payments__amount')),
                default=Value(0),
                output_field=DecimalField(),
            )),
            refusal_count=Count('payments__id', filter=Q(payments__status='refused', payments__createdAt__date__range=[start_date, end_date])),
            refusal_percent=ExpressionWrapper(F('refusal_value') * 100.0 / F('total_value'), output_field=DecimalField()),
        )

        return queryset

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        response['Content-Disposition'] = 'attachment; filename="company_billing_report.xlsx"'
        return response

class RequestReportDownloadView(APIView):
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            IsAdminUser(),
            FullAccessPermission(),
            ManageReportsPermission()
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get(self, request, *args, **kwargs):
        filtered_queryset = PaymentReportFilter(request.GET, queryset=Payment.objects.all()).qs

        if filtered_queryset.count() == 0:
            return Response({'error': 'No payments found matching the filters provided.'}, status=status.HTTP_400_BAD_REQUEST)

        report = ReportDownload.objects.create(user=request.user, status=ReportDownload.STATUS_PENDING)

        generate_report.delay(report.id, list(filtered_queryset.values()))

        serializer = ReportDownloadSerializer(report)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

class ReportDownloadViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    queryset = ReportDownload.objects.all()
    serializer_class = ReportDownloadSerializer

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            IsAdminUser(),
            FullAccessPermission(),
            ManageReportsPermission()
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        return ReportDownload.objects.filter(user=self.request.user)

    @action(detail=True, methods=['post'])
    def download(self, request, pk=None):
        report = self.get_object()

        if report.status == ReportDownload.STATUS_COMPLETED and report.file:
            response = HttpResponse(report.file, content_type='application/octet-stream')
            response['Content-Disposition'] = f'attachment; filename={report.file.name}'
            return response
        else:
            return Response({'error': 'Report is not ready for download.'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        try:
            report = self.get_object()

            if report.status in [ReportDownload.STATUS_PENDING, ReportDownload.STATUS_PROCESSING]:
                report.status = ReportDownload.STATUS_CANCELED
                report.save()

                # Clean the cache progress
                progress_key = f'report_progress_{report.id}'
                cache.delete(progress_key)

                return Response({'status': 'Report download canceled successfully.'}, status=status.HTTP_200_OK)
            else:
                return Response({'error': 'Report cannot be canceled at this stage.'}, status=status.HTTP_400_BAD_REQUEST)

        except ReportDownload.DoesNotExist:
            return Response({'error': 'Report not found.'}, status=status.HTTP_404_NOT_FOUND)

class PaymentDisputeAreaViewSet(viewsets.ModelViewSet):
    queryset = PaymentDisputeArea.objects.all()
    serializer_class = PaymentDisputeSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['payment', 'created_at', 'status', 'payment__chargedbackAt', 'payment__paymentMethod']

    def create(self, request, *args, **kwargs):
        serialized_data = PaymentDisputeSerializer(data=request.data)
        if not serialized_data.is_valid():
            return Response(serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)
        payment_dispute = serialized_data.save()
        return Response(PaymentDisputeSerializer(payment_dispute).data, status=status.HTTP_201_CREATED)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serialized_data = PaymentDisputeSerializer(instance, data=request.data, partial=True)
        if not serialized_data.is_valid():
            return Response(serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)

        # Exclude the payment field from being updated
        validated_data = serialized_data.validated_data
        if 'payment' in validated_data:
            validated_data.pop('payment')

        payment_dispute = serialized_data.save()
        return Response(PaymentDisputeSerializer(payment_dispute).data, status=status.HTTP_200_OK)


class PaymentDisputeFileUploadViewSet(viewsets.ModelViewSet):
    queryset = PaymentDisputeUploadedFile.objects.all()
    serializer_class = PaymentDisputeUploadedFileSerializer

    def create(self, serializer, *args, **kwargs):
        files = self.request.FILES.getlist('file')
        payment_dispute = get_object_or_404(PaymentDisputeArea, id=self.request.data.get('payment_dispute'))
        allowed_mime_types = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]

        for file in files:
            # Check file format using content_type attribute
            if file.content_type not in allowed_mime_types:
                return Response({'error': 'Invalid file format. Only PDF, JPG, JPEG, PNG, DOC, and DOCX are allowed.'},
                                status=status.HTTP_400_BAD_REQUEST)

            # Check file size (e.g., max 5MB)
            if file.size > 5 * 1024 * 1024:
                return Response({'error': 'File size exceeds the limit of 5MB.'}, status=status.HTTP_400_BAD_REQUEST)

        # Save each file
        for file in files:
            serializer = self.get_serializer(data={'file': file, 'payment_dispute': payment_dispute.id})
            if serializer.is_valid():
                serializer.save()
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        return Response({'status': 'Files uploaded successfully'}, status=status.HTTP_201_CREATED)

class SubscriptionView(FlexFieldsMixin, ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = SubscriptionSerializer
    filterset_class = SubscriptionFilter
    filter_backends = (DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter)
    search_fields = ['id', 'user__email', 'user__id']
    ordering_fields = ['createdAt', 'updatedAt', 'next_payment_date']
    permit_list_expands = ['user']
    pagination.PageNumberPagination.page_size = 100

    def get_queryset(self):
        queryset = Subscription.objects.all()

        if is_expanded(self.request, 'user') and is_expanded(self.request, 'payments'):
            queryset = queryset.select_related('user', 'payments')
        elif is_expanded(self.request, 'user'):
            queryset = queryset.select_related('user')
        elif is_expanded(self.request, 'payments'):
            queryset = queryset.prefetch_related('payments')

        return queryset

    def list(self, request, *args, **kwargs):
        start_date = request.query_params.get('next_payment_date__gte')
        end_date = request.query_params.get('next_payment_date__lte')

        if start_date and end_date:
            try:
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
                end_date = datetime.strptime(end_date, "%Y-%m-%d")
            except ValueError:
                return Response({'detail': 'Formato de data inválido. Use YYYY-MM-DD.'}, status=status.HTTP_400_BAD_REQUEST)

            if (end_date - start_date).days > 90:
                return Response({'detail': 'O intervalo de datas não pode ser maior que 90 dias.'}, status=status.HTTP_400_BAD_REQUEST)

        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class SubscriptionUpdateView(APIView):
    permission_classes = [IsAuthenticated]

    @staticmethod
    def get_subscription(pk: int, user: User) -> Subscription or Response:
        try:
            subscription = Subscription.objects.get(pk=pk) if user.is_superuser else Subscription.objects.get(pk=pk,
                                                                                                              user=user)
        except Subscription.DoesNotExist:
            return Response({'detail': 'Assinatura não encontrada.'}, status=status.HTTP_404_NOT_FOUND)

        return subscription


    def patch(self, request, pk):
        subscription: Subscription = self.get_subscription(pk, request.user)

        if isinstance(subscription, Response):
            return subscription

        serializer = SubscriptionUpdateSerializer(subscription, data=request.data, partial=True)
        if serializer.is_valid():
            with transaction.atomic():
                status_to_update = serializer.validated_data.get('status')
                payment_method_to_update = serializer.validated_data.get('paymentMethod')
                card_data_to_update = serializer.validated_data.get('card')

                # Subscriptions cancelling
                if status_to_update == Subscription.STATUS_CANCELED:
                    subscription.cancel()
                    subscription.refresh_from_db()

                    subscription_serializer = SubscriptionSerializer(subscription)
                    return Response({
                        'detail': 'Assinatura e pagamentos futuros cancelados com sucesso.',
                        'subscription': subscription_serializer.data
                    }, status=status.HTTP_200_OK)

                updated = False

                # Payment method update
                if payment_method_to_update or card_data_to_update:
                    if payment_method_to_update:
                        subscription.paymentMethod = payment_method_to_update
                        updated = True

                    # Check if card is sent and if the payment method is credit card
                    if card_data_to_update and payment_method_to_update == 'credit_card':
                        # TODO (scpaes): refactor logic to use properly the card serializer
                        card_token = card_data_to_update.get('token') or request.data.get('card').get('token')

                        # If a token is provided, search for the card by the token
                        if card_token:
                            try:
                                card = Card.objects.get(token=card_token)
                                subscription.card = card
                            except Card.DoesNotExist:
                                return Response({'detail': 'Cartão não encontrado.'}, status=status.HTTP_404_NOT_FOUND)

                        # If there is no token, create or update the card according to the data sent
                        else:
                            if customer := card_data_to_update.get('customer'):
                                card_data_to_update['customer'] = customer.id

                            card_serializer = CardSerializer(data=card_data_to_update)
                            card_serializer.is_valid(raise_exception=True)
                            card, _ = Card.objects.update_or_create(
                                customer__id=card_data_to_update["customer"],
                                customer__user=subscription.user,
                                number=card_serializer.validated_data['number'],
                                defaults=card_serializer.validated_data
                            )
                            subscription.card = card

                        updated = True

                    # To ensure that the card is not saved for methods that are not credit card
                    elif payment_method_to_update != 'credit_card' and subscription.card:
                        subscription.card = None
                        updated = True

                    subscription.save()

                    # Update future scheduled payments with the new payment method or card
                    future_payments = Payment.objects.filter(subscription=subscription, status='scheduled')
                    for payment in future_payments:
                        payment.paymentMethod = subscription.paymentMethod
                        if subscription.card and subscription.paymentMethod == 'credit_card':
                            payment.card = subscription.card
                        payment.save()

                if updated:
                    subscription_serializer = SubscriptionSerializer(subscription)
                    return Response({
                        'detail': 'Assinatura atualizada com sucesso.',
                        'subscription': subscription_serializer.data
                    }, status=status.HTTP_200_OK)
                else:
                    return Response({'detail': 'Nada para atualizar.'}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({'detail': 'Dados inválidos.', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class AdditionalFlowView(viewsets.ModelViewSet):
    permission_classes = [SuperUserPermission]
    serializer_class = AdditionalFlowSerializer
    queryset = AdditionalFlows.objects.all()


class PaymentReprocessingHistoryView(viewsets.ModelViewSet):
    permission_classes = [ReprocessPaymentPermission]
    serializer_class = PaymentReprocessingHistorySerializer
    queryset = PaymentReprocessingHistory.objects.all()

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
