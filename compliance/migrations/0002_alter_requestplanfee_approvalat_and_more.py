# Generated by Django 5.1.4 on 2025-05-16 17:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("compliance", "0001_initial"),
        ("gateway", "0155_planfee_need_approval"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="requestplanfee",
            name="approvalAt",
            field=models.DateTimeField(
                blank=True,
                help_text="Data e hora em que a solicitação foi aprovada",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="requestplanfee",
            name="changer",
            field=models.ForeignKey(
                blank=True,
                help_text="Usuário que aprova ou rejeita na solicitação",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="changer",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="requestplanfee",
            name="createdAt",
            field=models.DateTimeField(
                auto_now_add=True,
                help_text="Data e hora em que a solicitação foi criada",
            ),
        ),
        migrations.AlterField(
            model_name="requestplanfee",
            name="plan_fee",
            field=models.ForeignKey(
                help_text="Plano de taxa que foi solicitado para aprovação",
                on_delete=django.db.models.deletion.CASCADE,
                to="gateway.planfee",
            ),
        ),
        migrations.AlterField(
            model_name="requestplanfee",
            name="rejectAt",
            field=models.DateTimeField(
                blank=True,
                help_text="Data e hora em que a solicitação foi rejeitada",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="requestplanfee",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pendente"),
                    ("approved", "Aprovado"),
                    ("rejected", "Rejeitado"),
                ],
                default="pending",
                help_text="Status da solicitação de aprovação do plano de taxa",
                max_length=255,
            ),
        ),
        migrations.AlterField(
            model_name="requestplanfee",
            name="updatedAt",
            field=models.DateTimeField(
                auto_now=True,
                help_text="Data e hora em que a solicitação foi atualizada",
            ),
        ),
        migrations.AlterField(
            model_name="requestplanfee",
            name="user",
            field=models.ForeignKey(
                help_text="Usuário que fez a solicitação para aprovação",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
