# Generated by Django 5.1.4 on 2025-05-12 19:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("gateway", "0155_planfee_need_approval"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="RequestPlanFee",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pendente"),
                            ("approved", "Aprovado"),
                            ("rejected", "Rejeitado"),
                        ],
                        default="pending",
                        max_length=255,
                    ),
                ),
                ("approvalAt", models.DateTimeField(blank=True, null=True)),
                ("rejectAt", models.DateTimeField(blank=True, null=True)),
                ("createdAt", models.DateTimeField(auto_now_add=True)),
                ("updatedAt", models.DateTimeField(auto_now=True)),
                (
                    "changer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="changer",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "plan_fee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="gateway.planfee",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "permissions": [
                    ("manage_requestplanfee", "Can manage Requests Plan Fee")
                ],
            },
        ),
    ]
