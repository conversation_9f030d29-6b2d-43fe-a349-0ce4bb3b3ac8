import pytest
from rest_framework import status
from rest_framework.reverse import reverse
from rest_framework.test import APIClient

pytestmark = pytest.mark.django_db


class TestRequestPlanFee:
    def test_should_return_success_on_create_request_plan_fee(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        plan_fee,
    ):
        url: str = reverse('request-plan-fee-list-create')
        new_data = {'plan_fee': plan_fee.id}
        response = logged_in_as_staff_user_with_plan_fee_permission.post(path=url, data=new_data)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data == {'id': 1, 'status': 'pending', 'plan_fee': 1}

    def test_create_request_plan_fee_succeeds_if_previous_is_approved_or_rejected(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        request_plan_fee.status = 'approved'
        request_plan_fee.save()
        url: str = reverse('request-plan-fee-list-create')
        new_data = {'plan_fee': request_plan_fee.plan_fee.id}
        response = logged_in_as_staff_user_with_plan_fee_permission.post(path=url, data=new_data)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.data
        assert data is not None
        assert 'id' in data
        assert 'status' in data
        assert 'plan_fee' in data

    def test_error_when_creating_duplicate_request_plan_fee_for_same_plan_fee(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url: str = reverse('request-plan-fee-list-create')
        new_data = {'plan_fee': request_plan_fee.plan_fee.id}
        response = logged_in_as_staff_user_with_plan_fee_permission.post(path=url, data=new_data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data == {
            'status': 'error',
            'detail': 'Já existe um pedido de aprovação para esse plano.',
        }

    def test_should_return_error_on_create_request_plan_fee(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
    ):
        url: str = reverse('request-plan-fee-list-create')
        new_data = {}
        response = logged_in_as_staff_user_with_plan_fee_permission.post(path=url, data=new_data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data is not None
        assert response.data == {'status': 'error', 'detail': 'Plano não encontrado.'}

    def test_should_return_unauthorized_when_not_authenticated(self):
        client = APIClient()
        url: str = reverse('request-plan-fee-list-create')
        response = client.post(url, data={'plan_fee': 1})
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]

    def test_should_approve_request_plan_fee_successfully(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-approve', kwargs={'pk': request_plan_fee.id})
        response = logged_in_as_staff_user_with_plan_fee_permission.post(url, data={})

        assert response.status_code == status.HTTP_200_OK
        assert response.data['status'] == 'approved'

    def test_should_approve_request_plan_fee_not_pendding(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        request_plan_fee.status = 'approved'
        request_plan_fee.save()
        url = reverse('request-plan-fee-approve', kwargs={'pk': request_plan_fee.id})
        response = logged_in_as_staff_user_with_plan_fee_permission.post(url, data={})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data == {'message': 'Esse pedido já foi processado.'}

    def test_should_approve_request_plan_fee_when_not_staff(self):
        client = APIClient()
        url = reverse('request-plan-fee-approve', kwargs={'pk': 0})
        response = client.post(url, data={})

        assert response.status_code == status.HTTP_403_FORBIDDEN
        error_response_detail = response.data.get('detail')
        assert error_response_detail == 'As credenciais de autenticação não foram fornecidas.'

    def test_should_approve_request_plan_fee_when_is_staff_but_not_permission(
        self, logged_in_as_staff_user: APIClient
    ):
        url = reverse('request-plan-fee-approve', kwargs={'pk': 0})
        response = logged_in_as_staff_user.post(url, data={})

        assert response.status_code == status.HTTP_403_FORBIDDEN
        error_response_detail = response.data.get('detail')
        assert error_response_detail == 'Você não tem permissão para executar essa ação.'

    def test_should_approve_request_plan_fee_when_staff_but_not_permission(
        self,
        logged_in_as_staff_user: APIClient,
    ):
        url = reverse('request-plan-fee-approve', kwargs={'pk': 0})
        response = logged_in_as_staff_user.post(url, data={})

        assert response.status_code == status.HTTP_403_FORBIDDEN
        error_response_detail = response.data.get('detail')
        assert error_response_detail == 'Você não tem permissão para executar essa ação.'

    def test_should_reject_request_plan_fee_successfully(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-reject', kwargs={'pk': request_plan_fee.id})
        response = logged_in_as_staff_user_with_plan_fee_permission.post(url, data={})

        assert response.status_code == status.HTTP_200_OK
        assert response.data['status'] == 'rejected'

    def test_should_reject_request_plan_fee_not_pendding(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        request_plan_fee.status = 'reject'
        request_plan_fee.save()
        url = reverse('request-plan-fee-reject', kwargs={'pk': request_plan_fee.id})
        response = logged_in_as_staff_user_with_plan_fee_permission.post(url, data={})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data == {'message': 'Esse pedido já foi processado.'}

    def test_should_reject_request_plan_fee_when_not_staff(self):
        client = APIClient()
        url = reverse('request-plan-fee-reject', kwargs={'pk': 0})
        response = client.post(url, data={})

        assert response.status_code == status.HTTP_403_FORBIDDEN
        error_response_detail = response.data.get('detail')
        assert error_response_detail == 'As credenciais de autenticação não foram fornecidas.'

    def test_should_reject_request_plan_fee_when_is_staff_but_not_permission(
        self, logged_in_as_staff_user: APIClient
    ):
        url = reverse('request-plan-fee-reject', kwargs={'pk': 0})
        response = logged_in_as_staff_user.post(url, data={})

        assert response.status_code == status.HTTP_403_FORBIDDEN
        error_response_detail = response.data.get('detail')
        assert error_response_detail == 'Você não tem permissão para executar essa ação.'

    def test_should_reject_request_plan_fee_when_staff_but_not_permission(
        self,
        logged_in_as_staff_user: APIClient,
    ):
        url = reverse('request-plan-fee-reject', kwargs={'pk': 0})
        response = logged_in_as_staff_user.post(url, data={})

        assert response.status_code == status.HTTP_403_FORBIDDEN
        error_response_detail = response.data.get('detail')
        assert error_response_detail == 'Você não tem permissão para executar essa ação.'

    def test_should_return_success_on_get_request_plan_fee_list(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-list-create')
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 1
        assert type(data[0].get('plan_fee')) == int
        assert type(data[0].get('user')) == int
        assert 'id' in data[0]
        assert 'changer' in data[0]
        assert 'status' in data[0]
        assert 'approvalAt' in data[0]
        assert 'rejectAt' in data[0]
        assert 'createdAt' in data[0]

    def test_should_return_success_on_get_request_plan_fee_list_filter_plan_fee_name(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        search_query = 'Test Plan Fee'
        url = reverse('request-plan-fee-list-create') + f'?search={search_query}'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 1

    def test_should_return_success_on_get_request_plan_fee_list_filter_user_email(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        search_query = request_plan_fee.user.email
        url = reverse('request-plan-fee-list-create') + f'?search={search_query}'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 1

    def test_should_return_success_on_get_request_plan_fee_list_filter_user_cpf(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        search_query = request_plan_fee.user.cpf
        url = reverse('request-plan-fee-list-create') + f'?search={search_query}'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 1

    def test_should_return_success_on_get_request_plan_fee_list_filter_search_return_empty(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        search_query = 'NOT FOUND'
        url = reverse('request-plan-fee-list-create') + f'?search={search_query}'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 0

    def test_should_return_success_on_get_request_plan_fee_list_filter_plan_fee_id(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-list-create') + f'?plan_fee={request_plan_fee.plan_fee.id},2'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 1

    def test_should_return_success_on_get_request_plan_fee_list_filter_plan_fee_id_return_empty(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-list-create') + f'?plan_fee=9999'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 0

    def test_should_return_success_on_get_request_plan_fee_list_filter_user_id(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-list-create') + f'?user ={request_plan_fee.user.id},2'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 1

    def test_should_return_success_on_get_request_plan_fee_list_filter_user_id_return_empty(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-list-create') + f'?user=123456789'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 0

    def test_should_return_success_on_get_request_plan_fee_list_filter_status(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-list-create') + f'?status={request_plan_fee.status}'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 1

    def test_should_return_success_on_get_request_plan_fee_list_filter_status_return_empty(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-list-create') + f'?status=not_approved'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 0

    def test_should_return_success_on_get_requests_plan_fee_list_expand_plan_fee(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-list-create') + '?expand=plan_fee,user'
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data.get('results')
        assert data is not None
        assert len(data) == 1
        assert type(data[0].get('plan_fee')) == dict
        assert type(data[0].get('user')) == dict
        assert 'id' in data[0]
        assert 'changer' in data[0]
        assert 'status' in data[0]
        assert 'approvalAt' in data[0]
        assert 'rejectAt' in data[0]
        assert 'createdAt' in data[0]

    def test_should_return_unauthorized_user_without_permissions_on_get_requests_plan_fee_list(
        self,
        logged_in_as_staff_user: APIClient,
    ):
        url = reverse('request-plan-fee-list-create')
        response = logged_in_as_staff_user.get(url)

        assert response.status_code == status.HTTP_403_FORBIDDEN
        error_response_detail = response.data.get('detail')
        assert error_response_detail == 'Você não tem permissão para executar essa ação.'

    def test_should_return_unauthorized_user_not_staff_on_get_requests_plan_fee_list(
        self,
        logged_in_as_regular_user: APIClient,
    ):
        url = reverse('request-plan-fee-list-create')
        response = logged_in_as_regular_user.get(url)

        assert response.status_code == status.HTTP_403_FORBIDDEN
        error_response_detail = response.data.get('detail')
        assert error_response_detail == 'Você não tem permissão para executar essa ação.'

    def test_should_return_success_on_get_request_plan_fee_detail(
        self,
        logged_in_as_staff_user_with_plan_fee_permission: APIClient,
        request_plan_fee,
    ):
        url = reverse('request-plan-fee-detail')
        response = logged_in_as_staff_user_with_plan_fee_permission.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data
        assert data is not None
        assert type(data.get('plan_fee')) == int
        assert type(data.get('user')) == int
        assert 'id' in data
        assert 'changer' in data
        assert 'status' in data
        assert 'approvalAt' in data
        assert 'rejectAt' in data
        assert 'createdAt' in data

    def test_should_return_success_on_get_requests_plan_fee_detail_expand_plan_fee(
        self,
        logged_in_as_regular_user: APIClient,
        regular_user,
        request_plan_fee,
    ):
        request_plan_fee.user = regular_user
        request_plan_fee.save()

        url = reverse('request-plan-fee-detail') + '?expand=plan_fee,user'
        response = logged_in_as_regular_user.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.data
        assert data is not None
        assert type(data.get('plan_fee')) == dict
        assert type(data.get('user')) == dict
        assert 'id' in data
        assert 'changer' in data
        assert 'status' in data
        assert 'approvalAt' in data
        assert 'rejectAt' in data
        assert 'createdAt' in data

    def test_should_return_error_on_get_requests_plan_fee_detail_not_found_request_plan_fee(
        self,
        logged_in_as_regular_user: APIClient,
    ):
        url = reverse('request-plan-fee-detail')
        response = logged_in_as_regular_user.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND
        error_response_detail = response.data.get('detail')
        assert error_response_detail == 'Pedido para aprovação não encontrado.'
