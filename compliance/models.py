from django.db import models
from django.utils import timezone


class RequestPlanFee(models.Model):
    class Meta:
        permissions = [
            ('manage_requestplanfee', 'Can manage Requests Plan Fee'),
        ]

    user = models.ForeignKey('user.User', on_delete=models.CASCADE, related_name='user', help_text='Usuário que fez a solicitação para aprovação')
    changer = models.ForeignKey(
        'user.User', on_delete=models.CASCADE, related_name='changer', null=True, blank=True, help_text='Usuário que aprova ou rejeita na solicitação'
    )
    plan_fee = models.ForeignKey(
        'gateway.PlanFee', on_delete=models.CASCADE, blank=False, null=False, help_text='Plano de taxa que foi solicitado para aprovação'
    )
    status = models.CharField(
        max_length=255,
        choices=(
            ('pending', 'Pendente'),
            ('approved', 'Aprovado'),
            ('rejected', 'Rejeitado'),
        ),
        default='pending',
        help_text='Status da solicitação de aprovação do plano de taxa'
    )

    approvalAt = models.DateTimeField(null=True, blank=True, help_text='Data e hora em que a solicitação foi aprovada')
    rejectAt = models.DateTimeField(null=True, blank=True, help_text='Data e hora em que a solicitação foi rejeitada')
    createdAt = models.DateTimeField(auto_now_add=True, help_text='Data e hora em que a solicitação foi criada')
    updatedAt = models.DateTimeField(auto_now=True, help_text='Data e hora em que a solicitação foi atualizada')

    def set_approved_or_reject(self, is_approved: bool):
        """
        Atualiza o status da solicitação para 'approved' ou 'rejected' e define a data/hora correspondente.

        Args:
            is_approved (bool): Indica se a solicitação foi aprovada (True) ou rejeitada (False).

        Side Effects:
            - Atualiza o campo `status` para 'approved' ou 'rejected'.
            - Define o campo `approvalAt` com a data/hora atual se aprovado.
            - Define o campo `rejectAt` com a data/hora atual se rejeitado.
            - Salva as alterações no banco de dados.

        Raises:
            ValueError: Se o status atual já for 'approved' ou 'rejected'.
        """
        if self.status in ['approved', 'rejected']:
            raise ValueError("A solicitação já foi processada e não pode ser alterada.")

        self.status = 'approved' if is_approved else 'rejected'
        current_time = timezone.now()

        if is_approved:
            self.approvalAt = current_time
        else:
            self.rejectAt = current_time

        self.save()
