from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from gateway.models import <PERSON><PERSON>ee

from .filters import Request<PERSON><PERSON><PERSON>ee<PERSON>ilter
from .models import Request<PERSON><PERSON><PERSON>ee
from user.permissions import ManageRequestPlanFeePermission
from .serializers import RequestPlanFeeCreateSerializer, RequestPlanFeeSerializer


class RequestPlanFeeViewset(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = RequestPlanFeeSerializer
    queryset = RequestPlanFee.objects.all()
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    filterset_class = RequestPlanFeeFilter
    search_fields = [
        'plan_fee__name',
        'user__email',
        'user__cpf',
        'user__cnpj',
    ]
    ordering_fields = ['id', 'createdAt']

    def get_permissions(self):
        permissions = super().get_permissions()
        if self.action in ['approve', 'reject', 'list']:
            permissions = [IsAdminUser(), ManageRequestPlanFeePermission()]
        return permissions

    def get_object(self):
        return get_object_or_404(RequestPlanFee, pk=self.kwargs['pk'])

    def retrieve(self, request, *args, **kwargs):
        instance = RequestPlanFee.objects.filter(user=request.user, status='pending').first()
        if not instance:
            return Response(
                {
                    'status': 'error',
                    'detail': 'Pedido para aprovação não encontrado.',
                },
                status=status.HTTP_404_NOT_FOUND,
            )
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def create(self, request: Request, *args, **kwargs) -> Response:
        plan_fee_id = request.data.get('plan_fee')
        plan_fee = PlanFee.objects.filter(id=plan_fee_id).first()

        if not plan_fee:
            return Response(
                {
                    'status': 'error',
                    'detail': 'Plano não encontrado.',
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        request_plan_fee = (
            RequestPlanFee.objects.exclude(status__in=['approved', 'rejected'])
            .filter(plan_fee=plan_fee, user=request.user)
            .first()
        )

        if request_plan_fee:
            return Response(
                {
                    'status': 'error',
                    'detail': 'Já existe um pedido de aprovação para esse plano.',
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = RequestPlanFeeCreateSerializer(
            data=request.data, context={'user': request.user}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        request_plan_fee = self.get_object()

        if request_plan_fee.status != 'pending':
            return Response(
                {'message': 'Esse pedido já foi processado.'}, status=status.HTTP_400_BAD_REQUEST
            )

        request_plan_fee.set_approved_or_reject(is_approved=True)

        return Response({'status': request_plan_fee.status})

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        request_plan_fee = self.get_object()

        if request_plan_fee.status != 'pending':
            return Response(
                {'message': 'Esse pedido já foi processado.'}, status=status.HTTP_400_BAD_REQUEST
            )

        request_plan_fee.set_approved_or_reject(is_approved=False)

        return Response({'status': request_plan_fee.status})
