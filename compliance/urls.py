from django.urls import path

from . import views

urlpatterns = [
    path(
        'requests-plan-fee/',
        views.RequestPlanFeeViewset.as_view({'post': 'create', 'get': 'list'}),
        name='request-plan-fee-list-create',
    ),
    path(
        'request-plan-fee/',
        views.RequestPlanFeeViewset.as_view(
            {
                'get': 'retrieve'
            }
        ),
        name='request-plan-fee-detail'
    ),
    # Action approve
    path(
        'request-plan-fee/<int:pk>/approve/',
        views.RequestPlanFeeViewset.as_view({'post': 'approve'}),
        name='request-plan-fee-approve',
    ),
    # Action reject
    path(
        'request-plan-fee/<int:pk>/reject/',
        views.RequestPlanFeeViewset.as_view({'post': 'reject'}),
        name='request-plan-fee-reject',
    ),
]
