from django_filters import rest_framework as filters

from .models import RequestPlanFee


class RequestPlanFeeFilter(filters.FilterSet):
    plan_fee = filters.BaseInFilter(field_name='plan_fee', lookup_expr='in')
    status = filters.BaseInFilter(field_name='status', lookup_expr='in')
    user = filters.BaseInFilter(field_name='user', lookup_expr='in')

    class Meta:
        model = RequestPlanFee
        fields = ['plan_fee', 'status', 'user']
