from rest_flex_fields import FlexFieldsModelSerializer
from rest_framework import serializers

from gateway.serializers import PlanFeeFlexFiedlsSerializer
from user.serializers import UserFlexFiedlsSerializer

from .models import RequestPlanFee


class RequestPlanFeeSerializer(FlexFieldsModelSerializer):
    class Meta:
        model = RequestPlanFee
        fields = (
            'id',
            'user',
            'changer',
            'plan_fee',
            'status',
            'approvalAt',
            'rejectAt',
            'createdAt',
        )
        expandable_fields = {
            'plan_fee': (PlanFeeFlexFiedlsSerializer, {'fields': ['id', 'name']}),
            'user': (UserFlexFiedlsSerializer, {'fields': ['id', 'email']}),
        }


class RequestPlanFeeCreateSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    status = serializers.CharField(read_only=True)

    class Meta:
        model = RequestPlanFee
        fields = (
            'id',
            'status',
            'plan_fee',
        )

    def create(self, validated_data):
        if not self.context.get('user'):
            raise serializers.ValidationError('campo user não encontrado')
        validated_data['user'] = self.context['user']
        return super().create(validated_data)
