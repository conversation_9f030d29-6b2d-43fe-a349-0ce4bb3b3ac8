import io
import datetime
from typing import List

import pytest
from django.contrib.auth.models import Group, Permission
from django.core.cache import cache
from django.core.files import File
from django.test import RequestFactory
from django.urls import reverse
from django.utils import timezone
from model_bakery import baker, seq
from rest_framework.test import APIClient

from compliance.models import RequestPlanFee
from customer.models import Customer, Card
from gateway.company import Efi
from gateway.company.idwall import IdWall
from gateway.company.openpix import OpenPix
from gateway.models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, PlanFee
from gateway.views import DashboardAPIV2
from payment.filters import PaymentDisputeFilterset, PaymentFilterset
from payment.models import (
    Payment,
    PaymentDisputeArea,
    PaymentDisputeUploadedFile,
    RefusedPaymentLog,
    Subscription, FlowActions, AdditionalFlows, Position, PaymentReprocessingHistory,
)
from payment.strategies.bill_strategy import BillStrategy
from user.models import User, IdWallProfile, Withdrawal, APIEndpoint, Integration, AccessLog
from payment.strategies.pix_strategy import PIXStrategy
from user.views import UserViewSet, WithdrawalViewSet

PAYMENT_DISPUTE_UPLOADED_FILE_PATH = "documents/dummy_test.pdf"


@pytest.fixture
def pix_strategy() -> PIXStrategy:
    return PIXStrategy()


@pytest.fixture
def bill_strategy() -> BillStrategy:
    return BillStrategy()


@pytest.fixture
def payment_data(user: User, openpix_acquirer: Acquirer) -> Payment:
    return baker.make(
        "Payment",
        user=user,
        acquirer=openpix_acquirer,
        amount=100,
        paymentMethod="pix",
    )


@pytest.fixture
def openpix_acquirer() -> Acquirer:
    acquirer = Acquirer.objects.get(name="OpenPix", acquirer_gateway="openpix")
    acquirer.keys = {"token": "test-token"}
    return acquirer


@pytest.fixture
def openpix_api(openpix_acquirer) -> OpenPix:
    return OpenPix(openpix_acquirer)


@pytest.fixture
def user(db) -> User:
    return baker.make(
        User,
        email=seq("user@test_payment.com"),
        cpf=seq("**********"),
    )

@pytest.fixture
def user_with_balance(admin_user: User) -> User:
    admin_user.balance = 1000
    admin_user.save(update_fields=["balance"])
    return admin_user

@pytest.fixture
def user_with_idwall_profile(user: User) -> User:
    user.idwall_profile = IdWallProfile.objects.create( # type: ignore
        user=user,
        status='approved',
        cpf = user.cpf
    )
    user.idwall_profile.save() # type: ignore
    return user


@pytest.fixture
def client():
    return APIClient()

@pytest.fixture
def withdrawal(admin_user: User) -> Withdrawal:
    return baker.make(
        "Withdrawal",
        user=admin_user,
        amount=100,
        status="approved",
        paidAt=timezone.now() + timezone.timedelta(days=10),
    )

@pytest.fixture
def withdrawal_with_pix_key(user: User) -> Withdrawal:
    return baker.make(
        "Withdrawal",
        user=user,
        amount=100,
        status="approved",
        paidAt=timezone.now() + timezone.timedelta(days=10),
        pixKey="pix_key_from_withdrawal"
    )


@pytest.fixture
def admin_user(faker) -> User:
    return baker.make(
        "user.User",
        username=faker.name(),
        email=faker.email(),
        password="P@ssW0rd",
        is_staff=True,
        is_superuser=True,
        is_active=True,
        cpf = '1234567890'
    )

@pytest.fixture
def fee_for_admin_user(admin_user: User) -> Fee:
    return baker.make(
        "Fee",
        user=admin_user,
    )


@pytest.fixture
def logged_in_as_admin_user(client: APIClient, admin_user: User) -> APIClient:
    client.force_authenticate(user=admin_user)
    return client


@pytest.fixture
def regular_user(faker) -> User:
    return baker.make(
        "user.User",
        username=faker.name(),
        email=faker.email(),
        password="P@ssW0rd",
        is_staff=False,
        is_active=True
    )


@pytest.fixture
def regular_user_with_manage_plan_fee_permission(regular_user: User) -> User:
    permission = Permission.objects.get(codename='manage_planfee')
    regular_user.user_permissions.add(permission)
    return regular_user


@pytest.fixture
def staff_user_with_payment_reprocess_permission(staff_user: User) -> User:
    permission = Permission.objects.get(codename='manage_paymentreprocessing')
    staff_user.user_permissions.add(permission)
    return staff_user


@pytest.fixture
def approved_user() -> User:
    return baker.make(
        "user.User",
        status="approved",
    )

@pytest.fixture
def payment_with_paid_status(approved_user: User, regular_customer: Customer, acquirer: Acquirer) -> Payment:
    return baker.make(
        "Payment",
        amount=100,
        paymentMethod="pix",
        status="paid",
        user=approved_user,
        customer=regular_customer,
        acquirer=acquirer,
    )

@pytest.fixture
def payment_with_pending_status(approved_user: User, regular_customer: Customer, acquirer: Acquirer) -> Payment:
    return baker.make(
        "Payment",
        amount=100,
        paymentMethod="pix",
        status="pending",
        user=approved_user,
        customer=regular_customer,
        acquirer= acquirer,
    )

@pytest.fixture
def blocked_user() -> User:
    return baker.make(
        "user.User",
        status="blocked",
    )


@pytest.fixture
def regular_user_with_subscription(regular_user: User) -> Subscription:
    return baker.make(
        "Subscription",
        user=regular_user,
        status=Subscription.STATUS_ACTIVE,
        trial_days=0,
        next_payment_date=timezone.now(),
        amount=50,
    )

@pytest.fixture
def regular_user_with_inactive_subscription(regular_user_with_subscription: Subscription) -> Subscription:
    regular_user_with_subscription.status = Subscription.STATUS_INACTIVE
    regular_user_with_subscription.save(update_fields=["status"])
    return regular_user_with_subscription


@pytest.fixture
def pending_payment_with_subscription_pix_payment_method(
    regular_user_with_subscription: Subscription,
    regular_user: User,
    regular_customer: Customer,
) -> Payment:
    return baker.make(
        "Payment",
        user=regular_user,
        customer=regular_customer,
        amount=100,
        paymentMethod="pix",
        status="pending",
        subscription=regular_user_with_subscription,
    )

@pytest.fixture
def pending_payment_with_subscription_credit_card_payment_method(
    regular_user_with_subscription: Subscription,
    regular_user: User,
    regular_customer: Customer,
) -> Payment:
    return baker.make(
        "Payment",
        user=regular_user,
        customer=regular_customer,
        amount=100,
        paymentMethod="credit_card",
        status="pending",
        subscription=regular_user_with_subscription,
        items=[{"other": "other item"}],
    )

@pytest.fixture
def pending_payment_with_subscription_items(
    regular_user_with_subscription: Subscription,
    pending_payment_with_subscription_credit_card_payment_method: Payment,
) -> Payment:
    pending_payment_with_subscription_credit_card_payment_method.items = [
        {"subscription": regular_user_with_subscription.id}]
    pending_payment_with_subscription_credit_card_payment_method.save(update_fields=["items"])
    return pending_payment_with_subscription_credit_card_payment_method

@pytest.fixture
def regular_user_with_subscription_with_trial_days(regular_user: User) -> Subscription:
    return baker.make(
        "Subscription",
        user=regular_user,
        status=Subscription.STATUS_ACTIVE,
        trial_days=10,
    )

@pytest.fixture
def scheduled_payment_for_after_trial_days(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription_with_trial_days: Subscription,
    acquirer: Acquirer
) -> Payment:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="credit_card",
        customer=regular_customer,
        status="scheduled",
        subscription=regular_user_with_subscription_with_trial_days,
        acquirer=acquirer,
        due_date=timezone.now() + datetime.timedelta(days=regular_user_with_subscription_with_trial_days.trial_days),
    )

@pytest.fixture
def scheduled_payment_with_max_retries_reached(
    scheduled_payment: Payment,
    regular_user_with_subscription: Subscription
) -> Payment:
    scheduled_payment.retry_count = regular_user_with_subscription.max_retries
    scheduled_payment.retry_scheduled = True
    scheduled_payment.save(update_fields=["retry_count", "retry_scheduled"])
    return scheduled_payment

@pytest.fixture
def payment_refused(payment: Payment) -> Payment:
    payment.status = "refused"
    payment.reason = "Insufficient funds"
    payment.save(update_fields=["status", "reason"])
    return payment

@pytest.fixture
def payment_refused_with_subscription(payment_refused: Payment, regular_user_with_subscription: Subscription) -> Payment:
    payment_refused.subscription = regular_user_with_subscription
    payment_refused.save(update_fields=["subscription"])
    return payment_refused

@pytest.fixture
def payment_without_trial_days(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription,
    acquirer: Acquirer
) -> Payment:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="credit_card",
        customer=regular_customer,
        status="scheduled",
        subscription=regular_user_with_subscription,
        acquirer=acquirer,
    )

@pytest.fixture
def failed_payment_with_subscription(payment_without_trial_days: Payment) -> Payment:
    payment_without_trial_days.status = "failed"
    payment_without_trial_days.save(update_fields=["status"])
    return payment_without_trial_days


@pytest.fixture
def paid_payment_with_subscription(payment_without_trial_days: Payment, regular_user_with_subscription: Subscription) -> Payment:
    payment_without_trial_days.status = "paid"
    payment_without_trial_days.subscription = regular_user_with_subscription
    payment_without_trial_days.save(update_fields=["status", "subscription"])
    return payment_without_trial_days


@pytest.fixture
def payment_without_trial_days_pix_payment_method(payment_without_trial_days: Payment) -> Payment:
    payment_without_trial_days.paymentMethod = "pix"
    payment_without_trial_days.status = "pending"
    payment_without_trial_days.save(update_fields=["paymentMethod", "status"])
    return payment_without_trial_days

@pytest.fixture
def scheduled_payment(scheduled_payments: list[Payment]) -> Payment:
    return scheduled_payments[0]

@pytest.fixture
def refused_payment(scheduled_payment: Payment) -> Payment:
    scheduled_payment.status = 'refused'
    scheduled_payment.save(update_fields=['status'])
    return scheduled_payment

@pytest.fixture
def scheduled_payments(
    regular_user: User,
    regular_customer: Customer,
    regular_user_with_subscription: Subscription,
) -> list[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="pix",
        customer=regular_customer,
        status="scheduled",
        subscription=regular_user_with_subscription,
        _quantity=10,
    )

@pytest.fixture
def payment_with_picpay_as_payment_method(
    regular_user: User,
    regular_customer: Customer,
) -> Payment:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="picpay",
        customer=regular_customer,
        status="pending",
    )


@pytest.fixture
def scheduled_payments_credit_card_payment_method(scheduled_payments: list[Payment]) -> list[Payment]:
    Payment.objects.filter(id__in=[i.id for i in scheduled_payments]).update(paymentMethod="credit_card")
    return scheduled_payments


@pytest.fixture
def regular_customer(faker, regular_user: User) -> Customer:
    return baker.make(
        "Customer",
        user=regular_user,
        name=faker.name(),
        email=regular_user.email,
        phone=faker.phone_number(),
        birthDate=faker.date_of_birth(),
        docNumber="689.774.450-60",
        docType="cpf"
    )

@pytest.fixture
def duplicated_customer(regular_customer: Customer) -> Customer:
    regular_customer.id = None
    regular_customer.pk = None
    regular_customer._state_adding = True
    regular_customer.name = 'Duplicated customer name'
    regular_customer.save()
    return regular_customer


@pytest.fixture
def regular_customer_with_address(regular_customer: Customer) -> Customer:
    return baker.make(
        "customer.Address",
        customer=regular_customer,
        street="Old St",
        number="123",
        city="Oldtown",
        state="OldState",
        zipcode="12345-678",
    )


@pytest.fixture
def payments_with_pix_payment_method(regular_user: User, regular_customer: Customer) -> list[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="pix",
        customer=regular_customer,
        paidAmount=100,
        status="paid",
        _quantity=10,
    )


@pytest.fixture
def payments_with_credit_card_payment_method(regular_user: User, regular_customer: Customer) -> list[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="credit_card",
        customer=regular_customer,
        paidAmount=100,
        status="paid",
        _quantity=10,
    )


@pytest.fixture
def payments_with_boleto_payment_method(regular_user: User, regular_customer: Customer) -> list[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="boleto",
        customer=regular_customer,
        paidAmount=100,
        status="paid",
        _quantity=10,
    )


@pytest.fixture
def charged_back_payments_pix_payment_method(regular_user: User, regular_customer: Customer) -> list[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="pix",
        customer=regular_customer,
        paidAmount=100,
        status="chargedback",
        chargedbackAt=timezone.now(),
        _quantity=10,
    )


@pytest.fixture
def charged_back_payments_credit_card_payment_method(regular_user: User, regular_customer: Customer) -> list[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="credit_card",
        customer=regular_customer,
        paidAmount=100,
        status="chargedback",
        chargedbackAt=timezone.now() + timezone.timedelta(days=10),
        _quantity=10,
    )


@pytest.fixture
def charged_back_payments_boleto_payment_method(regular_user: User, regular_customer: Customer) -> list[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="boleto",
        customer=regular_customer,
        paidAmount=100,
        status="chargedback",
        chargedbackAt=timezone.now(),
        _quantity=10,
    )


@pytest.fixture
def withdrawal_data(admin_user: User) -> list[Withdrawal]:
    return baker.make(
        "Withdrawal",
        user=admin_user,
        amount=100,
        status="approved",
        paidAt=timezone.now() + timezone.timedelta(days=10),
        _quantity=10,
    )


@pytest.fixture
def setup_dashboard_api_view(admin_user: User) -> tuple[DashboardAPIV2, RequestFactory]:
    factory = RequestFactory()
    view = DashboardAPIV2()
    start_date = timezone.now().date()
    end_date = (timezone.now() + timezone.timedelta(days=11)).date()
    url = f"{reverse('dashboard-v2')}?start_date={start_date}&end_date={end_date}"
    request = factory.get(url)
    request.query_params = {
        "startDate": start_date,
        "endDate": end_date,
    }
    request.user = admin_user
    view.request = request
    return view, request


@pytest.fixture
def acquirer_with_priority():
    Acquirer.objects.all().update(
        enabled=True,
        creditCardEnabled=True,
    )

    for priority, acquirer in enumerate(Acquirer.objects.all()):
        acquirer.creditCardPriority = priority + 1
        acquirer.save()


@pytest.fixture
def logged_in_as_regular_user(client: APIClient, regular_user: User) -> APIClient:
    client.force_login(user=regular_user)
    return client

@pytest.fixture
def credit_card(faker, regular_customer: Customer) -> Card:
    return baker.make(
        "Card",
        customer=regular_customer,
        token=faker.uuid4(),
        externalToken=faker.uuid4(),
        holderName=faker.name(),
        number=faker.credit_card_number(),
        cvv=faker.credit_card_security_code()
    )


@pytest.fixture
def payment(regular_user, regular_customer) -> Payment:
    return baker.make(
        "Payment",
        customer=regular_customer,
        user=regular_user,
        amount=100,
        status="pending",
        paymentMethod="credit_card",
        postbackUrl="https://someurl.com"
    )

@pytest.fixture
def payment_with_external_id(payment: Payment) -> Payment:
    payment.externalId = "1234567890"
    payment.save(update_fields=["externalId"])
    return payment

@pytest.fixture
def payment_with_declined_status(payment: Payment) -> Payment:
    payment.status = "declined"
    payment.save()
    return payment

@pytest.fixture
def payment_with_canceled_status(payment: Payment) -> Payment:
    payment.status = "canceled"
    payment.save()
    return payment


@pytest.fixture
def payment_with_refused_status(payment: Payment) -> Payment:
    payment.status = "refused"
    payment.save()
    return payment


@pytest.fixture
def payment_with_refunded_status(payment: Payment) -> Payment:
    payment.status = "refunded"
    payment.save()
    return payment


@pytest.fixture
def payment_with_subscription(payment: Payment) -> Payment:
    payment.due_date = timezone.now() + timezone.timedelta(days=1)
    subscription = baker.make(
        "Subscription",
        user=payment.user,
        status="active",
        recurrence_period=2,
        quantity_recurrences=10,
        next_payment_date=payment.due_date,
        amount=payment.amount,
    )
    payment.subscription = subscription
    payment.save(update_fields=["due_date", "subscription"])
    return payment


@pytest.fixture
def subscription_with_active_status(regular_user: User) -> Subscription:
    return baker.make(
        "Subscription",
        user=regular_user,
        status="active",
        recurrence_period=1,
        quantity_recurrences=12,
        amount=100,
    )


@pytest.fixture
def subscription_scheduled_payments(
    subscription_with_active_status: Subscription,
    regular_user: User,
    regular_customer: Customer,
) -> List[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        customer=regular_customer,
        amount=subscription_with_active_status.amount,
        status="scheduled",
        paymentMethod="pix",
        subscription=subscription_with_active_status,
        due_date=subscription_with_active_status.next_payment_date,
        _quantity=subscription_with_active_status.quantity_recurrences,
    )


@pytest.fixture
def subscription_with_paid_payments(
    subscription_with_active_status: Subscription,
    regular_user: User,
    regular_customer: Customer,
) -> List[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        customer=regular_customer,
        amount=subscription_with_active_status.amount,
        status="paid",
        paymentMethod="pix",
        subscription=subscription_with_active_status,
        due_date=subscription_with_active_status.next_payment_date,
        _quantity=subscription_with_active_status.quantity_recurrences,
    )


@pytest.fixture
def payment_with_status_paid(payment: Payment) -> Payment:
    payment.status = "paid"
    payment.save()
    return payment


@pytest.fixture
def subscription(regular_user: User, regular_customer: Customer) -> Subscription:
    return baker.make(
        "Subscription",
        user=regular_user,
        status="active",
        recurrence_period=1,
        quantity_recurrences=12,
        amount=100,
    )


@pytest.fixture
def payment_with_status_scheduled(payment: Payment, subscription: Subscription) -> Payment:
    payment.subscription = subscription
    payment.status = "scheduled"
    payment.save()
    return payment

@pytest.fixture
def payment_with_retry_scheduled(payment_with_status_scheduled: Payment) -> Payment:
    payment_with_status_scheduled.retry_scheduled = True
    payment_with_status_scheduled.save()

    return payment_with_status_scheduled

@pytest.fixture
def payment_without_postback_url(payment: Payment) -> Payment:
    payment.postbackUrl = None
    payment.save()
    return payment

@pytest.fixture
def refused_payment_log(payment: Payment, acquirer_with_priority) -> RefusedPaymentLog:
    acquirer = Acquirer.objects.first()
    payment.acquirer = acquirer
    payment.save()

    return baker.make(
        "RefusedPaymentLog",
        payment=payment,
        acquirer=acquirer,
        amount=payment.amount,
        reason="Test reason",
        status="refused",
    )


@pytest.fixture
def pix_enabled_acquirer_with_priority():
    Acquirer.objects.all().update(
        enabled=True,
        pixEnabled=True,
    )

    for priority, acquirer in enumerate(Acquirer.objects.all()):
        acquirer.pixPriority = priority + 1
        acquirer.save()


@pytest.fixture
def ticket_enabled_acquirer_with_priority():
    Acquirer.objects.all().update(
        enabled=True,
        ticketEnabled=True,
    )

    for priority, acquirer in enumerate(Acquirer.objects.all()):
        acquirer.ticketPriority = priority + 1
        acquirer.save()


@pytest.fixture
def user_with_custom_pix_acquirer(payment_data: Payment, pix_enabled_acquirer_with_priority, user: User) -> User:
    latest_acquirer = Acquirer.objects.filter(pixEnabled=True).order_by("pixPriority", "id").last()
    user.pixAcquirer = latest_acquirer
    user.save(update_fields=["pixAcquirer"])
    return user


@pytest.fixture
def user_with_custom_ticket_acquirer(payment_data: Payment, ticket_enabled_acquirer_with_priority, user: User) -> User:
    latest_acquirer = Acquirer.objects.filter(ticketEnabled=True).order_by("ticketPriority", "id").last()
    user.boletoAcquirer = latest_acquirer
    user.save(update_fields=["boletoAcquirer"])
    return user

@pytest.fixture
def acquirer() -> Acquirer:
    return baker.make(
        "Acquirer",
        name="Acquirer Test",
        enabled=True,
    )


@pytest.fixture
def transactions(regular_user: User, regular_customer: Customer, acquirer: Acquirer) -> list[Payment]:
    return [
        baker.make("Payment", paymentMethod="pix", status="paid", amount=100, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
        baker.make("Payment", paymentMethod="pix", status="paid", amount=100, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
        baker.make("Payment", paymentMethod="credit_card", status="paid", amount=200, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
        baker.make("Payment", paymentMethod="credit_card", status="paid", amount=200, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
        baker.make("Payment", paymentMethod="boleto", status="paid", amount=300, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
        baker.make("Payment", paymentMethod="boleto", status="paid", amount=300, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
        baker.make("Payment", paymentMethod="nupay", status="paid", amount=400, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
        baker.make("Payment", paymentMethod="openfinance_nubank", status="paid", amount=500, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
        baker.make("Payment", paymentMethod="credit_card", status="chargedback", amount=150, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
        baker.make("Payment", paymentMethod="pix", status="chargedback", amount=100, user=regular_user,
                   customer=regular_customer, acquirer=acquirer),
    ]


@pytest.fixture
def default_fee() -> DefaultFee:
    return baker.make(
        "DefaultFee",
        pixFixed=0.01,
        pixPercentage=0.01,
        creditCardFixed=0.01,
    )


@pytest.fixture
def regular_fee_from_admin_user(admin_user: User) -> Fee:
    return baker.make(
        "Fee",
        user=admin_user,
    )

@pytest.fixture
def fee_for_regular_user(regular_user: User) -> Fee:
    return baker.make(
        "Fee",
        user=regular_user,
        pixFixed=1.99,
        pixPercentage=0.34,
        pixRelease = 16,
        pixReleaseReserve = 11,
        pixReserve = 1.23,
        antecipationReleasePix = 14
    )

@pytest.fixture
def request_factory_from_admin(admin_user: User):
    view = WithdrawalViewSet()
    request_factory = RequestFactory().get(reverse("withdrawal-list-create"))
    request_factory.user = admin_user
    view.request = request_factory
    return request_factory, view


@pytest.fixture
def request_factory_from_anonymous_user():
    view = WithdrawalViewSet()
    request_factory = RequestFactory().get(reverse("withdrawal-list-create"))
    request_factory.user = None
    view.request = request_factory
    return request_factory, view


@pytest.fixture
def staff_user(faker) -> User:
    return baker.make(
        "user.User",
        username=faker.name(),
        email=faker.email(),
        cpf = '11111111111',
        password="P@ssW0rd",
        is_staff=True,
        is_active=True,
    )


@pytest.fixture
def logged_in_as_staff_user(client: APIClient, staff_user: User) -> APIClient:
    client.force_authenticate(user=staff_user)
    return client

@pytest.fixture
def logged_in_as_staff_user_with_permissions(client: APIClient, staff_user_with_permissions: User) -> APIClient:
    client.force_authenticate(user=staff_user_with_permissions)
    return client

@pytest.fixture
def logged_in_as_user_with_group(client: APIClient, user_with_group: User) -> APIClient:
    client.force_authenticate(user=user_with_group)
    return client

@pytest.fixture
def api_endpoint_permission(staff_user_with_permissions: User) -> APIEndpoint:
    permission = staff_user_with_permissions.user_permissions.first()
    return baker.make(
        APIEndpoint,
        name="Test API Endpoint",
        path="/api/v1/test",
        required_permission=permission,
        description="Test description",
    )

@pytest.fixture
def api_endpoint_permission_full_access(
    api_endpoint_permission: APIEndpoint,
    group_with_permissions: Group,
) -> APIEndpoint:
    api_endpoint_permission.required_permission = Permission.objects.get(codename="full_access")
    api_endpoint_permission.save(update_fields=["required_permission"])
    return api_endpoint_permission

@pytest.fixture
def request_factory_from_staff_user(staff_user: User):
    view = WithdrawalViewSet()
    request_factory = RequestFactory().get(reverse("withdrawal-list-create"))
    request_factory.user = staff_user
    view.request = request_factory
    view.queryset = Withdrawal.objects.all()
    return request_factory, view

@pytest.fixture
def staff_user_with_permissions(staff_user: User) -> User:
    permission = Permission.objects.get(content_type__app_label='user', codename='view_withdrawal')
    staff_user.user_permissions.add(permission)
    return staff_user

@pytest.fixture
def user_view_set() -> UserViewSet:
    return UserViewSet()


@pytest.fixture
def payment_dispute(payment: Payment) -> PaymentDisputeArea:
    return baker.make(
        "PaymentDisputeArea",
        payment=payment,
        description="Test reason",
    )

@pytest.fixture
def payment_dispute_with_sent_status(payment_data) -> PaymentDisputeArea:
    return baker.make(
        "PaymentDisputeArea",
        payment=payment_data,
        description="Test reason",
        status="enviado",
    )


@pytest.fixture
def payment_dispute_with_files(payment_dispute: PaymentDisputeArea) -> PaymentDisputeUploadedFile:
    file_path = PAYMENT_DISPUTE_UPLOADED_FILE_PATH
    try:
        with open(file_path, "rb") as file:
            file_content = file.read()
    except FileNotFoundError:
        file_content = b"dummy content"

    in_memory_file = io.BytesIO(file_content)
    in_memory_file.name = "comprovante.pdf"

    return baker.make(
        "PaymentDisputeUploadedFile",
        payment_dispute=payment_dispute,
        file=File(in_memory_file, name="comprovante.pdf"),
    )


@pytest.fixture
def charged_back_payment(payment: Payment) -> Payment:
    payment.status = "chargedback"
    payment.chargedbackAt = timezone.now()
    payment.save(update_fields=["status", "chargedbackAt"])
    return payment


@pytest.fixture
def second_payment(regular_user: User, regular_customer: Customer) -> Payment:
    return baker.make(
        "Payment",
        customer=regular_customer,
        user=regular_user,
        amount=100,
        status="pending",
        paymentMethod="credit_card",
    )


@pytest.fixture
def second_payment_charged_back(second_payment: Payment) -> Payment:
    second_payment.status = "chargedback"
    second_payment.chargedbackAt = timezone.now()
    second_payment.save(update_fields=["status", "chargedbackAt"])
    return second_payment


@pytest.fixture
def payment_filter_set() -> PaymentFilterset:
    return PaymentFilterset


@pytest.fixture
def payment_dispute_filter_set() -> PaymentDisputeFilterset:
    return PaymentDisputeFilterset

@pytest.fixture
def user_eligible_for_health_score(faker, regular_user, regular_customer) -> User:
    user = baker.make(
        "user.User",
        username=faker.name(),
        email=faker.email(),
    )

    baker.make(
        "Payment",
        user=user,
        customer=regular_customer,
        status="paid",
        paymentMethod="pix",
        amount=100,
        _quantity=20,
    )

    return user


@pytest.fixture
def mixed_status_payments(
    regular_user: User,
    regular_customer: Customer,
    user_eligible_for_health_score: User,
):
    baker.make(
        "Payment",
        user=user_eligible_for_health_score,
        amount=100,
        paymentMethod="credit_card",
        customer=regular_customer,
        status="chargedback",
        chargedbackAt=timezone.now(),
        _quantity=3,
    )

    baker.make(
        "Payment",
        user=user_eligible_for_health_score,
        amount=100,
        paymentMethod="pix",
        customer=regular_customer,
        status="MED",
        _quantity=5,
    )

    baker.make(
        "Payment",
        user=user_eligible_for_health_score,
        amount=100,
        paymentMethod="pix",
        customer=regular_customer,
        status="refunded",
        _quantity=7,
    )

@pytest.fixture
def low_score_payments(
    user_eligible_for_health_score: User,
    regular_customer: Customer
):
    baker.make(
        "Payment",
        user=user_eligible_for_health_score,
        amount=100,
        paymentMethod="credit_card",
        customer=regular_customer,
        status="chargedback",
        chargedbackAt=timezone.now(),
        _quantity=10,
    )

    baker.make(
        "Payment",
        user=user_eligible_for_health_score,
        amount=100,
        paymentMethod="pix",
        customer=regular_customer,
        status="MED",
        _quantity=50,
    )

    baker.make(
        "Payment",
        user=user_eligible_for_health_score,
        amount=100,
        paymentMethod="pix",
        customer=regular_customer,
        status="refunded",
        _quantity=7,
    )


@pytest.fixture
def pix_payment_with_wrong_status(regular_user: User, acquirer: Acquirer, regular_customer: Customer) -> Payment:
    return baker.make(
        "Payment",
        paymentMethod="pix",
        status="chargedback",
        user=regular_user,
        customer=regular_customer,
        acquirer=acquirer,
    )


@pytest.fixture
def cred_card_payment_with_chargedback_status(regular_user: User, acquirer: Acquirer,
                                              regular_customer: Customer) -> Payment:
    return baker.make(
        "Payment",
        paymentMethod="credit_card",
        status="chargedback",
        user=regular_user,
        customer=regular_customer,
        acquirer=acquirer,
    )

@pytest.fixture
def pix_payment_with_paid_status(regular_user: User, acquirer: Acquirer, regular_customer: Customer) -> Payment:
    return baker.make(
        "Payment",
        paymentMethod="pix",
        status="paid",
        user=regular_user,
        customer=regular_customer,
        acquirer=acquirer,
    )

@pytest.fixture
def credit_card_payment_with_paid_status(regular_user: User, acquirer: Acquirer, regular_customer: Customer) -> Payment:
    return baker.make(
        "Payment",
        paymentMethod="credit_card",
        status="paid",
        user=regular_user,
        customer=regular_customer,
        acquirer=acquirer,
    )

@pytest.fixture
def user_group() -> Group:
    return baker.make(
        "Group",
        name="Test Group",
    )

@pytest.fixture
def group_with_permissions(user_group: Group) -> Group:
    permission = Permission.objects.filter(codename="full_access").first()
    user_group.permissions.add(permission)
    user_group.save()
    return user_group

@pytest.fixture
def user_with_group(group_with_permissions: Group) -> User:
    user = baker.make(
        "user.User",
    )
    user.groups.add(group_with_permissions)
    return user

@pytest.fixture
def pending_balance(payment: Payment, regular_user: User):
    return baker.make("PendingBalance", payment=payment, user=regular_user, amount=50, isReserve=False)

@pytest.fixture
def pending_balance_reserve(payment: Payment, regular_user: User):
    return baker.make("PendingBalance", payment=payment, user=regular_user, amount=50, isReserve=True)

@pytest.fixture
def user_without_cnpj() -> User:
    return baker.make(
        "user.User",
        email='<EMAIL>',
        username='testuser',
        password='testpassword',  # noqa: S106
        cpf='11111111111',
        idwallSdkToken='idwallSdkToken',
        first_name='usuário',
        last_name='de teste',
        birthDate=datetime.date(2000, 1, 1),
        motherName='Maria do teste',
        cellphone='11982746352',
        cep='04284920',
        number='112',
        address='Rua de Teste',
        city='Cidade teste',
        state='estado teste',
        status='gateway_pending'
    )

@pytest.fixture
def user_with_blank_cnpj() -> User:
    return baker.make(
        "user.User",
        email='<EMAIL>',
        username='testuser2',
        password='testpassword2',  # noqa: S106
        cpf='2222222222',
        cnpj='',
        idwallSdkToken='idwallSdkToken',
        first_name='usuário',
        last_name='de teste',
        birthDate=datetime.date(2000, 1, 1),
        motherName='Maria do teste',
        cellphone='11982746352',
        cep='04284920',
        number='112',
        address='Rua de Teste',
        city='Cidade teste',
        state='estado teste',
        status='gateway_pending'
    )

@pytest.fixture
def user_pending_with_cnpj() -> User:
    return baker.make(
        "user.User",
        email='<EMAIL>',
        username='testuser3',
        password='testpassword3',  # noqa: S106
        cpf='3333333333',
        cnpj='123456789',
        idwallSdkToken='idwallSdkToken',
        first_name='usuário',
        last_name='de teste',
        birthDate=datetime.date(2000, 1, 1),
        motherName='Maria do teste',
        cellphone='11982746352',
        cep='04284920',
        number='112',
        address='Rua de Teste',
        city='Cidade teste',
        state='estado teste',
        status='pending'
    )

@pytest.fixture
def user_gateway_pending_with_cnpj() -> User:
    return baker.make(
        "user.User",
        email='<EMAIL>',
        username='testuser4',
        password='testpassword4',  # noqa: S106
        cpf='4444444444',
        cnpj='123456789',
        idwallSdkToken='idwallSdkToken',
        first_name='usuário',
        last_name='de teste',
        birthDate=datetime.date(2000, 1, 1),
        motherName='Maria do teste',
        cellphone='11982746352',
        cep='04284920',
        number='112',
        address='Rua de Teste',
        city='Cidade teste',
        state='estado teste',
        status='gateway_pending'
    )


@pytest.fixture
def user_gateway_pending_with_cnpj_2() -> User:
    return baker.make(
        "user.User",
        email='<EMAIL>',
        username='testuser5',
        password='testpassword5',  # noqa: S106
        cpf='5555555555',
        cnpj='123456789',
        idwallSdkToken='idwallSdkToken',
        first_name='usuário',
        last_name='de teste',
        birthDate=datetime.date(2000, 1, 1),
        motherName='Maria do teste',
        cellphone='11982746352',
        cep='04284920',
        number='112',
        address='Rua de Teste',
        city='Cidade teste',
        state='estado teste',
        status='gateway_pending'
    )


@pytest.fixture
def user_gateway_pending_with_cnpj_3() -> User:
    return baker.make(
        "user.User",
        email='<EMAIL>',
        username='testuser6',
        password='testpassword6',  # noqa: S106
        cpf='6666666666',
        cnpj='123456789',
        idwallSdkToken='idwallSdkToken',
        first_name='usuário',
        last_name='de teste',
        birthDate=datetime.date(2000, 1, 1),
        motherName='Maria do teste',
        cellphone='11982746352',
        cep='04284920',
        number='112',
        address='Rua de Teste',
        city='Cidade teste',
        state='estado teste',
        status='gateway_pending'
    )

@pytest.fixture
def admin_user_approved_idwall_profile(admin_user:User) -> IdWallProfile:
    return baker.make(
        "user.IdWallProfile",
        user=admin_user,
        cpf = admin_user.cpf,
        status = 'approved'
    )

@pytest.fixture
def five_credit_card_paid_payments(regular_user: User, regular_customer: Customer, acquirer: Acquirer) -> list[Payment]:
    return baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        liquidAmount=100,
        paymentMethod="credit_card",
        customer=regular_customer,
        status="paid",
        paidAt=datetime.datetime.now(),
        acquirer=acquirer,
        _quantity=5,
    )

@pytest.fixture
def two_payments_with_chargeback_status(five_credit_card_paid_payments: list[Payment]):
    for _ in range(2):
        payment = five_credit_card_paid_payments.pop()
        payment.chargeback()

@pytest.fixture
def idwall_integration(admin_user: User) -> Integration:
    return baker.make(
        'user.Integration',
        user=admin_user,
        type='idwall',
        enabled=True,
        integration_type='kyc',
        keys={
            'base_url': 'https://base.url',
            'auth_token': 'auth_token',
            'webhook_secret_token': 'mysecret',
            'rg_validation_flow_id': '0123-rg_validation_flow_id',
            'cnh_validation_flow_id': '4567-cnh_validation_flow_id',
        },
        fields=['base_url', 'auth_token', 'webhook_secret_token'],
    )

@pytest.fixture
def idwall_api(idwall_integration: Integration) -> IdWall:
    return IdWall(idwall_integration)


@pytest.fixture
def plan_fee() -> PlanFee:
    return baker.make(
        "gateway.PlanFee",
        name="Test Plan Fee",
        enabled=True,
    )

@pytest.fixture
def staff_user_approved_idwall_profile(staff_user:User) -> IdWallProfile:
    return baker.make(
        "user.IdWallProfile",
        user= staff_user,
        cpf = staff_user.cpf,
        status = 'approved'
    )


@pytest.fixture
def efi_acquirer() -> Acquirer:
    return baker.make("Acquirer", pixEnabled=True, acquirer_gateway='efi', name="Efi Acquirer")


@pytest.fixture
def efi_api_instance(mocker, acquirer) -> Efi:
    mocker.patch("gateway.company.efi.Efi.setAuthBasic")
    mocker.patch("gateway.company.efi.Efi.setAuth")
    mocker.patch("gateway.company.efi.Efi.makeCert")
    mocker.patch("gateway.company.efi.Efi.get_error_message")
    mocker.patch("gateway.company.efi.Efi.getPixAccessToken")
    mocker.patch("gateway.company.efi.Efi.getAccessToken")
    mocker.patch("gateway.company.efi.Efi.chargeOneStep")
    mocker.patch("gateway.company.efi.Efi.createTransaction")
    mocker.patch("gateway.company.efi.Efi.getNotification")
    mocker.patch("gateway.company.efi.Efi.getTransaction")
    mocker.patch("gateway.company.efi.Efi.refundTransaction")
    mocker.patch("gateway.company.efi.Efi.withdrawPix")
    mocker.patch("gateway.company.efi.Efi.getPix")
    mocker.patch("gateway.company.efi.Efi.balance")
    mocker.patch("gateway.company.efi.Efi.refundPix")
    mocker.patch("gateway.company.efi.Efi.setWebhook")
    efi_instance = Efi(acquirer)
    mocker.patch.object(efi_instance, 'getMeds', return_value={
        'infracoes': [
            {'endToEndId': 'e2e1', 'status': 'APROVADA'},
            {'endToEndId': 'e2e2', 'status': 'REJEITADA'}
        ]
    })
    return efi_instance


@pytest.fixture
def payments_with_end_to_end_id(regular_user: User, regular_customer: Customer, efi_acquirer: Acquirer) -> tuple[
    Payment, Payment]:
    paid_payment = baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="pix",
        customer=regular_customer,
        status="paid",
        e2eId="e2e1",
        acquirer=efi_acquirer,
    )

    charged_back_payment = baker.make(
        "Payment",
        user=regular_user,
        amount=100,
        paymentMethod="pix",
        customer=regular_customer,
        status="chargedback",
        e2eId="e2e2",
        acquirer=efi_acquirer,
    )

    return paid_payment, charged_back_payment



@pytest.fixture
def acquirer_with_risk_dilution_percentage(acquirer: Acquirer) -> Acquirer:
    acquirer.risk_dilution_percentage = 0.5
    acquirer.save(update_fields=["risk_dilution_percentage"])
    return acquirer


@pytest.fixture
def action_position() -> Position:
    return baker.make(
        "payment.Position",
        x=0,
        y=0,
    )


@pytest.fixture
def flow_position() -> Position:
    return baker.make(
        "payment.Position",
        x=2.465,
        y=3,
    )


@pytest.fixture
def randomizer_flow_action(acquirer: Acquirer, action_position: Position) -> FlowActions:
    return baker.make(
        "payment.FlowActions",
        position=action_position,
        type="randomizer",
        options=[
            {
                "percentage": "0.5",
                "acquirerId": str(acquirer.id),
            }
        ]
    )


@pytest.fixture
def risk_dilution_additional_flow(randomizer_flow_action: FlowActions, acquirer: Acquirer, flow_position: Position) -> AdditionalFlows:
    risk_dilution_flow = baker.make(
        "payment.AdditionalFlows",
        name="Risk Dilution Flow",
        status="active",
        triggerType="transaction",
        type="trigger",
        position=flow_position,
        settings={
            "triggerType": "transaction",
            "type": "trigger",
            "actions": [
                {
                    "type": "randomizer",
                    "options": [
                        {
                            "percentage": "0.5",
                            "acquirerId": "1"
                        },
                        {
                            "percentage": "0.5",
                            "acquirerId": "2"
                        }
                    ]
                }
            ]
        }
    )
    risk_dilution_flow.actions.add(randomizer_flow_action)
    return risk_dilution_flow


@pytest.fixture
def two_acquirers_with_risk_dilution_percentage(acquirer: Acquirer) -> tuple[Acquirer]:
    acquirer.risk_dilution_percentage = 0.7
    acquirer.creditCardEnabled = True
    acquirer.pixEnabled = True
    acquirer.save(update_fields=["risk_dilution_percentage", "creditCardEnabled", "pixEnabled"])
    second_acquirer = baker.make(
        "Acquirer",
        risk_dilution_percentage=0.3,
        creditCardEnabled=True,
        pixEnabled=True,
    )

    return acquirer, second_acquirer


@pytest.fixture
def two_acquires_without_risk_dilution_percentage(two_acquirers_with_risk_dilution_percentage: tuple[Acquirer]):
    acquirer, second_acquirer = two_acquirers_with_risk_dilution_percentage
    acquirer.risk_dilution_percentage = 0
    acquirer.save(update_fields=["risk_dilution_percentage"])
    second_acquirer.risk_dilution_percentage = 0
    second_acquirer.save(update_fields=["risk_dilution_percentage"])
    return acquirer, second_acquirer


@pytest.fixture
def access_log(admin_user: User) -> AccessLog:
    return baker.make(
        "AccessLog",
        user=admin_user,
        ip_address="127.0.0.1",
        user_agent="Mozilla/5.0",
    )


@pytest.fixture
def payment_from_pagarme(payment: Payment, regular_customer) -> Payment:
    acquirer = Acquirer.objects.get(acquirer_gateway="pagarme")
    payment.acquirer = acquirer
    payment.customer = regular_customer
    payment.paymentMethod = 'credit_card'
    payment.save(update_fields=["paymentMethod", "acquirer", "customer"], skip_hooks=True)
    return payment


@pytest.fixture
def payment_reprocessing_history(
    payment: Payment,
    regular_user: User,
    acquirer: Acquirer,
    payment_from_pagarme: Payment,
) -> PaymentReprocessingHistory:
    return baker.make(
        "PaymentReprocessingHistory",
        payment=payment,
        acquirer=acquirer,
        user=regular_user,
        status="pending",
        reason="Test reason",
    )
@pytest.fixture
def serpro_integration(admin_user: User) -> Integration:
    return baker.make(
        'user.Integration',
        user=admin_user,
        type='serpro',
        enabled=True,
        integration_type='pre_kyc',
        keys={
            'base_url': 'https://base.url',
            'consumer_key': 'consumer_key',
            'consumer_secret': 'consumer_secret',
        },
        fields=['base_url', 'consumer_key', 'consumer_secret'],
    )


@pytest.fixture
def request_plan_fee(plan_fee: PlanFee, staff_user:User) -> RequestPlanFee:
    return baker.make(
        "compliance.RequestPlanFee",
        plan_fee=plan_fee,
        user=staff_user,
    )


@pytest.fixture
def user_staff_with_group_permissions(staff_user:User, group_with_permissions: Group) -> User:
    staff_user.groups.add(group_with_permissions)
    return staff_user



@pytest.fixture
def staff_user_with_plan_fee_permission (staff_user: User) -> User:
    permission = Permission.objects.get(content_type__app_label='compliance', codename='manage_requestplanfee')
    staff_user.user_permissions.add(permission)
    return staff_user


@pytest.fixture
def logged_in_as_user_not_staff(client: APIClient, user: User) -> APIClient:
    client.force_authenticate(user=user)
    return client

@pytest.fixture
def logged_in_as_staff_user_with_plan_fee_permission(client: APIClient, staff_user_with_plan_fee_permission: User) -> APIClient:
    client.force_authenticate(user=staff_user_with_plan_fee_permission)
    return client

@pytest.fixture
def clear_cache():
    cache.clear()


@pytest.fixture
def default_fee_for_calculate_interest(regular_user: User) -> Fee:
    fields = {
        "pixFixed": "5.49",
        "pixPercentage": "0.01",
        "pixRelease": 0,
        "ticketFixed": "2.49",
        "ticketPercentage": "4.99",
        "ticketRelease": 2,
        "picpayFixed": None,
        "picpayPercentage": None,
        "picpayRelease": None,
        "nupayFixed": None,
        "nupayPercentage": None,
        "nupayRelease": None,
        "googlepayFixed": None,
        "googlepayPercentage": None,
        "googlepayRelease": None,
        "applepayFixed": None,
        "applepayPercentage": None,
        "applepayRelease": None,
        "openFinanceNubankFixed": None,
        "openFinanceNubankPercentage": None,
        "openFinanceNubankRelease": None,
        "creditCardFixed": "2.49",
        "creditCardPercentage": "2.99",
        "creditCardRelease": 10,
        "creditCard2x": "11.49",
        "creditCard3x": "13.49",
        "creditCard4x": "15.49",
        "creditCard5x": "16.89",
        "creditCard6x": "17.89",
        "creditCard7x": "18.49",
        "creditCard8x": "19.49",
        "creditCard9x": "26.99",
        "creditCard10x": "29.90",
        "creditCard11x": "32.89",
        "creditCard12x": "36.82",
        "googlepay2x": None,
        "googlepay3x": None,
        "googlepay4x": None,
        "googlepay5x": None,
        "googlepay6x": None,
        "googlepay7x": None,
        "googlepay8x": None,
        "googlepay9x": None,
        "googlepay10x": None,
        "googlepay11x": None,
        "googlepay12x": None,
        "applepay2x": None,
        "applepay3x": None,
        "applepay4x": None,
        "applepay5x": None,
        "applepay6x": None,
        "applepay7x": None,
        "applepay8x": None,
        "applepay9x": None,
        "applepay10x": None,
        "applepay11x": None,
        "applepay12x": None,
        "creditCardReserve": None,
        "creditCardReleaseReserve": None,
        "ticketReserve": None,
        "ticketReleaseReserve": None,
        "pixReserve": None,
        "pixReleaseReserve": None,
        "picpayReserve": None,
        "picpayReleaseReserve": None,
        "nupayReserve": None,
        "nupayReleaseReserve": None,
        "googlepayReserve": None,
        "googlepayReleaseReserve": None,
        "applepayReserve": None,
        "applepayReleaseReserve": None,
        "openFinanceNubankReserve": None,
        "openFinanceNubankReleaseReserve": None,
        "antecipationReserve": None,
        "antecipationReleaseReserve": None,
        "antecipationPercentage": "6.99",
        "antecipationReleaseCreditCard": 2,
        "antecipationReleaseTicket": 1,
        "antecipationReleasePix": 1,
        "antecipationReleasePicpay": None,
        "antecipationReleaseNupay": None,
        "antecipationReleaseGooglepay": None,
        "antecipationReleaseApplepay": None,
        "antecipationReleaseOpenFinanceNubank": None,
        "prechargebackFixed": "0.01",
        "chargebackFixed": "0.01",
        "withdrawalFee": "2.99",
        "enabled": False
    }

    return baker.make("gateway.Fee", user=regular_user, **fields)

@pytest.fixture
def subscription_with_active_status_unlimited_recurrences(
    regular_user: User,
    regular_customer: Customer,
) -> Subscription:
    return baker.make(
        "Subscription",
        user=regular_user,
        status="active",
        recurrence_period=2,
        quantity_recurrences=-1,
        amount=20,
    )


@pytest.fixture
def first_paid_subscription_payment(
    subscription_with_active_status_unlimited_recurrences: Subscription,
    regular_user: User,
    regular_customer: Customer,
    acquirer: Acquirer
) -> Payment:
    date = timezone.now()
    return baker.make(
        "Payment",
        user=regular_user,
        customer=regular_customer,
        amount=subscription_with_active_status_unlimited_recurrences.amount,
        status="paid",
        paymentMethod="credit_card",
        subscription=subscription_with_active_status_unlimited_recurrences,
        paidAt= date,
        acquirer=acquirer,
        installments=2,
    )