# Dockerfile django
FROM python:3.12

RUN apt-get update && apt-get install -y zbar-tools less fonts-firacode

# Create the user
ARG USERNAME=vscode
ARG USER_UID=1001
ARG USER_GID=$USER_UID

RUN groupadd --gid $USER_GID $USERNAME \
&& useradd --uid $USER_UID --gid $USER_GID -s /bin/bash -m $USERNAME \
#
# [Optional] Add sudo support. Omit if you don't need to install software after connecting.
&& apt-get update \
&& apt-get install -y sudo \
&& echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
&& chmod 0440 /etc/sudoers.d/$USERNAME

# Install UV package manager a pip replacement
RUN curl -LsSf https://astral.sh/uv/0.7.2/install.sh | UV_INSTALL_DIR=/usr/bin sh

COPY entrypoints/*.sh /tmp/entrypoints/
RUN chmod +x /tmp/entrypoints/*.sh

COPY requirements.txt /tmp

RUN uv pip install pytest pytest-cov ruff awscli-local --system \
    && uv pip install -r /tmp/requirements.txt --system

USER $USERNAME