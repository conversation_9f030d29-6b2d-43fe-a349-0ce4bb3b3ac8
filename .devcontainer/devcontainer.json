// For format details, see https://aka.ms/vscode-remote/devcontainer.json or this file's README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.202.3/containers/python-3
{
	"name": "cakto-split",
	"dockerComposeFile": [
		"./docker-compose.yml"
	],
	"service": "cakto-split",
	"shutdownAction": "stopCompose",
	"workspaceFolder": "/workspace",
	"features": {
		"ghcr.io/devcontainers/features/aws-cli:1": {}
	},
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-python.python",
				"ms-python.vscode-pylance",
				"charliermarsh.ruff",
				"johnpapa.vscode-peacock",
				"shardulm94.trailing-spaces",
				"humao.rest-client",
				"mhutchie.git-graph",
				"donjayamanne.githistory",
				"codeium.codeium",
				"github.vscode-github-actions",
				"github.vscode-pull-request-github"
			],
			"settings": {
				"terminal.integrated.profiles.linux": {
					"bash": {
						"path": "/bin/bash"
					}
				},
				"terminal.integrated.fontFamily": "Fira Code, monospace",
				"editor.fontFamily": "Fira Code, Consolas, 'Courier New', monospace",
				"[python]": {
					"editor.formatOnSave": false,
					"editor.defaultFormatter": "charliermarsh.ruff",
					"editor.formatOnSaveMode": "file",
					"editor.rulers": [
						100
					]
				},
				"workbench.colorCustomizations": {
					"editorRuler.foreground": "#ff4081"
				},
				"python.defaultInterpreterPath": "/usr/local/bin/python",
				"python.languageServer": "Pylance",
				"python.analysis.typeCheckingMode": "basic",
				"python.analysis.diagnosticMode": "workspace",
				"python.analysis.completeFunctionParens": true,
				"python.testing.pytestArgs": [
					"src"
				]
			}
		}
	},
	"remoteEnv": {},
	"mounts": [],
	"forwardPorts": [
		"cakto-split:8000",
		"redis:6379",
		"db:5432"
	],
	"postCreateCommand": "/tmp/entrypoints/development.sh"
}