services:

# WEB
  cakto-split:
    container_name: cakto-split-devcontainer
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      - ..:/workspace:cached
    command: sleep infinity
    environment:
      - AWS_ENDPOINT_URL=http://localstack:4566
    env_file:
      - ../.env.development
    depends_on:
      - redis
      - db
      - localstack

# REDIS
  redis:
    container_name: redis-devcontainer
    image: redis:7.4-alpine3.20

# DATABASE
  db:
    container_name: postgres-devcontainer
    image: postgres:14.13-alpine3.20
    restart: always
    shm_size: 128mb
    environment:
      POSTGRES_USER: caktouser
      POSTGRES_PASSWORD: caktopassword
      POSTGRES_DB: caktodb

  # S3
  localstack:
    container_name: "localstack-devcontainer"
    image: localstack/localstack:3.7.2
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
    environment:
      - SERVICES=s3
      - DEBUG=1
      - AWS_ACCESS_KEY_ID=aws_s3_access_key_id
      - AWS_SECRET_ACCESS_KEY=aws_secret_access_key
      - DOCKER_HOST=unix:///var/run/docker.sock
