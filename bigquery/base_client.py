from abc import ABC

import waffle
from google.cloud import bigquery
from google.oauth2 import service_account
from django.conf import settings
from google.cloud.exceptions import NotFound

from bigquery.exceptions import BigQueryExportPaymentInsertRowError
from bigquery.payment_schema import PAYMENT_SCHEMA_ITEMS


if waffle.switch_is_active("start_bigquery_clint"):
    project_id = settings.GOOGLE_CLOUD_PROJECT_ID
    credential_file_path = settings.GOOGLE_APPLICATION_CREDENTIALS
    credentials = service_account.Credentials.from_service_account_file(credential_file_path)


class AbstractPaymentBaseClient(ABC):
    def __init__(self, table_name: str, dataset_name: str):
        """
        Initialize the client with the table and dataset name.
        @param table_name:
        @param dataset_name:
        """
        if not waffle.switch_is_active("start_bigquery_clint"):
            self.not_initialized = True
        else:
            self.not_initialized = False
            self.client: bigquery.Client = bigquery.Client(credentials=credentials, project=project_id)
            self.dataset: bigquery.Dataset = self.get_or_create_dataset(dataset_name)
            self.table: bigquery.Table = self.get_or_create_table(table_name, self.create_schema())
            self.qualified_table_name: str = f"{self.client.project}.{self.dataset.dataset_id}.payment"

    def get_or_create_dataset(self, dataset_name: str) -> bigquery.Dataset:
        """
        Get or create a dataset in the project.
        @param dataset_name:
        @return:
        """
        dataset_ref = bigquery.DatasetReference(
            project=self.client.project,
            dataset_id=f"{self.client.project}_{dataset_name}".replace("-", "_")
        )
        try:
            dataset = self.client.get_dataset(dataset_ref)
        except NotFound:
            dataset = bigquery.Dataset(dataset_ref)
            dataset.location = "southamerica-east1"
            dataset = self.client.create_dataset(dataset)

        return dataset

    def get_or_create_table(self, table_name: str, schema: list) -> bigquery.Table:
        """
        Get or create a table in the dataset.
        @param table_name:
        @param schema:
        @return:
        """
        table_id = f"{self.client.project}.{self.dataset.dataset_id}.{table_name}"
        table = bigquery.Table(table_id, schema=schema)
        try:
            bq_table = self.client.get_table(table)
        except NotFound:
            bq_table = self.client.create_table(table)
        return bq_table

    def create_schema(self) -> list[bigquery.SchemaField]:
        """
        Create the schema for the table.
        @return:
        """
        return [bigquery.SchemaField(name, field_type, mode=mode) for name, field_type, mode in PAYMENT_SCHEMA_ITEMS]

    def perform_query(self, query: str) -> bigquery.table.RowIterator:
        """
        Perform the query and wait for the result.
        @param query:
        @return:
        """
        return self.client.query_and_wait(query)

    def insert_rows(self, rows: list[dict]):
        """
        Insert rows into the table.
        @param rows:
        @return:
        """
        errors = self.client.insert_rows(self.table, rows)
        if errors:
            raise BigQueryExportPaymentInsertRowError(errors)

    @staticmethod
    def get_result_rows(query_result: bigquery.table.RowIterator) -> list[dict]:
        """
        Convert the query result to a list of dictionary.

        :param query_result: The result of the query execution.
        :return: A list of dictionary representation of the query result.
        """
        results = []
        for row in query_result:
            results.append(dict(row.items()))
        return results

    @staticmethod
    def query_to_dict(query_result: bigquery.table.RowIterator) -> dict:
        """
        Convert the query result to a dictionary.

        :param query_result: The result of the query execution.
        :return: A dictionary representation of the query result.
        """
        result_dict = {}
        for row in query_result:
            result_dict = dict(row.items())
        return result_dict

    def setup(self):
        if not waffle.switch_is_active("start_bigquery_clint"):
            return

        self.get_or_create_dataset(dataset_name="payment_dataset")
        self.get_or_create_table("payment", self.create_schema())
