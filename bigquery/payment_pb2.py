# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: payment.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rpayment.proto\"\xe1\x01\n\x07Payment\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0b\x61\x63quirer_id\x18\x02 \x01(\t\x12\x0e\n\x06\x61mount\x18\x03 \x01(\x02\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x12\n\ncreated_at\x18\x05 \x01(\x03\x12\x16\n\x0e\x63hargedback_at\x18\x06 \x01(\x03\x12\x16\n\x0epayment_method\x18\x07 \x01(\t\x12\x14\n\x0cinstallments\x18\x08 \x01(\x05\x12\x0e\n\x06profit\x18\t \x01(\x02\x12\x10\n\x08interest\x18\n \x01(\x02\x12\x0c\n\x04\x63ost\x18\x0b \x01(\x02\x12\x0b\n\x03\x66\x65\x65\x18\x0c \x01(\x02')



_PAYMENT = DESCRIPTOR.message_types_by_name['Payment']
Payment = _reflection.GeneratedProtocolMessageType('Payment', (_message.Message,), {
  'DESCRIPTOR' : _PAYMENT,
  '__module__' : 'payment_pb2'
  # @@protoc_insertion_point(class_scope:Payment)
  })
_sym_db.RegisterMessage(Payment)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PAYMENT._serialized_start=18
  _PAYMENT._serialized_end=243
# @@protoc_insertion_point(module_scope)
