# TODO (scpaes): ADD TO THE SCHEMA THE MISSING FIELDS (fine, gatewayFee, user_id)

PAYMENT_SCHEMA_ITEMS = [
    ("id", "STRING", "REQUIRED"),
    ("acquirer_id", "STRING", "REQUIRED"),
    ("user_id", "STRING", "REQUIRED"),
    ("amount", "FLOAT64", "REQUIRED"),
    ("status", "STRING", "REQUIRED"),
    ("created_at", "TIMESTAMP", "REQUIRED"),
    ("chargedback_at", "TIMESTAMP", "NULLABLE"),
    ("payment_method", "STRING", "REQUIRED"),
    ("installments", "INTEGER", "NULL<PERSON><PERSON>"),
    ("profit", "FLOAT64", "NULLABLE"),
    ("interest", "FLOAT64", "NULLABLE"),
    ("cost", "FLOAT64", "NULL<PERSON><PERSON>"),
    ("fee", "FLOAT64", "NULL<PERSON><PERSON>"),
    ("fine", "FLOAT64", "NULL<PERSON><PERSON>"),
    ("gatewayFee", "FLOAT64", "NU<PERSON><PERSON>LE"),
]
