from datetime import date

from django.db.models import Sum

from bigquery.base_client import AbstractPaymentBaseClient
from django.conf import settings

from user.models import Withdrawal


def get_payment_filter_where_start_end_user_acquirer(payment_filters: dict) -> str:
    payment_filter = ""

    if all(date_key in payment_filters for date_key in ['start_date', 'end_date']):
        start_date = payment_filters['start_date']
        end_date = payment_filters['end_date']
        payment_filter = f"AND created_at >= '{start_date}' AND created_at <= '{end_date}'"

    if 'user' in payment_filters:
        payment_filter += f"AND user_id = '{payment_filters['user']}'"

    if 'acquirer' in payment_filters:
        payment_filter += f"AND acquirer_id = '{payment_filters['acquirer']}'"

    return payment_filter


def get_withdrawal_filters(start_date: date, end_date: date):
    if start_date == end_date:
        filters = {'createdAt__date': start_date}
    else:
        filters = {
            'createdAt__date__gte': start_date,
            'createdAt__date__lte': end_date
        }
    return filters

def generate_sql_query_for_payment_methods(table_name: str, payment_filter: str) -> str:
    """
    Generate the SQL query to get the dashboard data from the table.
    @param payment_filter:
    @param table_name:
    @return:
    """

    sql_query = f"""
    SELECT payment_method,
           COUNT(id) AS  totalPaymentCount,
           SUM(amount) AS totalSalesAmount,
           SUM(CASE WHEN status = 'paid' THEN amount ELSE NULL END) AS paidSalesAmount,
           COUNT(CASE WHEN status = 'paid' THEN 1 ELSE NULL END) AS paidSalesCount,
           SUM(CASE WHEN status = 'paid' THEN fee ELSE NULL END) + 
           SUM(CASE WHEN status = 'paid' THEN interest ELSE NULL END) + 
           SUM(CASE WHEN status = 'paid' THEN fine ELSE NULL END) AS revenue,
           SUM(CASE WHEN status = 'paid' THEN fine ELSE NULL END) AS paidFine,
           SUM(CASE WHEN status = 'paid' THEN cost ELSE NULL END) AS fee,
           SUM(CASE WHEN status = 'paid' THEN profit ELSE NULL END) AS profit,
           ((100.0 * COUNT(CASE WHEN status = 'paid' THEN 1 ELSE NULL END)) / NULLIF(COUNT(id), 0)) AS conversion,
           SUM(CASE WHEN status = 'paid' THEN gateway_fee ELSE NULL END) AS totalGatewayFee,
           COUNT(CASE WHEN status = 'chargedback' THEN 1 ELSE NULL END) AS chargebackCount,
           SUM(CASE WHEN status = 'chargedback' THEN amount ELSE NULL END) AS chargebackAmount,
           SUM(CASE WHEN status = 'chargedback' THEN fine ELSE NULL END) AS chargebackFine,
           AVG(CASE WHEN status = 'paid' THEN amount END) AS averagePaidAmount,
           SUM(CASE WHEN status = 'paid' AND payment_method = 'credit_card'THEN interest ELSE NULL END) AS creditCardInterest,
           SUM(CASE WHEN status = 'refunded' THEN amount ELSE NULL END) AS refundedAmount,
           COUNT(CASE WHEN status = 'refunded' THEN 1 ELSE NULL END) AS refundedCount
    FROM `{table_name}`
    WHERE payment_method IN ('credit_card', 'boleto', 'pix', 'picpay', 'nupay', 'googlepay', 'applepay', 'openfinance_nubank')
        {payment_filter}
    GROUP BY payment_method;
    """
    return sql_query


def generate_sql_query_to_calculate_payment_installments(
    table_name: str,
    start_date: str = None,
    end_date: str = None
) -> str:
    """
    Generate the SQL query to calculate the profit for each installment.
    @param table_name:
    @param start_date:
    @param end_date:
    @return:
    """
    date_filter = ""
    if start_date and end_date:
        date_filter = f"AND created_at >= '{start_date}' AND created_at <= '{end_date}'"

    sql_query = f"""
    SELECT installments,
           SUM(interest) AS profit
    FROM `{table_name}`
    WHERE status = 'paid' AND payment_method = 'credit_card'
        {date_filter}
    GROUP BY installments
    ORDER BY installments ASC;
    """

    return sql_query


def generate_sql_query_for_revenue_data(table_name: str, start_date: str = None, end_date: str = None) -> str:
    """
    Generate the SQL query to get the revenue data from the table.
    @param table_name:
    @param start_date:
    @param end_date:
    @return:
    """
    date_filter = ""
    if start_date and end_date:
        date_filter = f"AND created_at >= '{start_date}' AND created_at <= '{end_date}'"

    sql_query = f"""
    SELECT COUNT(id) as totalSalesAmount,
           COUNT(CASE WHEN status = 'paid' THEN 1 ELSE NULL END) AS paidSalesAmount,
           AVG(CASE WHEN status = 'paid' THEN amount END) AS averagePaidAmount
           SUM(CASE WHEN status = 'paid' THEN fine ELSE NULL END) AS profit,
           SUM(CASE WHEN status = 'paid' THEN gatewayFee ELSE NULL END) AS totalGatewayFee,
    FROM `{table_name}`
    WHERE status != 'chargedback'
        {date_filter};
    """
    return sql_query


def generate_sql_query_for_chargeback_data(table_name: str, start_date: str = None, end_date: str = None) -> str:
    """
    Generate the SQL query to get the chargeback data from the table.
    @param table_name:
    @param start_date:
    @param end_date:
    @return:
    """
    date_filter = ""
    if start_date and end_date:
        date_filter = f"AND created_at >= '{start_date}' AND created_at <= '{end_date}'"

    sql_query = f"""
    SELECT payment_method,
        COUNT(id) AS totalCount,
        SUM(amount) AS totalAmount,
        SUM(fine) AS revenue,
    FROM `{table_name}`
    WHERE status = 'chargedback' AND payment_method IN ('credit_card', 'pix')
        {date_filter}
    GROUP BY payment_method;
    """
    return sql_query


def generate_sql_query_for_refunded_data(table_name: str, start_date: str = None, end_date: str = None) -> str:
    """
    Generate the SQL query to get the refunded data from the table.
    @param table_name:
    @param start_date:
    @param end_date:
    @return:
    """
    date_filter = ""
    if start_date and end_date:
        date_filter = f"AND created_at >= '{start_date}' AND created_at <= '{end_date}'"

    sql_query = f"""
    SELECT COUNT(id) AS totalRefundedAmount,
           SUM(amount) AS totalRefundedAmount,
    FROM `{table_name}`
    WHERE status = 'refunded'
        {date_filter};
    """
    return sql_query


def generate_sql_query_for_gateway_fee_data(table_name: str, start_date: str = None, end_date: str = None) -> str:
    """
    Generate the SQL query to get the gateway fee data from the table.
    @param table_name:
    @param start_date:
    @param end_date:
    @return:
    """
    date_filter = ""
    if start_date and end_date:
        date_filter = f"AND created_at >= '{start_date}' AND created_at <= '{end_date}'"

    sql_query = f"""
    SELECT
           SUM(gatewayFee) AS totalGatewayFee,
    FROM `{table_name}`
    WHERE status = 'paid'
        {date_filter};
    """
    return sql_query


class DashboardClient(AbstractPaymentBaseClient):
    @staticmethod
    def _get_payment_filters(start_date, end_date, user_id=None, acquirer=None) -> dict:
        filters = {}
        if user_id:
            filters['user'] = user_id
        if acquirer:
            filters['acquirer'] = acquirer

        # TODO (scpaes): Check if the date filters are correct, when start date is equal to end date.
        filters.update({
            'start_date': start_date,
            'end_date': end_date
        })

        return filters

    def _calculate_revenue_data(self, payment_data_grouped_by_payment_method: dict) -> dict:
        """
        Calculate the revenue data for the given date range.
        @param start_date:
        @param end_date:
        @return:
        """
        total_sales_amount = 0
        paid_sales_amount = 0
        average_paid_amount = 0

        for payment_method in ('credit_card', 'boleto', 'pix', 'picpay', 'nupay', 'googlepay', 'applepay', 'openfinance_nubank'):
            total_sales_amount += (
                payment_data_grouped_by_payment_method.get(payment_method, {})
                .get('totalPaymentCount', 0)
            )
            paid_sales_amount += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('paidSalesCount', 0)
            )
            average_paid_amount += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).
                get('averagePaidAmount', 0)
            )

        return {
            "totalSalesAmount": total_sales_amount,
            "paidSalesAmount": paid_sales_amount,
            "averageTicket": average_paid_amount,
        }

    def _calculate_payment_details_grouped_by_payment_method(self, payment_filter: str) -> dict:
        """
        Calculate the payment details for the given date range.
        @param payment_filter:
        @return:
        """
        query = generate_sql_query_for_payment_methods(
            table_name=self.qualified_table_name,
            payment_filter=payment_filter
        )
        query_result = self.perform_query(query)
        result = self.query_to_dict(query_result)
        return result

    @staticmethod
    def _calculate_chargeback_data(payment_data_grouped_by_payment_method: dict) -> dict:
        """
        Calculate the chargeback data for the given date range.
        @param payment_data_grouped_by_payment_method:
        @return:
        """
        result = {}
        for payment_method in ('credit_card', 'pix'):
            chargeback_count = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('chargebackCount', 0)
            )
            chargeback_amount = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('chargebackAmount', 0)
            )
            total_payments_count = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('totalPaymentCount', 0)
            )
            chargeback_percentage = (total_payments_count / chargeback_count * 100) if chargeback_count else 0

            if payment_method == 'credit_card':
                result['chargeback'] = {
                    'count': chargeback_count,
                    'amount': chargeback_amount,
                    'percentage': chargeback_percentage
                }
            else:
                result['med'] = {
                    'count': chargeback_count,
                    'amount': chargeback_amount,
                    'percentage': chargeback_percentage
                }

        return result

    def _calculate_refunded_data(self, payment_data_grouped_by_payment_method: dict) -> dict:
        """
        Calculate the refunded data for the given date range.
        @param start_date:
        @param end_date:
        @return:
        """
        total_refund_amount = 0
        total_refund_count = 0

        for payment_method in ('credit_card', 'boleto', 'pix', 'picpay', 'nupay', 'googlepay', 'applepay', 'openfinance_nubank'):
            total_refund_amount += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('refundedAmount', 0)
            )
            total_refund_count += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('refundedCount', 0)
            )
            total_payments_count = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('totalPaymentCount', 0)
            )

        return {
            "amount": total_refund_amount,
            "count": total_refund_count,
            "percentage": (total_refund_count / total_payments_count * 100) if total_payments_count else 0
        }

    def _calculate_installments_profit(self, filter: str) -> list[dict]:
        """
        Calculate the profit for each installment.
        @param filter:
        @return:
        """
        query = generate_sql_query_to_calculate_payment_installments(filter)
        query_result = self.perform_query(query)
        result = self.query_to_dict(query_result)

        installments_profit_dict = {data['installments']: data['profit'] for data in result}

        installments_profit = []
        for i in range(1, 13):
            profit = installments_profit_dict.get(i, 0)
            installments_profit.append({
                'installments': i,
                'profit': profit
            })

        return installments_profit

    def _calculate_payment_details(self, payment_data_grouped_by_payment_method: dict, filter: str) -> dict:
        result = {}
        method_map = {
            'boleto': 'boleto',
            'pix': 'pix',
            'credit_card': 'creditCard',
            'picpay': 'picpay',
            'nupay': 'nupay',
            'googlepay': 'googlepay',
            'applepay': 'applepay',
            'openfinance_nubank': 'openFinanceNubank'
        }
        for payment_method in ('credit_card', 'boleto', 'pix', 'picpay', 'nupay', 'googlepay', 'applepay', 'openfinance_nubank'):
            total_amount = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('totalSalesAmount', 0)
            )
            paid_amount = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('paidSalesAmount', 0)
            )
            paid_sales_count = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('paidSalesCount', 0)
            )
            revenue = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('revenue', 0)
            )
            payment_fee = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('fee', 0)
            )
            payment_profit = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('profit', 0)
            )
            total_payments_count = (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('totalPaymentCount', 0)
            )
            method_details = {
                "amount": total_amount,
                "paidAmount": paid_amount,
                "count": paid_sales_count,
                "conversion": (100 * paid_sales_count) / total_payments_count if total_payments_count else 0,
                "revenue": revenue,
                "fee": payment_fee,
                "profit": payment_profit
            }

            result_key = method_map[payment_method]
            result[result_key] = method_details

            if payment_method == 'credit_card':
                method_details['interest'] = (
                    payment_data_grouped_by_payment_method.get(payment_method, {}).get('creditCardInterest', 0)
                )
                installments_profit = self._calculate_installments_profit(filter)
                result[result_key]['installmentsProfit'] = installments_profit

        return result

    @staticmethod
    def _calculate_total(payment_data_grouped_by_payment_method: dict) -> dict:
        """
        Calculate the total for the payment details.
        @param payment_data_grouped_by_payment_method:
        @return:
        """
        total_amount = 0
        total_gateway_fee = 0
        total_fee = 0
        total_profit = 0
        for payment_method in ('credit_card', 'boleto', 'pix', 'picpay', 'nupay', 'googlepay', 'applepay', 'openfinance_nubank'):
            total_amount += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('paidSalesAmount', 0)
            )
            total_gateway_fee += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('totalGatewayFee', 0)
            )
            total_fee += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('fee', 0)
            )
            total_profit += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('profit', 0)
            )
        return {
            'summary': [
                {
                    'label': 'Receita',
                    'value': total_amount or 0
                },
                {
                    'label': 'Taxa de gateway',
                    'value': total_gateway_fee or 0
                }
            ],
            'volume': total_amount or 0,
            'revenue': total_fee or 0,
            'fee': total_gateway_fee or 0,
            'profit': total_profit or 0
        }

    def _calculate_gateway_fee_data(self, payment_data_grouped_by_payment_method: dict) -> dict:
        """
        Calculate the gateway fee data for the given date range.
        @param start_date:
        @param end_date:
        @return:
        """
        total_gateway_fee = 0

        for payment_method in ('credit_card', 'boleto', 'pix', 'picpay', 'nupay', 'googlepay', 'applepay', 'openfinance_nubank'):
            total_gateway_fee += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('totalGatewayFee', 0)
            )

        return {
            "gateway": {
                "fee": total_gateway_fee,
                "percentage": float(settings.GATEWAY_FEE_PERCENTAGE)
            }
        }

    def _calculate_summary(self, payment_data_grouped_by_payment_method: dict, withdrawal_filters: dict):
        withdrawal_only_fields = {"amount", "status", "fee", "paidAt"}
        withdrawals = Withdrawal.objects.filter(**withdrawal_filters).only(*withdrawal_only_fields)

        antecipation_data = withdrawals.filter(status='approved', type='antecipation').aggregate(
            volume=Sum('amount'),
            revenue=Sum('fee'),
            profit=Sum('fee')
        )

        default_withdrawal_data = withdrawals.filter(status='approved', type='default').aggregate(
            volume=Sum('amount'),
            revenue=Sum('fee'),
            profit=Sum('fee')
        )

        chargeback_volume = 0
        chargeback_revenue = 0
        paid_fine_profit = 0
        for payment_method in ('credit_card', 'boleto', 'pix', 'picpay', 'nupay', 'googlepay', 'applepay', 'openfinance_nubank'):
            chargeback_volume += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('chargebackAmount', 0)
            )
            chargeback_revenue += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('chargebackFine', 0)
            )
            paid_fine_profit += (
                payment_data_grouped_by_payment_method.get(payment_method, {}).get('paidFine', 0)
            )

        # Calculates profit from each type of withdrawal
        summary = [
            {
                'label': 'Antecipação',
                'volume': antecipation_data['volume'] or 0,
                'revenue': antecipation_data['revenue'] or 0,
                'fee': 0,
                'profit': antecipation_data['profit'] or 0
            },
            {
                'label': 'Saque',
                'volume': default_withdrawal_data['volume'] or 0,
                'revenue': default_withdrawal_data['revenue'] or 0,
                'fee': 0,
                'profit': default_withdrawal_data['profit'] or 0
            },
            {
                'label': 'Multa',
                'volume': chargeback_volume or 0,
                'revenue': chargeback_revenue or 0,
                'fee': 0,
                'profit': paid_fine_profit or 0
            },
        ]

        return summary

    def get_dashboard_data(
        self,
        start_date: date = None,
        end_date: date = None,
        user_id: str = None,
        acquirer_id: str = None
    ) -> dict:
        """
        Get the dashboard data for the given date range.
        @param acquirer_id:
        @param user_id:
        @param start_date:
        @param end_date:
        @return:
        """
        raise NotImplementedError("This method is not completed yet.")

        payment_filters: dict = self._get_payment_filters(start_date, end_date, user_id, acquirer_id)
        payment_filter: str = get_payment_filter_where_start_end_user_acquirer(payment_filters)

        payment_data_grouped_by_payment_method: dict = (
            self._calculate_payment_details_grouped_by_payment_method(payment_filter=payment_filter)
        )

        revenue_data = self._calculate_revenue_data(
            payment_data_grouped_by_payment_method=payment_data_grouped_by_payment_method
        )
        payment_details = self._calculate_payment_details(
            payment_data_grouped_by_payment_method=payment_data_grouped_by_payment_method,
            filter=payment_filter
        )
        chargeback_data = self._calculate_chargeback_data(
            payment_data_grouped_by_payment_method=payment_data_grouped_by_payment_method
        )
        refund_data = self._calculate_refunded_data(
            payment_data_grouped_by_payment_method=payment_data_grouped_by_payment_method
        )
        gateway_fee_data = self._calculate_gateway_fee_data(
            payment_data_grouped_by_payment_method=payment_data_grouped_by_payment_method
        )
        summary = self._calculate_summary(
            payment_data_grouped_by_payment_method=payment_data_grouped_by_payment_method,
            withdrawal_filters=get_withdrawal_filters(start_date, end_date)
        )

        total = self._calculate_total(payment_details)

        return {
            **revenue_data,
            **payment_details,
            **chargeback_data,
            **refund_data,
            **gateway_fee_data,
            'summary': summary,
            'total': total
        }



client: DashboardClient = DashboardClient(dataset_name="payment_dataset", table_name="payment")
client.setup()
