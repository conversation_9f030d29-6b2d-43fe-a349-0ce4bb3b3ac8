from bigquery.base_client import AbstractPaymentBaseClient
from gateway.models import <PERSON><PERSON><PERSON><PERSON>


def generate_sql_query(table_name: str, acquirer_id: str or list[str] = None, start_date: str = None, end_date: str = None) -> str:
    """
    Generate an SQL query with the given table name and optional date range.

    :param table_name: The name of the table to query.
    :param acquirer_id: The id of the acquirer.
    :param start_date: The start date for the date range filter (optional).
    :param end_date: The end date for the date range filter (optional).
    :return: The SQL query as a string.
    """
    date_filter = ""
    if start_date and end_date:
        date_filter = f"AND created_at >= '{start_date}' AND created_at <= '{end_date}'"

    acquirer_filter = ""
    if isinstance(acquirer_id, str):
        acquirer_filter = f"WHERE acquirer_id = '{acquirer_id}'"
    elif isinstance(acquirer_id, list):
        acquirer_filter = f"WHERE acquirer_id IN ({', '.join([f'\'{id}\'' for id in acquirer_id])})"

    sql_query = f"""
    SELECT acquirer_id,
           COUNT(id) AS totalTransactionsCount,
           COUNT(CASE WHEN payment_method = 'pix' THEN 1 ELSE NULL END) AS pixTransactionsCount,
           COUNT(CASE WHEN payment_method = 'credit_card' THEN 1 ELSE NULL END) AS creditCardTransactionsCount,
           COUNT(CASE WHEN payment_method = 'boleto' THEN 1 ELSE NULL END) AS boletoTransactionsCount,
           ((100.0 * COUNT(CASE WHEN payment_method = 'pix' THEN 1 ELSE NULL END)) / NULLIF(COUNT(id), 0)) AS pixPercentage,
           ((100.0 * COUNT(CASE WHEN payment_method = 'credit_card' THEN 1 ELSE NULL END)) / NULLIF(COUNT(id), 0)) AS creditCardPercentage,
           ((100.0 * COUNT(CASE WHEN payment_method = 'boleto' THEN 1 ELSE NULL END)) / NULLIF(COUNT(id), 0)) AS boletoPercentage,
           COUNT(CASE WHEN (payment_method = 'credit_card' AND status = 'chargedback') THEN 1 ELSE NULL END) AS chargedBackCount,
           ((100.0 * COUNT(CASE WHEN (payment_method = 'credit_card' AND status = 'chargedback') THEN 1 ELSE NULL END)) / NULLIF(COUNT(CASE WHEN payment_method = 'credit_card' THEN 1 ELSE NULL END), 0)) AS chargeBackPercentage,
           COUNT(CASE WHEN (payment_method = 'pix' AND status = 'chargedback') THEN 1 ELSE NULL END) AS medCount,
           ((100.0 * COUNT(CASE WHEN (payment_method = 'pix' AND status = 'chargedback') THEN 1 ELSE NULL END)) / NULLIF(COUNT(CASE WHEN payment_method = 'pix' THEN 1 ELSE NULL END), 0)) AS medPercentage,
           ((100.0 * COUNT(CASE WHEN status = 'paid' THEN 1 ELSE NULL END)) / NULLIF(COUNT(id), 0)) AS approvalPercentage,
           SUM(CASE WHEN status = 'paid' THEN amount ELSE NULL END) AS totalAmount
    FROM `{table_name}`
    {acquirer_filter}
      {date_filter}
    GROUP BY acquirer_id;
    """
    return sql_query


class AcquirerMetricsClient(AbstractPaymentBaseClient):
    def get_acquirer_metrics(self, acquirer_id: str, start_date: str = None, end_date: str = None) -> dict:
        """
        Get the metrics for the given acquirer id and date range.
        @param acquirer_id:
        @param start_date:
        @param end_date:
        @return:
        """
        qualified_table_name = f"{self.client.project}.{self.dataset.dataset_id}.payment"
        query = generate_sql_query(qualified_table_name, acquirer_id, start_date, end_date)
        result = self.perform_query(query)

        return self.query_to_dict(result)

    def get_metrics(self, start_date: str = None, end_date: str = None) -> dict:
        """
        Get the metrics for all acquirers and date range.
        @param start_date:
        @param end_date:
        @return:
        """
        qualified_table_name = f"{self.client.project}.{self.dataset.dataset_id}.payment"
        query = generate_sql_query(qualified_table_name, start_date, end_date)
        result = self.perform_query(query)

        return self.query_to_dict(result)

    def get_all_metrics_at_once(self, start_date: str = None, end_date: str = None) -> list[dict]:
        """
        Get the metrics for all acquirers at once and date range.
        @param start_date:
        @param end_date:
        @return:
        """
        qualified_table_name = f"{self.client.project}.{self.dataset.dataset_id}.payment"
        acquirer_ids = list(Acquirer.objects.values_list('id', flat=True))
        query = generate_sql_query(
            table_name=qualified_table_name,
            acquirer_id=acquirer_ids,
            start_date=start_date,
            end_date=end_date
        )
        result = self.perform_query(query)

        return self.get_result_rows(result)



client: AcquirerMetricsClient = AcquirerMetricsClient(dataset_name="payment_dataset", table_name="payment")
client.setup()
