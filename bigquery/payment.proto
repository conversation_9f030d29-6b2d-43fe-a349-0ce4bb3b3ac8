/*
Protobuf now exists in two flavours: proto2 and proto3, the newest.
proto2 works well with BigQuery, whereas there are some issues with
proto3 which is fairly recent. Moreover, all examples provided by GCP
currently use proto2. So for now I recommend to stick to this version.
*/
syntax = "proto2";


message Payment {
  /*
  In proto2, it is now recommended by Google to declare all your
  fields as optional, even if they are REQUIRED in the Bigquery schema.
  */
  optional string id = 1;
  optional string acquirer_id = 2;
  optional string acquirer_id = 2;
  optional float amount = 3;
  optional string status = 4;
  optional int64 created_at = 5;
  optional int64 chargedback_at = 6;
  optional string payment_method = 7;
  optional int32 installments = 8;
  optional float profit = 9;
  optional float interest = 10;
  optional float cost = 11;
  optional float fee = 12;
  optional float fine = 12;
  optional float fee = 12;
}