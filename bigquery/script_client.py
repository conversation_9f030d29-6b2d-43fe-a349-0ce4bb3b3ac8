from google.cloud import bigquery
from bigquery.base_client import AbstractPayment<PERSON>ase<PERSON>lient
from google.cloud.exceptions import NotFound
from bigquery.exceptions import BigQueryExportPaymentInsertRowError
from bigquery.payment_schema import PAYMENT_SCHEMA_ITEMS


class PaymentClient(AbstractPaymentBaseClient):
    def get_or_create_dataset(self, dataset_name: str) -> bigquery.Dataset:
        dataset_ref = bigquery.DatasetReference(
            project=self.client.project,
            dataset_id=f"{self.client.project}_{dataset_name}".replace("-", "_")
        )
        try:
            dataset = self.client.get_dataset(dataset_ref)
        except NotFound:
            dataset = bigquery.Dataset(dataset_ref)
            dataset.location = "US"
            dataset = self.client.create_dataset(dataset)

        return dataset

    def get_or_create_table(self, table_name: str, schema: list) -> bigquery.Table:
        table_id = f"{self.client.project}.{self.dataset.dataset_id}.{table_name}"
        table = bigquery.Table(table_id, schema=schema)
        try:
            bq_table = self.client.get_table(table)
        except NotFound:
            bq_table = self.client.create_table(table)
        return bq_table

    def create_schema(self) -> list[bigquery.SchemaField]:
        return [bigquery.SchemaField(name, field_type, mode=mode) for name, field_type, mode in PAYMENT_SCHEMA_ITEMS]

    def perform_query(self, query: str) -> bigquery.table.RowIterator:
        return self.client.query_and_wait(query)

    def insert_rows(self, rows: list[dict]):
        errors = self.client.insert_rows(self.table, rows)
        if errors:
            raise BigQueryExportPaymentInsertRowError(errors)

    def setup(self):
        self.get_or_create_dataset(dataset_name="payment_dataset")
        self.get_or_create_table("payment", self.create_schema())
