from google.cloud import bigquery_storage_v1
from google.protobuf import descriptor_pb2
from google.protobuf import message_factory
import google.auth


def write_to_bigquery(project_id, dataset_id, table_id, rows_to_insert):
    # Authentication and client setup
    credentials, project = google.auth.default()
    client = bigquery_storage_v1.BigQueryWriteClient(credentials=credentials)

    # Define parent resource
    parent = f"projects/{project_id}/datasets/{dataset_id}/tables/{table_id}"

    # Create a write stream
    write_stream = client.create_write_stream(parent=parent)
    print(f"Created write stream: {write_stream.name}")

    # Define the table schema in Protobuf
    # This is a simplified example. You'll need to map this to your actual schema.
    table_schema = descriptor_pb2.DescriptorProto()
    table_schema.name = "YourTableSchema"

    for field_name in rows_to_insert[0].keys():
        field = table_schema.field.add()
        field.name = field_name
        field.number = len(table_schema.field)
        field.type = descriptor_pb2.FieldDescriptorProto.TYPE_STRING  # Adjust type as necessary

    # Create a dynamic message class based on the schema
    factory = message_factory.MessageFactory()
    message_type = factory.CreatePrototype(table_schema)

    # Prepare rows
    proto_rows = []
    for row in rows_to_insert:
        message = message_type()
        for field_name, field_value in row.items():
            setattr(message, field_name, field_value)
        proto_rows.append(message.SerializeToString())

    # Append rows to BigQuery using the Storage Write API
    append_request = bigquery_storage_v1.AppendRowsRequest(
        write_stream=write_stream.name,
        proto_rows=bigquery_storage_v1.AppendRowsRequest.ProtoData(
            rows=bigquery_storage_v1.ProtoRows(
                serialized_rows=proto_rows
            ),
            writer_schema=bigquery_storage_v1.ProtoSchema(proto_descriptor=table_schema),
        ),
    )
    response = client.append_rows(requests=[append_request])
    print(f"Rows written with response: {response}")

    # Finalize the stream to make rows available for DML
    finalize_response = client.finalize_write_stream(write_stream.name)
    print(f"Write stream finalized with response: {finalize_response}")


if __name__ == "__main__":
    project_id = "your-project-id"
    dataset_id = "your-dataset-id"
    table_id = "your-table-id"

    rows_to_insert = [
        {"column1": "value1", "column2": "value2"},
        {"column1": "value3", "column2": "value4"},
    ]

    write_to_bigquery(project_id, dataset_id, table_id, rows_to_insert)
