from .base import BaseAPI
from django.conf import settings
from .utils import ErrorEx<PERSON>, make_signature
from rest_framework import status
from django.core.cache import cache
import tempfile
import requests
import base64
import os

class Pagarme(BaseAPI):
    URL = 'https://api.pagar.me/core/v5/'
    secret_key = ''

    def getAccessToken(self):
        return base64.b64encode(f'{self.secret_key}:'.encode()).decode()

    def get_error_message(self, res):
        from payment.utils.pagarme_errors import PagarmeErrors

        data = res.json()

        if data.get('message'):
            errors = data.get('errors')
            if errors:
                return 0, PagarmeErrors.translate(list(data['errors'].values())[0][0])

            return 0, PagarmeErrors.translate(data.get('message'))

        charges = data.get('charges')
        if charges and isinstance(charges, list) and len(charges) > 0:
            last_transaction = charges[0].get('last_transaction')
            if last_transaction:
                acquirer_message = last_transaction.get('acquirer_message')
                acquirer_return_code = last_transaction.get('acquirer_return_code')
                if acquirer_message:
                    return acquirer_return_code, PagarmeErrors.translate(acquirer_return_code)

        return 0, res.text

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
          self.URL = acquirer.customUrl

        if acquirer.keys.get('secret_key'):
            self.secret_key = acquirer.keys.get('secret_key')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Basic {self.getAccessToken()}'
        })

    def balance(self):
        res = self.get('recipients')
        res = res.json()

        data = res.get('data')

        if data and isinstance(data, list) and len(data) > 0:
            idRecipient = data[0].get('id')

            res = self.get(f'recipients/{idRecipient}/balance')
            return res.json().get('available_amount')

    def createRecipient(self, **kwargs):
        res = self.post('recipients', json=kwargs)
        return res.json()

    def createTransaction(self, **kwargs):
        res = self.post('orders', type='payment', json=kwargs)
        return res.json()

    def refundTransaction(self, charge_id):
        res = self.delete(f'charges/{charge_id}', type='refund')
        return res.json()

    def getTransaction(self, id):
        res = self.get(f'orders/{id}', customUrl=self.URL)
        return res.json()

    def withdrawPix(self, **kwargs):
        res = self.get('recipients')
        res = res.json()

        data = res.get('data')

        if data and isinstance(data, list) and len(data) > 0:
            idRecipient = data[0].get('id')

            res = self.post(f'recipients/{idRecipient}/withdrawals', customUrl=self.URL, type='withdrawal', json=kwargs)
            print(res.json())
            return res.json()

    def get_transaction(self, charge_id: str) -> dict:
        """
        Get transaction details from Pagar.me.

        :param charge_id: The charge ID of the transaction.
        :return: A dictionary containing the transaction details.
        """
        res = self.get(f'charges/{charge_id}')
        return res.json()
