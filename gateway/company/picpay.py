from .base import BaseAPI
from django.core.cache import cache

class Pi<PERSON><PERSON><PERSON>(BaseAPI):
    URL = 'https://checkout-api-sandbox.picpay.com'
    client_id = ""
    client_secret = ""
    webhook_secret = ""

    def generateAcessToken(self):
        return self.api.post(self.URL + '/oauth2/token', data={
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'grant_type': 'client_credentials'
        }).json()

    def getAccessToken(self):
        if cache.get('picpay_access_token'):
            return cache.get('picpay_access_token')
        else:
            token = self.generateAcessToken()
            cache.set('picpay_access_token', token.get('access_token'), token.get('expires_in'))
            return token.get('access_token')

    def get_error_message(self, res):
        try:
            data = res.json()
            if "errors" in data and isinstance(data["errors"], list) and data["errors"]:
                data = data["errors"][0]
            return 0, data.get('message')
        except:
            raise Exception(res.text)

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
           self.URL = acquirer.customUrl

        self.client_secret = acquirer.keys.get("client_secret", "")
        self.client_id = acquirer.keys.get("client_id", "")
        self.webhook_secret = acquirer.keys.get("webhook_secret", "")

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.getAccessToken()}'
        })

    def createCreditCardTransaction(self, **kwargs):
        res = self.post('/api/v1/charge/authorization', json=kwargs)
        return res.json()
    
    def createPixTransaction(self, **kwargs):
        res = self.post('/api/v1/charge/pix', json=kwargs)
        return res.json()
    
    def createWalletTransaction(self, **kwargs):
        res = self.post('/api/v1/charge/wallet', json=kwargs)
        return res.json()

    def refund(self, id, **kwargs):
        res = self.post(f'/api/v1/charge/{id}/refund', json=kwargs)
        return res.json()
    