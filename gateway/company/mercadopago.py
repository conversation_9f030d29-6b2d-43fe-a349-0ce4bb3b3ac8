from .base import BaseAPI
from django.core.cache import cache

class MercadoPago(BaseAPI):
    URL = 'https://api.mercadopago.com'
    accessToken = ""

    def get_error_message(self, res):
        try:
            data = res.json()
            if "errors" in data and isinstance(data["errors"], list) and data["errors"]:
                data = data["errors"][0]
            return 0, data.get('message')
        except:
            raise Exception(res.text)

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
           self.URL = acquirer.customUrl

        self.accessToken = acquirer.keys.get("access_token", "")

        self.api.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {self.accessToken}"
        })

    def createTransaction(self, paymentId, deviceId, **kwargs):
        res = self.post("/v1/payments", type="payment", headers={"X-Idempotency-Key": str(paymentId), "X-meli-session-id": str(deviceId)}, json=kwargs)
        return res.json()

    def refund(self, paymentId, **kwargs):
        res = self.post(f"/v1/payments/{paymentId}/refunds",  type="refund", headers={"X-Idempotency-Key": str(paymentId)}, json=kwargs)
        return res.json()

    def createCardToken(self, **kwargs):
        res = self.post("/v1/card_tokens", type="payment", json=kwargs)
        return res.json()

    def getPaymentInfo(self, paymentId):
        res = self.get(f"/v1/payments/{paymentId}")
        return res.json()
