from gateway.company.utils import ErrorException
from payment.models import Payment
from payment.strategies.antifraud.base import Recomendation
from payment.utils.nethone_formatter import NethoneFormatter
from user.models import Integration

from .base import BaseAPI


class Nethone(BaseAPI):
    def get_error_message(self, res):
        try:
            data = res.json()
            if isinstance(data['errors'], list):
                data = data['errors'][0]
            return (
                data.get('code'),
                data.get('message') + '. Details: ' + (res.text if res.text else '')
            )
        except Exception:
            raise Exception(res.text)

    def __init__(self, integration: Integration) -> None:
        super().__init__()
        self.integration = integration
        self.URL = self.integration.keys['base_url']
        self._auth = (self.integration.keys['username'], self.integration.keys['password'])
        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

    def get_recomendation(self, payment_data: Payment) -> Recomendation:
        recomendation_data = NethoneFormatter.transform_to_recomendation_data(payment_data)

        response = self.post(
            '/v1/inquiries',
            type='payment',
            auth=self._auth,
            json=recomendation_data
        )

        response_json = response.json()
        return NethoneFormatter.transform_to_recomendation(response_json)

    def create_transaction(self, recomendation_id: str, payment_data: Payment) -> str:
        transaction_data = NethoneFormatter.transform_to_transaction_data(
            payment_data, recomendation_id
        )
        response = self.post(
            '/v1/transactions',
            type='payment',
            auth=self._auth,
            json=transaction_data
        )

        json = response.json()
        return json['id_str']

    def update_transaction(self, payment_data: Payment) -> None:
        antifraud_transaction_id = payment_data.antifraud_transaction_id
        if not antifraud_transaction_id:
            raise ErrorException(
                message='Nethone update_transaction called with antifraud_transaction_id empty',
                type='payment',
                code=400
            )

        transaction_data = NethoneFormatter.transform_to_update_transaction_data(
            payment_data
        )

        self.put(
            f'/v1/transactions/{antifraud_transaction_id}',
            type='payment',
            auth=self._auth,
            json=transaction_data
        )
