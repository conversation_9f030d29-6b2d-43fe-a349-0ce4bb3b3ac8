from gateway.company.utils import <PERSON><PERSON>rEx<PERSON>, OnboardException
from payment.utils.idwall_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>att<PERSON>

from .base import BaseAPI


def _log_idwall_request(
        user,
        request_type,
        request_url,
        request_json,
        response_json,
        response_status_code
    ):
    from user.models import IdWallRequestLog
    IdWallRequestLog.objects.create(
        user=user,
        cpf=user.cpf,
        request_type=request_type,
        request_url=request_url,
        request_json=request_json,
        response_json=response_json,
        response_status_code=response_status_code
    )

class IdWall(BaseAPI):

    def _get_flow_id(self, user_data) -> str:
        if user_data.docType == 'cnh' and user_data.docNumber is not None:
            return self._cnh_validation_flow_id

        return self._rg_validation_flow_id

    def _start_flow_manually(self, user_data, flow_id:str):
        user_ref = user_data.cpf
        final_url = f'maestro/profile/{user_ref}/flow/{flow_id}'
        response = self.post(
            final_url,
            type=self.api_type
        )

        _log_idwall_request(
            user= user_data,
            request_type = 'start_flow_manually',
            request_url = final_url,
            request_json = None,
            response_json = response.json(),
            response_status_code = response.status_code
        )

        self.check_error(response, type=self.api_type, force_validation=True)



    def __init__(self, integration) -> None:
        super().__init__()
        self.api_type = 'onboard'
        self.integration = integration
        self.URL = self.integration.keys['base_url']
        self._auth_token = self.integration.keys['auth_token']
        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': self._auth_token
        })
        self._webhook_secret_token = self.integration.keys['webhook_secret_token']
        self._rg_validation_flow_id = self.integration.keys['rg_validation_flow_id']
        self._cnh_validation_flow_id = self.integration.keys['cnh_validation_flow_id']

    def check_error(self, res, type='default', force_validation=False) -> None:  # noqa: A002
        if force_validation:
            super().check_error(res, type)

    def get_error_message(self, res):
        try:
            data = res.json()
            if isinstance(data, list):
                data = data[0]
            error = ';'.join(data.get('errors', []))
            if not error:
                error = data.get('message', '')

            return res.status_code, error

        except Exception:
            raise Exception(f"Status code: {res.status_code}; text: {res.text}")

    def new_profile(self, user_data) -> None:
        if not user_data.has_idwall_validation_docs():
            raise ErrorException(
                message='IdWall new_profile called with idwallSdkToken empty',
                code=None,
                type='onboard'
            )

        profile_data = IdWallFormatter.transform_to_new_profile_data(user_data)
        final_url = 'maestro/profile/sdk?runOCR=true'
        response = self.post(
            final_url,
            type=self.api_type,
            json=profile_data
        )

        _log_idwall_request(
            user= user_data,
            request_type = 'new_profile',
            request_url = final_url,
            request_json = profile_data,
            response_json = response.json(),
            response_status_code = response.status_code
        )

        self.check_error(response, type=self.api_type, force_validation=True)

    def update_profile(self, user_data) -> None:
        if not user_data.has_idwall_validation_docs():
            raise ErrorException(
                message='IdWall update_profile called with idwallSdkToken empty',
                code=None,
                type='onboard'
            )

        profile_data = IdWallFormatter.transform_to_update_profile_data(user_data)

        final_url = f'maestro/profile/{user_data.cpf}/sdk?runOCR=true'
        response = self.put(
            final_url,
            type=self.api_type,
            json=profile_data
        )

        _log_idwall_request(
            user= user_data,
            request_type = 'update_profile',
            request_url = final_url,
            request_json = profile_data,
            response_json = response.json(),
            response_status_code = response.status_code
        )

        self.check_error(response, type=self.api_type, force_validation=True)

    def get_validation_errors(self, user_data) -> list[str]:
        profile_ref: str = user_data.cpf
        final_url = f'maestro/profile/{profile_ref}/lastProfileFlow'
        response = self.get(
            final_url,
            type=self.api_type
        )
        response_json = response.json()

        _log_idwall_request(
            user= user_data,
            request_type = 'validation_errors',
            request_url = final_url,
            request_json = None,
            response_json = response_json,
            response_status_code = response.status_code
        )

        self.check_error(response, type=self.api_type, force_validation=True)

        return IdWallFormatter.extract_validation_errors(response_json)

    def start_flow_for_user(self, user_data) -> None:
        try:
            print(f'Starting RG flow for profile {user_data.cpf}')
            self._start_flow_manually(user_data, self._rg_validation_flow_id)
        except OnboardException as e:
            if e.code == 406 and e.message.startswith('Cannot initiate flow'):
                print(f'Failed to run RG flow. Starting CNH flow for profile {user_data.cpf}')
                self._start_flow_manually(user_data, self._cnh_validation_flow_id)
                return
            raise e

    @property
    def webhook_secret_token(self) -> str:
        return self._webhook_secret_token
