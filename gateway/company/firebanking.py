from .base import BaseAPI
from .utils import ErrorException
from rest_framework import status

class FireBanking(BaseAPI):
    URL = 'https://api.firebanking.io'
    apikey = ''

    def get_error_message(self, res):
        data = res.json()
        return None, data.get('message')

    def __init__(self, acquirer) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('apikey'):
            self.apikey = acquirer.keys.get('apikey')

        self.api.headers.update({
            'content-type': 'application/json',
            'accept': 'application/json',
            'apiKey': self.apikey
        })

    def createTransaction(self, **kwargs):
        res = self.post('/payment', type='payment', json=kwargs)
        return res.json()

    def refundTransaction(self, id, **kwargs):
        res = self.post(f'/payment/refund/{id}', json=kwargs)
        return res.json()

    def withdrawTransaction(self, **kwargs):
        res = self.post(f'/payment/withdraw', json=kwargs)
        return res.json()

    def setWebhook(self, url):
        res = self.post('/business/integration', json={
            'webhookUrl':url
        })
        return res.json()
