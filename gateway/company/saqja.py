from .base import BaseAPI
from django.conf import settings
from .utils import make_signature
from django.core.cache import cache
from rest_framework import status
import requests
import base64
import os

class Saqja(BaseAPI):
    URL = 'https://pix-service.saqja.com/api/'
    client = ''
    secret = ''

    def getBasicToken(self):
        return 'Basic ' + base64.b64encode((self.client + ':' + self.secret).encode()).decode()

    def __init__(self, acquirer) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('secret'):
            self.secret = acquirer.keys.get('secret')
        if acquirer.keys.get('client'):
            self.client = acquirer.keys.get('client')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': self.getBasicToken()
        })

    def createTransaction(self, **kwargs):
        res = self.post('/api/venda/', json=kwargs)
        if not status.is_success(res.status_code):
            raise Exception(res.text)
        return res.json()

    def createCreditCardTransaction(self, **kwargs):
        res = self.post('/api/cartao-credito/venda/', json=kwargs)
        if not status.is_success(res.status_code):
            raise Exception(res.text)
        return res.json()

    def getTransaction(self, id):
        return self.get('/charges/' + id).json()

    def refundTransaction(self, id, **kwargs):
        res = self.post(self.URL + '/api/transactions/' + id + '/refund', json=kwargs)
        if not res.status_code == 200:
            raise Exception(res.text)
        return res.json()
