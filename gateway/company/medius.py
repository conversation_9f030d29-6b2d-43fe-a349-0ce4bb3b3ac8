from .base import BaseAPI
from .utils import ErrorException
from rest_framework import status
import base64

class Medius(BaseAPI):
    URL = 'https://api.mediuspag.com'
    apikey = ''

    def get_error_message(self, res):
        data = res.json()
        return None, data.get('error')

    def error_condition(self, res):
        if super().error_condition(res):
            return True
        elif res.json().get('success') == False:
            return True

    def setAuthBasic(self):
        return base64.b64encode(f'{self.secret}:x'.encode()).decode()

    def __init__(self, acquirer) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('secret'):
            self.secret = acquirer.keys.get('secret')

        self.api.headers.update({
            'content-type': 'application/json',
            'accept': 'application/json',
            'Authorization': f'Basic {self.setAuthBasic()}'
        })

    def createTransaction(self, **kwargs):
        res = self.post('/functions/v1/transactions', type='payment', json=kwargs)
        return res.json()

    def refundTransaction(self, id, **kwargs):
        res = self.delete(f'/functions/v1/transactions/{id}', json=kwargs)
        return res.json()

    def withdrawTransaction(self, id, **kwargs):
        res = self.post(f'/payment/withdraw', json=kwargs)
        return res.json()

    def setWebhook(self, url):
        res = self.post('/business/integration', json={
            'webhookUrl':url
        })
        return res.json()
