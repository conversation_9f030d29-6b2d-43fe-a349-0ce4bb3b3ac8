from .base import BaseAPI
from django.conf import settings
from .utils import make_signature, ErrorException
from django.core.cache import cache
from rest_framework import status
import requests
import base64
import os

class Asaas(BaseAPI):
    URL = 'https://asaas.com/api/'
    apikey = ''

    def get_error_message(self, res):
        if res.status_code == status.HTTP_403_FORBIDDEN:
            return "403", res.text
        try:
            mainerror = res.json().get('errors')[0]
        except:
            raise Exception(res.text)
        return mainerror.get('code'), mainerror.get('description')

    def __init__(self, acquirer=None, apikey=None) -> None:
        super().__init__()
        if acquirer:
            if acquirer.customUrl:
                self.URL = acquirer.customUrl

            if acquirer.keys.get('apikey'):
                self.apikey = acquirer.keys.get('apikey')

        if apikey:
            self.apikey = apikey

        self.api.headers.update({
            'content-type': 'application/json',
            'accept': 'application/json',
            'access_token': self.apikey
        })

    def createTransaction(self, **kwargs):
        res = self.post('/v3/payments', json=kwargs)
        return res.json()

    def captureTransaction(self, id, card):
        res = self.post(f'/v3/payments/{id}/payWithCreditCard', type='payment', json={
            'creditCard': {
                'holderName': card.holderName,
                'number': card.encryptedNumber,
                'expiryMonth': card.expMonth,
                'expiryYear': card.expYear,
                'ccv': card.cvv
            },
            'creditCardHolderInfo':{
                'name': card.holderName,
                'email': card.customer.email,
                'cpfCnpj': card.customer.docNumber,
                'postalCode': '89223005',
                'addressNumber': '277',
                'phone': card.customer.phone
            }
        })
        return res.json()

    def getBoletoBarcode(self, id):
        res = self.get(f'/v3/payments/{id}/identificationField')
        return res.json()

    def getPixQrCode(self, id):
        res = self.get(f'/v3/payments/{id}/pixQrCode')
        return res.json()

    def createCustomer(self, **kwargs):
        res = self.post('/v3/customers', json=kwargs)
        return res.json()

    def getTransaction(self, id):
        return self.get('/charges/' + id).json()

    def getCharge(self, id):
        return self.get(f'v3/payments/{id}').json()

    def withdrawPix(self, **kwargs):
        res = self.post('/v3/transfers', type='withdrawal', json=kwargs)
        return res.json()

    def refundTransaction(self, id, **kwargs):
        res = self.post(f'/v3/payments/{id}/refund', json=kwargs)
        return res.json()

    def refundInstallment(self, id, **kwargs):
        res = self.post(f'v3/installments/{id}/refund', json=kwargs)
        return res.json()

    def balance(self):
        res = self.get('v3/finance/balance')
        return res.json().get('balance')

    def createSub(self, **kwargs):
        res = self.post('/v3/accounts', type='onboard', json=kwargs)
        return res.json()

    def getDocs(self):
        res = self.get('v3/myAccount/documents', type='onboard')
        return res.json()
