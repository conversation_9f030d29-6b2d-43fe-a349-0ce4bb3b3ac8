from .base import BaseAPI
from django.conf import settings
import requests
import base64
import os

class Payup(BaseAPI):
    URL = 'https://ws.pag360.app.br/hubPay/api/v1/'

    def __init__(self, acquirer) -> None:
        super().__init__()
        self.apiKey = acquirer.keys.get('secret_key')
        self.api.headers.update({
            'Auth-API-Pag360': 'Bearer ' + self.apiKey
        })

    def createTransaction(self, **kwargs):
        res = self.api.post(self.URL + 'doPay', json=kwargs)
        if not res.status_code == 200:
            raise Exception(res.text)
        return res.json()

    def getTransaction(self, id):
        return self.api.get(self.URL + 'gettransaction', json={
            'id': id
        }).json()

    def refundTransaction(self, **kwargs):
        res = self.api.post(self.URL + 'doCancel', json=kwargs)
        if not res.status_code == 200:
            raise Exception(res.text)
        return res.json()

    def getCancelations(self, date):
        res = self.get(f'cancellations?date={date}')
        return res.json()
