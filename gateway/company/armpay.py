from .base import BaseAPI
from django.conf import settings
from .utils import make_signature
from rest_framework import status
from django.core.cache import cache
import tempfile
import requests
import base64
import os

class Armpay(BaseAPI):
    URL = 'https://armpay.acessarminhaconta.com'
    client_id = ''
    client_secret = ''

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('client_secret'):
            self.client_secret = acquirer.keys.get('client_secret')
        if acquirer.keys.get('client_id'):
            self.client_id = acquirer.keys.get('client_id')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'id': self.client_id,
            'secret': self.client_secret
        })

    def createTransaction(self, **kwargs):
        res = self.post('/v1/pixIn/dinamico', json=kwargs)
        return res.json()

    def withdrawPix(self, id, **kwargs):
        res = self.put(f'/v1/pixOut/transferir', type='withdrawal', json=kwargs)
        return res.json()

    def balance(self):
        return self.get('/v1/conta/saldo').json().get('Saldo')
