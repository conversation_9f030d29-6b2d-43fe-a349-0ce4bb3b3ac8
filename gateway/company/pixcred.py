from .base import BaseAPI
from django.conf import settings
from .utils import make_signature
from rest_framework import status
import requests
import base64
import os

class Pixcred(BaseAPI):
    URL = 'https://sandbox-wallet-gateway.pixcred.app'
****************************************************************************************************************************************************************************************************************************************************************************************
    api_token = '280e68bd-1e44-4fcb-9e00-e125cd62d340'

    def setAuthBasic(self):
        return base64.b64encode(f':{self.api_token}'.encode()).decode()

    def setAuth(self, body):
        signature, timestamp = make_signature(self.api_token, self.private_key, body)

        self.api.headers.update({
            'Request-Signature': signature,
            'Request-Timestamp': str(timestamp)
        })

    def __init__(self, acquirer) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('private_key'):
            self.private_key = acquirer.keys.get('private_key')
        if acquirer.keys.get('api_token'):
            self.api_token = acquirer.keys.get('api_token')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Basic {self.setAuthBasic()}'
        })

    def createTransaction(self, **kwargs):
        res = self.api.post(self.URL + '/api/cob_requests/create', json=kwargs)
        if not res.status_code == 201:
            raise Exception(res.text)
        return res.json()

    def getTransaction(self, id):
        return self.api.get(self.URL + '/api/transactions/' + id).json()

    def getByE2E(self, id):
        return self.api.get(self.URL + f'/api/pay_requests/get/e2e/{id}' + id).json()

    def refundTransaction(self, id, **kwargs):
        res = self.api.post(self.URL + '/api/transactions/' + id + '/refund', json=kwargs)
        if not res.status_code == 200:
            raise Exception(res.text)
        return res.json()

    def balance(self):
        self.setAuth({})
        res = self.api.get('https://secure.pixcred.app/api/v1/gateway/balance')
        if not res.status_code == 200:
            raise Exception(res.text)
        return res.json().get('data').get('balance')

    def withdrawPix(self, **kwargs):
        print(kwargs)
        self.setAuth(kwargs)
        res = self.api.post('https://secure.pixcred.app/api/v1/cash_out', json=kwargs)
        if not status.is_success(res.status_code):
            raise Exception(res.text)
        return res.json()
