from django.core.cache import cache
from .base import BaseAPI
from ..models import Acquirer
import base64
import datetime


class WemaxPay(BaseAPI):
    URL = "https://api.wemaxpay.com.br/"
    token = ""

    def authenticate(self):
        bearer = cache.get('wemaxpay_token')
        if bearer:
            return bearer

        res = self.post('/account/login', json={
            'email': self.email,
            'password': self.password,
        }).json()
        bearer = res.get('token')
        cache.set('wemaxpay_token', bearer, timeout=3600)
        self.api.headers.update({
            'Authorization': f'Bearer {bearer}',
        })

    def __init__(self, acquirer: Acquirer):
        super().__init__()

        self.URL = acquirer.customUrl if acquirer.customUrl else self.URL

        self.token = acquirer.keys.get('token')
        self.email = acquirer.keys.get('email')
        self.password = acquirer.keys.get('password')

        self.authenticate()

    def validateCard(self, **kwargs) -> dict:
        return self.post(f'/credit-card/validate?api_token={self.token}', json=kwargs).json()

    def createCreditCard(self, **kwargs) -> dict:
        return self.post(f'/credit-card?api_token={self.token}', json=kwargs).json()
