from .base import BaseAPI
from django.conf import settings
from .utils import ErrorEx<PERSON>, make_signature
from rest_framework import status
from django.core.cache import cache
import tempfile
import requests
import base64
import os

class NextPayments(BaseAPI):
    URL = 'https://apidev.nextpayments.com.br'
    client_id = ''
    client_secret = ''

    def generateAcessToken(self):
        return requests.post('https://oauth2.nextpayments.com.br/oauth2/token',
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            data={
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'client_credentials'
            }).json()

    def getAccessToken(self):
        if cache.get('nextpayments_access_token'):
            return cache.get('nextpayments_access_token')
        else:
            token = self.generateAcessToken()
            cache.set('nextpayments_access_token', token.get('access_token'), token.get('expires_in'))
            return token.get('access_token')

    def get_error_message(self, res):
        data = res.json()
        if data.get('message'):
            return 0, data.get('message')

        return 0, res.text

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        self.client_id = acquirer.keys.get('client_id', '')
        self.client_secret = acquirer.keys.get('client_secret', '')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.getAccessToken()}'
        })

    def balance(self):
        res = self.get(f'/virtual-account/get-balance')
        return res.json().get('balance')

    def createCreditCardTransaction(self, **kwargs):
        resp = self.post('/payments/tokenization', type='payment', json={
            "cardNumber": kwargs['cardNumber']
        })
        resp = resp.json()
        kwargs['tokenId'] = resp.get('tokenId')

        resp = self.post('/payments/authorize', type='payment', json=kwargs)
        return resp.json()

    def createPixTransaction(self, **kwargs):
        res = self.post('/pix/generate', type='payment', json=kwargs)
        return res.json()

    def refundTransaction(self, id):
        res = self.put(f'/payments/cancel/{id}', type='refund')
        return res.json()

    def withdrawPix(self, **kwargs):
        res = self.put('/pix/out', type='withdrawal', json=kwargs)
        return res.json()

    def setWebhook(self, url, type='PIX'):
        headers = self.api.headers
        headers.pop('Content-Type', None)
        try:
            resp = self.get('/webhook', headers=headers)
            data = resp.json()
        except:
            data = {}
        webhooks = data.get('webhooks', [])

        existsWebhook = next((webhook for webhook in webhooks if webhook['type'] == type and webhook['url'] == url), None)

        if not existsWebhook:
            resp = self.post('/webhook', json={
                'url': url,
                'httpMethod': 'POST',
                'type': type
            })

        return True
