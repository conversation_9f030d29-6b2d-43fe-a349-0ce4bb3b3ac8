from gateway.company.base import BaseAPI
from gateway.models import Acquirer


class OpenPix(BaseAPI):
    URL = "https://api.openpix.com.br"

    def __init__(self, acquirer: Acquirer):
        super().__init__()
        self.URL = acquirer.customUrl if acquirer.customUrl else self.URL
        self.token = acquirer.keys.get("apikey", None)
        self.api.headers.update(
            {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Authorization": f"{self.token}",
            }
        )

    def create_charge(self, **kwargs) -> dict:
        res = self.post("/api/v1/charge?return_existing=false", json=kwargs)
        return res.json()

    def get_transaction(self, transaction_id: str) -> dict:
        res = self.get(f"/api/v1/transaction/{transaction_id}")
        return res.json()

    def create_refund(self, **kwargs) -> dict:
        correlation_id = kwargs.get("correlationID")
        res = self.post(f"/api/v1/charge/{correlation_id}/refund", json=kwargs)
        return res.json()

    def get_refund(self, refund_id: str) -> dict:
        res = self.get(f"/api/v1/refund/{refund_id}")
        return res.json()

    def set_webhook(self, **kwargs):
        self.post("/api/v1/webhook", json=kwargs)
