from .base import BaseAPI
from django.conf import settings
from .utils import make_signature
from rest_framework import status
import requests
import base64
import os

class Arkama(BaseAPI):
    URL = 'https://api.arkama.com.br/'
    token = ''

    def __init__(self, acquirer) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('token'):
            self.token = acquirer.keys.get('token')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        })

    def createTransaction(self, **kwargs):
        res = self.post(f'v1/orders?token={self.token}', json=kwargs)
        return res.json()

    def getTransaction(self, id):
        return self.get(self.URL + '/api/transactions/' + id).json()

    def balance(self):
        self.setAuth({})
        res = self.get('v2/balance')
        return res.json().get('data').get('balance')

    def withdrawPix(self, **kwargs):
        self.setAuth(kwargs)
        res = self.get('v2/withdraw/pix', type='withdrawal', json=kwargs)
        return res.json()

    def refund(self, id):
        res = self.post(f'v1/orders/{id}/refund?token={self.token}')
        return res.json()
