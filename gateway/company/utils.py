import base64
import time
import uuid
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives.asymmetric import utils
from cryptography.hazmat.primitives.serialization import load_pem_private_key

def flatten_dict(obj, prefix=''):
    result = []
    for key, value in obj.items():
        if isinstance(value, dict):
            result.extend(flatten_dict(value, f"{prefix}{key}."))
        else:
            result.append(f"{prefix}{key}={value}")
    return result

def generate_message(api_token, timestamp, body):
    flattened_body = flatten_dict(body)
    flattened_body.sort()
    body_string = ''.join(flattened_body)

    body_base64 = base64.b64encode(body_string.encode()).decode()
    return f"{api_token}:{timestamp}:{body_base64}"

def sign_message(private_key, message):
    digest = hashes.Hash(hashes.SHA256(), backend=default_backend())
    digest.update(message.encode())
    digest_value = digest.finalize()

    signature = private_key.sign(digest_value, ec.ECDSA(utils.Prehashed(hashes.SHA256())))
    return base64.b64encode(signature).decode()

def make_signature(api_token, pv_key, body):
    # Date and time in UNIX format (Timestamp)
    timestamp = int(time.time() * 1000)

    # Private key in PEM format
    private_key_pem = pv_key

    # Load private key
    private_key = load_pem_private_key(private_key_pem.encode(), password=None, backend=default_backend())

    # Generate and sign the message
    message = generate_message(api_token, timestamp, body)
    signature_base64 = sign_message(private_key, message)

    return signature_base64, timestamp


class ErrorException(Exception):
    def __init__(self, message, code=None, type='default'):
        if type == 'default':
            print(message)
        elif type == 'payment':
            raise PaymentException(message, code, type)
        elif type == 'withdrawal':
            raise WithdrawalException(message, code, type)
        elif type == 'onboard':
            raise OnboardException(message, code, type)
        elif type == 'refund':
            raise RefundException(message, code, type)
        elif type == 'nfe':
            raise NfeError(message, code, type)
        elif type == 'entity_information':
            raise EntityInformationError(message, code, type)


        self.message = message
        self.code = code
        self.type = type

    def __str__(self):
        return f'{self.code} - {self.message}'

class PaymentException(Exception):
    def __init__(self, message, code, type):
        self.message = message
        self.code = code
        self.type = type

class WithdrawalException(Exception):
    def __init__(self, message, code, type):
        self.message = message
        self.code = code
        self.type = type

class OnboardException(Exception):
    def __init__(self, message, code, type):
        self.message = message
        self.code = code
        self.type = type

class RefundException(Exception):
    def __init__(self, message, code, type):
        self.message = message
        self.code = code
        self.type = type

class NfeError(Exception):
    def __init__(self, message, code, type_):
        self.message = message
        self.code = code
        self.type = type_

class AntifraudPaymentError(Exception):
    def __init__(self, message, payment_id):
        self.message = message
        self.payment_id = payment_id
        self.error_uuid = uuid.uuid4()

    def __str__(self):
        return f'uuid: {self.error_uuid}; payment_id: {self.payment_id}; message: {self.message}'


class EntityInformationError(Exception):
    def __init__(self, message, code, type_):
        self.message = message
        self.code = code
        self.type = type_
