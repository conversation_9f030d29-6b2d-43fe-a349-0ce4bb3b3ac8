from .base import BaseAPI
from .utils import make_signature
from django.core.cache import cache
import tempfile
import base64

class Efi(BaseAPI):
    URL = 'https://cobrancas-h.api.efipay.com.br'
    URL_PIX = 'https://pix.api.efipay.com.br'
    client_id = ''
    client_secret = ''

    def setAuthBasic(self):
        return base64.b64encode(f'{self.client_id}:{self.client_secret}'.encode()).decode()

    def setAuth(self, body):
        signature, timestamp = make_signature(self.client_id, self.client_secret, body)

        self.api.headers.update({
            'Request-Signature': signature,
            'Request-Timestamp': str(timestamp)
        })

    def makeCert(self):
        # Create a temporary file to store the certificate
        cert_file = tempfile.NamedTemporaryFile(delete=False)
        cert_file.write(self.certificate.encode('utf-8'))
        cert_file.close()
        return cert_file

    def get_error_message(self, res):
        try:
            data = res.json()
            if data.get('code'):
                return data.get('code'), data.get('error')
            return data.get('nome'), data.get('mensagem')
        except:
            raise Exception(res.text)

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('client_secret'):
            self.client_secret = acquirer.keys.get('client_secret')
        if acquirer.keys.get('client_id'):
            self.client_id = acquirer.keys.get('client_id')
        if acquirer.keys.get('certificate'):
            self.certificate = acquirer.keys.get('certificate')
            self.cert_file = self.makeCert()
        if acquirer.keys.get('pixKey'):
            self.pixKey = acquirer.keys.get('pixKey')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Basic {self.setAuthBasic()}'
        })

        if type == 'pix':
            self.URL = self.URL_PIX
            self.api.cert = self.cert_file.name
            self.getPixAccessToken()
        else:
            self.getAccessToken()

    def getPixAccessToken(self):
        token = cache.get('efi_token_pix')
        if not token:
            # Get the access token
            res = self.post('oauth/token',
                customUrl=self.URL_PIX,
                json={'grant_type': 'client_credentials'},
                cert=self.cert_file.name
            )

            # Update the headers with the access token
            data = res.json()
            token = data.get('access_token')
            cache.set('efi_token_pix', token, data.get('expires_in'))
        self.api.headers.update({
            'Authorization': f'Bearer {token}'
        })

    def getAccessToken(self):
        token = cache.get('efi_token')
        if not token:
            res = self.post('/v1/authorize', json={'grant_type': 'client_credentials'})
            data = res.json()
            token = data.get('access_token')
            cache.set('efi_token', token, data.get('expires_in') - 1)

        self.api.headers.update({
            'Authorization': f'Bearer {token}'
        })


    def chargeOneStep(self, **kwargs):
        res = self.post('/v1/charge/one-step', type='payment', json=kwargs)
        return res.json()

    def createTransaction(self, **kwargs):
        res = self.post('/v2/cob/', customUrl=self.URL_PIX, json=kwargs)
        return res.json()

    def getNotification(self, id):
        res = self.get('/v1/notification/' + id)
        return res.json()

    def getTransaction(self, id):
        return self.get(self.URL + '/api/transactions/' + id).json()

    def refundTransaction(self, id, **kwargs):
        res = self.post(f'/v1/charge/card/{id}/refund', json=kwargs)
        return res.json()

    def withdrawPix(self, id, **kwargs):
        res = self.put(f'v2/gn/pix/{id}/', type='withdrawal', customUrl=self.URL_PIX, json=kwargs)
        return res.json()

    def getMeds(self, startDate, endDate):
        inicio = startDate.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
        fim = endDate.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
        return self.get(f'v2/gn/infracoes?inicio={inicio}&fim={fim}').json()

    def getPix(self, id):
        res = self.get(f'/v2/cob/{id}', customUrl=self.URL_PIX)
        return res.json()

    def balance(self):
        return self.get('v2/gn/saldo/').json().get('saldo')

    def refundPix(self, e2eId, id, **kwargs):
        res = self.put(f'v2/pix/{e2eId}/devolucao/{id}', customUrl=self.URL_PIX, json=kwargs)
        return res.json()

    def setWebhook(self, url, **kwargs):
        res = self.put(f'v2/webhook/{self.pixKey}', customUrl=self.URL_PIX, headers={
            'x-skip-mtls-checking':'true'
        },
        json={
          'webhookUrl':url + '?ignorar='
        })
        return res.json()
