from .base import BaseAPI
from django.conf import settings
from .utils import ErrorException, make_signature
from rest_framework import status
from django.core.cache import cache
import tempfile
import requests
import base64
import os

class Sumup(BaseAPI):
    URL = 'https://api.sumup.com/v0.1/'
    secret_key = ''

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        # if acquirer.customUrl:
        #   self.URL = acquirer.customUrl

        self.acquirer = acquirer

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.acquirer.keys.get('apikey')}'
        })

    def getCheckout(self, id):
        res = self.get(f'checkouts/{id}')
        return res.json()

    def createCheckout(self, **kwargs):
        res = self.post('checkouts', json=kwargs)
        return res.json()

    def getMe(self):
        return self.get('me').json()

    def payWithCreditCard(self, id,  **kwargs):
        res = self.put(f'checkouts/{id}', json=kwargs)
        return res.json()

    def refund(self, charge_id):
        res = self.delete(f'charges/{charge_id}', type='refund')
        return res.json()
