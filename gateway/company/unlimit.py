from .base import BaseAPI
from django.core.cache import cache

class Unlimit(BaseAPI):
    URL = 'https://sandbox.cardpay.com'
    terminal_code = ""
    password = ""

    def generateAcessToken(self):
        return self.api.post(self.URL + '/api/auth/token', data={
            'terminal_code': self.terminal_code,
            'password': self.password,
            'grant_type': 'password'
        }).json()

    def getAccessToken(self):
        if cache.get('unlimit_access_token'):
            return cache.get('unlimit_access_token')
        else:
            token = self.generateAcessToken()
            cache.set('unlimit_access_token', token.get('access_token'), token.get('expires_in'))
            return token.get('access_token')

    def get_error_message(self, res):
        try:
            data = res.json()
            if "errors" in data and isinstance(data["errors"], list) and data["errors"]:
                data = data["errors"][0]
            return 0, data.get('message')
        except:
            raise Exception(res.text)

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
           self.URL = acquirer.customUrl

        self.terminal_code = acquirer.keys.get("terminal_code", "")
        self.password = acquirer.keys.get("password", "")

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.getAccessToken()}'
        })

    def createTransaction(self, **kwargs):
        res = self.post('/api/payments', type='payment', json=kwargs)
        return res.json()

    def withdrawPix(self, **kwargs):
        res = self.get('/api/payouts', type='withdrawal', json=kwargs)
        return res.json()
    
    def refund(self, **kwargs):
        res = self.post('/api/refunds', type='refund', json=kwargs)
        return res.json()
    