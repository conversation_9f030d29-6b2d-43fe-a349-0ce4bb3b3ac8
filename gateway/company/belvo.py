import uuid
from gateway.company.efi import Efi
from gateway.models import Acquirer
from .base import BaseAPI
import base64

class Belvo(BaseAPI):
    URL = 'https://api.belvo.com'
    auth_username = ''
    auth_password = ''
    holder_type = ''
    holder_identifier_type = ''
    holder_identifier = ''
    holder_first_name = ''
    holder_last_name = ''
    bank_account_type = ''
    bank_account_agency = ''
    bank_account_number = ''
    bank_account_institution = ''
    calllback_url = ''

    @staticmethod
    def extract_first_message(obj):
        message = "Erro desconhecido"
        required_found = False

        def recursive(obj):
            nonlocal message, required_found
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key == 'message' and isinstance(value, str):
                        message = value
                    if key == 'open_finance' and isinstance(value, dict):
                        if any('required' in str(v).lower() for v in value.values()):
                            required_found = True
                    recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    recursive(item)

        recursive(obj)
        if required_found:
            message = "Campo obrigatórios ausentes na requisição"
        return message
    
    def get_error_message(self, res):
        try:
            data = res.json()
            message = self.extract_first_message(data)
            return 0, message
        except:
            raise Exception(res.text)

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
           self.URL = acquirer.customUrl
        
        self.auth_username = acquirer.keys.get('auth_username', '')
        self.auth_password = acquirer.keys.get('auth_password', '')
        self.holder_type = acquirer.keys.get('holder_type', '')
        self.holder_identifier_type = acquirer.keys.get('holder_identifier_type', '')
        self.holder_identifier = acquirer.keys.get('holder_identifier', '')
        self.holder_first_name = acquirer.keys.get('holder_first_name', '')
        self.holder_last_name = acquirer.keys.get('holder_last_name', '')
        self.bank_account_type = acquirer.keys.get('bank_account_type', '')
        self.bank_account_agency = acquirer.keys.get('bank_account_agency', '')
        self.bank_account_number = acquirer.keys.get('bank_account_number', '')
        self.bank_account_institution = acquirer.keys.get('bank_account_institution', '')
        self.callback_url = acquirer.keys.get('callback_url', '')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Basic {base64.b64encode(f"{self.auth_username}:{self.auth_password}".encode("utf-8")).decode("utf-8")}'
        })

    def createTransaction(self, **kwargs):
        res = self.post('/payments/br/payment-intents/', type='payment', json=kwargs)
        return res.json()
    
    def refundByEfi(self, e2eId, amount):
        acquirer_efi = Acquirer.objects.get(acquirer_gateway="efi")
        efi = Efi(acquirer_efi)
        res = efi.refundPix(e2eId, str(uuid.uuid4()).replace('-', ''), valor=str(format(amount, '.2f')))
        return res
