from .base import BaseAPI
from django.conf import settings
import requests
import base64
import os

class Owempay(BaseAPI):
    URL = 'https://www.compraonlineprotegida.com/services'

    def __init__(self, acquirer) -> None:
        super().__init__()
        self.apiKey = acquirer.keys.get('apiKey')
        self.clientId = acquirer.keys.get('clientId')

    def createTransaction(self, **kwargs):
        data = {
            'apiKey': self.apiKey,
            'clientId': self.clientId,
            **kwargs
        }
        print(data)
        res = self.api.post(self.URL + '/apigateways', json=data)
        if not res.status_code == 200:
            raise Exception(res.text)
        return res.json()

    def getTransaction(self, id):
        return self.api.get(self.URL + '/gettransaction', json={
            'apiKey': self.apiKey,
            'clientId': self.clientId,
            'id': id
        }).json()

    def refundTransaction(self, id):
        res = self.api.post(self.URL + '/refundtransaction', json={
            'apiKey': self.apiKey,
            'id': id
        })
        if not res.status_code == 200:
            raise Exception(res.text)
        return res.json()
