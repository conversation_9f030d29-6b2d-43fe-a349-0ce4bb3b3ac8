from .base import BaseAPI
from django.conf import settings
from .utils import make_signature
from django.core.cache import cache
from rest_framework import status
import tempfile
import requests
import base64
import os

class Voluti(BaseAPI):
    URL = 'https://api.pix.voluti.com.br'
    client = '538da36a-9170-4ef4-aa24-a38ee628a5a7'
    secret = '2500cc1a-47ad-458d-b368-79b064eed6ae27b4f4b8-509f-4289-9195-f18c74ac89df'
    certificate_crt = ''
    certificate_key = ''

    def generateAcessToken(self):
        res = self.post('/oauth/token', json={
            'client_id': self.client,
            'client_secret': self.secret,
            'grant_type': 'client_credentials'
        })
        return res.json()

    def makeCert(self):
        cert_file = tempfile.NamedTemporaryFile(delete=False)
        key_file = tempfile.NamedTemporaryFile(delete=False)

        cert_file.write(self.certificate_crt.encode('utf-8'))
        key_file.write(self.certificate_key.encode('utf-8'))

        cert_file.close()
        key_file.close()

        return cert_file, key_file

    def getAccessToken(self):
        if cache.get('voluti_access_token'):
            return cache.get('voluti_access_token')
        else:
            token = self.generateAcessToken()
            cache.set('voluti_access_token', token.get('access_token'), token.get('expires_in'))
            return token.get('access_token')

    def __init__(self, acquirer) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('client_secret'):
            self.secret = acquirer.keys.get('client_secret')
        if acquirer.keys.get('client_id'):
            self.client = acquirer.keys.get('client_id')
        if acquirer.keys.get('pixkey'):
            self.pixkey = acquirer.keys.get('pixkey')

        self.certificate_crt = acquirer.keys.get('certificate_crt', '')
        self.certificate_key = acquirer.keys.get('certificate_key', '')

        if self.certificate_crt and self.certificate_key:
            self.cert_file, self.key_file = self.makeCert()
            self.api.cert = (self.cert_file.name, self.key_file.name)
            self.api.verify = False

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Bearer ' + self.getAccessToken()
        })
    def createTransaction(self, **kwargs):
        res = self.post('/cob', json=kwargs, verify=False)
        return res.json()

    def refundTransaction(self, e2eId, id, **kwargs):
        res = self.put(f'/pix/{e2eId}/devolucao/{id}', json=kwargs)
        return res.json()

    def setWebhook(self, url):
        res = self.put(f'/webhook/{self.pixkey}', json={'webhookUrl': url})
        return res.json()
