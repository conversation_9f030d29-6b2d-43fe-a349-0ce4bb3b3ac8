import os

import requests
from requests import Response
from rest_framework import status

from payment.utils.focus_nfe_formatter import (
    FocusNfeFormatter,
    FocusNfeNFSeCancelationResponseData,
    FocusNfeNFSeData,
    FocusNfeNFSeFullData,
)
from splitpay import settings

from .base import BaseAPI

_api_type = 'nfe'

PRODUCTION_BASE_URL = 'https://api.focusnfe.com.br'
TEST_BASE_URL = 'https://homologacao.focusnfe.com.br'


class FocusNFe(BaseAPI):
    def __init__(self, integration) -> None:
        super().__init__()
        self._integration = integration
        self._is_production = (
            str(self._integration.keys.get('is_production', False)).lower() == 'true'
        )
        self._nfse_company = self._integration.user.nfse_company
        self._user_auth = (self._get_user_token(), '')
        self.URL = self._get_base_url()

    def _get_base_url(self) -> str:
        if self._is_production:
            return PRODUCTION_BASE_URL

        return TEST_BASE_URL

    def _get_user_token(self) -> None:
        if self._is_production:
            return self._nfse_company.token_producao

        return self._nfse_company.token_homologacao

    def _get_webhook_url(self) -> str:
        if self._is_production:
            return f'{settings.BACKEND_URL}/api/webhook/nfse/'

        return 'https://webhook.site/31625d3e-36d7-43e3-9dbf-2e723f75cd6a'

    def create_nfse(self, ref: str, nfse_data: FocusNfeNFSeData) -> None:
        nfse_payload = FocusNfeFormatter.new_nfse(nfse_data)
        self.post(
            '/v2/nfse',
            params={'ref': ref},
            json=nfse_payload,
            auth=self._user_auth,
            type=_api_type,
        )

    def cancel_nfse(
        self, nfse_reference: str, justification_text: str
    ) -> FocusNfeNFSeCancelationResponseData:
        payload = FocusNfeFormatter.new_nfse_cancel_payload(justification_text)
        response = self.delete(
            f'/v2/nfse/{nfse_reference}',
            json=payload,
            auth=self._user_auth,
            type=_api_type,
        )

        return FocusNfeFormatter.parse_nfse_cancel_response(response.json())

    def get_nfse(self, nfse_reference: str) -> FocusNfeNFSeFullData:
        response = self.get(
            f'/v2/nfse/{nfse_reference}',
            auth=self._user_auth,
            type=_api_type,
        )

        return FocusNfeFormatter.parse_nfse_full_data_response(response.json())


    def create_webhook(self) -> str:
        response = self.post(
            uri='/v2/hooks',
            json={
                'event': 'nfse',
                'url': self._get_webhook_url(),
                self._nfse_company.doc_type: self._nfse_company.doc_number,
            },
            auth=self._user_auth,
            type=_api_type,
        )

        return response.json()['id']

    def delete_webhook(self):
        self.delete(
            uri=f'/v2/hooks/{self._nfse_company.id_webhook}',
            auth=self._user_auth,
            type=_api_type,
        )

    def download_text_file(self, file_path: str) -> bytes:
        response = self.get(
            uri=file_path,
            auth=self._user_auth,
            type=_api_type,
            stream=True
        )

        return response.content

    def get_error_message(self, res):
        try:
            data = res.json()
            if isinstance(data, list):
                data = data[0]

            error = ';'.join(data.get('erros', []))
            if not error:
                error = data.get('mensagem', '')

            return res.status_code, error

        except Exception:
            raise Exception(f'Status code: {res.status_code}; text: {res.text}')  # noqa: B904


class FocusNFeAdmin(FocusNFe):
    def __init__(self, integration) -> None:
        super().__init__(integration)
        self._admin_token = self._integration.keys['admin_token']
        self._admin_auth = (self._admin_token, '')

    def _get_company_endpoint_params(self) -> dict:
        if self._is_production:
            return {}

        return {'dry_run': 1}

    def create_company(self, data: dict) -> Response:
        return self.post(
            '/v2/empresas',
            customUrl=PRODUCTION_BASE_URL,
            params=self._get_company_endpoint_params(),
            json=data,
            auth=self._admin_auth,
            type=_api_type,
        )

    def delete_company(self, company_id: int) -> Response:
        return self.delete(
            f'/v2/empresas/{company_id}',
            customUrl=PRODUCTION_BASE_URL,
            params=self._get_company_endpoint_params(),
            auth=self._admin_auth,
            type=_api_type,
        )

    def get_company(self, company_id: int) -> Response:
        return self.get(
            f'/v2/empresas/{company_id}',
            params=self._get_company_endpoint_params(),
            auth=self._admin_auth,
            type=_api_type,
        )


class FocusNFeOld(BaseAPI):
    URL = 'https://homologacao.focusnfe.com.br' if settings.DEBUG else 'https://api.focusnfe.com.br'
    main_token = os.environ.get('FOCUS_MAIN_PROD_TOKEN')

    def __init__(self, user) -> None:
        self.api = requests.Session()
        self.user = user

    def create_company(self, data: dict) -> Response:
        return self.post(
            '/v2/empresas',
            'https://api.focusnfe.com.br',
            params={'token': self.main_token},
            json=data,
        )

    def delete_company(self, id: int) -> Response:
        return self.delete(
            f'/v2/empresas/{id}', 'https://api.focusnfe.com.br', params={'token': self.main_token}
        )

    def create_nfse(self, ref: str, data: dict) -> Response:
        return self.post('/v2/nfse', params={'ref': ref, 'token': self.get_user_token()}, json=data)

    def delete_nfse(self, ref: int, data: dict) -> Response:
        return self.delete(f'/v2/nfse/{ref}', params={'token': self.get_user_token()}, json=data)

    def setWebhook(self):
        response = self.post(
            uri='/v2/hooks',
            params={'token': self.get_user_token()},
            json={
                'event': 'nfse',
                'url': 'https://webhook.site/31625d3e-36d7-43e3-9dbf-2e723f75cd6a'
                if settings.DEBUG
                else settings.BACKEND_URL + '/api/webhook/nfse/',
                self.user.nfse_company.doc_type: self.user.nfse_company.doc_number,
            },
        )
        if status.is_success(response.status_code):
            nfse_company = self.user.nfse_company
            nfse_company.id_webhook = response.json()['id']
            nfse_company.save()

    def deleteWebhook(self):
        self.delete(
            uri=f'/v2/hooks/{self.user.nfse_company.id_webhook}',
            params={'token': self.get_user_token()},
        )

    def get_user_token(self) -> None:
        if settings.DEBUG:
            return self.user.nfse_company.token_homologacao
        return self.user.nfse_company.token_producao

    def check_error(self, res, type='default') -> bool:
        return not self.error_condition(res)
