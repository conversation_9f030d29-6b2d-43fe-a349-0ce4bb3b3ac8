from .base import BaseAPI
from django.conf import settings
from .utils import make_signature
from rest_framework import status
from django.core.cache import cache
import tempfile
import requests
import base64
import os

class PagBank(BaseAPI):
    URL = 'https://sandbox.api.pagseguro.com/'
    apiKey = ''

    def get_error_message(self, res):
        try:
            data = res.json()
            if isinstance(data, list):
                data = data[0]
            return data.get('code'), data.get('message')
        except:
            raise Exception(res.text)

    def __init__(self, acquirer) -> None:
        super().__init__()

        if acquirer.customUrl:
           self.URL = acquirer.customUrl

        self.apiKey = acquirer.keys.get('apikey', '')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.apiKey}'
        })

    def createTransaction(self, **kwargs):
        res = self.post(f'/orders', type='payment', json=kwargs)
        return res.json()

    def refund(self, charge_id, **kwargs):
        res = self.post(f'/charges/{charge_id}/cancel', type='refund', json=kwargs)
        return res.json()
