import hashlib
import hmac
import json
from .base import BaseAPI

class SaQ(BaseAPI):
    URL = 'https://api.saq.digital'
    client_id = ""
    client_secret = ""
    secret_key = ""
    bank_agency = ""
    bank_account_number = ""

    def generateAcessToken(self):
        return self.api.post(self.URL + '/v2/finance/auth-token/', json={
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'grant_type': 'client_credentials'
        }).json()

    def getAccessToken(self):
        token = self.generateAcessToken()
        return token.get('access_token')

    def getHMAC(self, payload):
        payload_json_str = json.dumps(payload, ensure_ascii=False)
        payload_json_str = payload_json_str.replace(": ", ":")
        payload_json_str = payload_json_str.replace(", ", ",").encode('utf-8')

        hmac_obj = hmac.new(self.secret_key.encode('utf-8'), payload_json_str, hashlib.sha512)

        return hmac_obj.hexdigest()

    def get_error_message(self, res):
        try:
            data = res.json()
            if data.get('message'):
                return 0, data.get('message')
            return 0, data.get('detail')
        except:
            raise Exception(res.text)


    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
           self.URL = acquirer.customUrl

        self.client_id = acquirer.keys.get('client_id', '')
        self.client_secret = acquirer.keys.get('client_secret', '')
        self.secret_key = acquirer.keys.get('secret_key', '')
        self.bank_agency = acquirer.keys.get('bank_agency', '')
        self.bank_account_number = acquirer.keys.get('bank_account_number', '')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.getAccessToken()}'
        })

    def createPixTransaction(self, **kwargs):
        res = self.post('/v2/finance/create-pix-copy-and-paste/', type='payment', headers={
            'hmac': self.getHMAC(kwargs)
        }, json=kwargs)
        return res.json()

    def withdrawPix(self, **kwargs):
        res = self.post('/v2/finance/create-cashout-self-approve/', type='withdrawal', headers={
            'hmac': self.getHMAC(kwargs)
        }, json=kwargs)
        return res.json()

    def refund(self, **kwargs):
        res = self.post(f'/v2/finance/chargebacks-pix-copy-and-paste/', type='refund', json=kwargs)
        return res.json()

    def balance(self):
        res = self.get(f'/v2/finance/get-balance/?account_branch_identifier={self.bank_agency}&account_number={self.bank_account_number}')
        return res.json().get("amount")

    def getCashoutDetails(self, transaction_id):
        res = self.get(f'/v2/finance/status-cashout/?id={transaction_id}')
        return res.json()
