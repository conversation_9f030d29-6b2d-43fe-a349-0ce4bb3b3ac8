from .base import BaseAPI
from django.conf import settings
import requests
import base64
import os

class Hopypay(BaseAPI):
    URL = 'https://api.sandbox.hopysplit.com.br/v1/'
    secret = 'sk_test_fQvygrascBB77K3DWqTOyBn04gL9NWOO0UK6qMZ3VK'

    def encodedBasicAuth(self):
        return 'Basic ' + base64.b64encode(f"{self.secret}:x".encode('utf-8')).decode('utf-8')

    def __init__(self, acquirer) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('secret_key'):
            self.secret = acquirer.keys.get('secret_key')

        self.api.headers.update({
            'Authorization': self.encodedBasicAuth()
        })

    def createTransaction(self, **kwargs):
        res = self.post('/v1/transactions', type='payment', json=kwargs)
        return res.json()

    def getTransaction(self, id):
        return self.get(f'/v1/transactions/{id}').json()

    def refundTransaction(self, id, **kwargs):
        res = self.post(f'/v1/transactions/{id}/refund', json=kwargs)
        return res.json()
