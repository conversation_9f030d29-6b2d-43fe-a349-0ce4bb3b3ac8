from django.core.cache import cache

from .base import BaseAPI
from ..models import Acquirer
import base64


class PrimePag(BaseAPI):
    URL = "https://api.primepag.com.br/"
    client_id = ""
    client_secret = ""

    def __init__(self, acquirer: Acquirer):
        super().__init__()
        self.URL = acquirer.customUrl if acquirer.customUrl else self.URL
        self.client_id = acquirer.keys.get("client_id")
        self.client_secret = acquirer.keys.get("client_secret")
        self.authenticate()

    def get_basic_auth_token_base_64(self):
        return base64.b64encode(f"{self.client_id}:{self.client_secret}".encode()).decode()

    def generate_access_token(self) -> str:
        self.api.headers.update({
            'Content-Type': 'application/json',
            'Authorization': f'Basic {self.get_basic_auth_token_base_64()}'
        })

        response = self.post("auth/generate_token", json={"grant_type": "client_credentials"})
        response_data = response.json()
        token = response_data.get("access_token")
        expires_in = response_data.get("expires_in")

        cache.set("primepag_token", token, expires_in - 1)

        return token

    def authenticate(self):
        token = cache.get("primepag_token")
        if not token:
            token = self.generate_access_token()

        self.api.headers.update({
            "Authorization": f"Bearer {token}"
        })

    def create_charge(self, **kwargs) -> dict:
        return self.post("  v1/pix/qrcodes", json=kwargs).json()

    def set_webhook(self, **kwargs) -> dict:
        return self.post("v1/webhooks/1", json=kwargs).json()
