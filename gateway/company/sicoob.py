import tempfile

from django.conf import settings
from urllib.parse import urlparse
from django.core.cache import cache

from .base import BaseAPI
from ..models import Acquirer

SCOPE_SET = {
    "cob.read",
    "cob.write",
    "pix.write",
    "pix.read",
    "webhook.read",
    "webhook.write",
    "payloadlocation.write",
    "payloadlocation.read",
    "cco_consulta",
    "cco_transferencias",
    "openid",
    "pixpagamentos_escrita",
    "pixpagamentos_webhook",
    "pixpagamentos_consulta",
}


class Sicoob(BaseAPI):
    URL = "https://sandbox.sicoob.com.br/sicoob/sandbox/" if settings.DEBUG else "https://api.sicoob.com.br/"
    client_id = ""
    access_token = ""
    pix_key = ""
    account_number = ""

    def __init__(self, acquirer: Acquirer):
        super().__init__()

        self.URL = acquirer.customUrl if acquirer.customUrl else self.URL
        self.client_id = acquirer.keys.get("client_id") or self.client_id
        self.pix_key = acquirer.keys.get("pix_key") or self.pix_key
        self.account_number = acquirer.keys.get("account_number") or self.account_number
        self.host = urlparse(self.URL).netloc

        if acquirer.keys.get("certificate"):
            self.certificate = acquirer.keys.get("certificate")
            self.cert_file = self.make_cert()
            self.api.cert = self.cert_file.name

        if "sandbox" in self.URL:
            self.access_token = acquirer.keys.get("access_token") or self.access_token
        else:
            self.authenticate()

        self.api.headers = ({
            "Authorization": f"Bearer {self.access_token}",
            "client_id": self.client_id,
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Host": self.host,
        })

    def make_cert(self):
        cert_file = tempfile.NamedTemporaryFile(delete=False)
        cert_file.write(self.certificate.encode('utf-8'))
        cert_file.close()
        return cert_file

    def authenticate(self):
        if access_token := cache.get("sicoob_access_token"):
            self.access_token = access_token
            return

        cert_file_name = self.cert_file.name if self.cert_file else ""

        self.api.headers.update({"Content-Type": "application/x-www-form-urlencoded"})
        data = {
            "grant_type": "client_credentials",
            "client_id": self.client_id,
            "scope": " ".join(SCOPE_SET),
        }
        res = self.post(
            "https://auth.sicoob.com.br/auth/realms/cooperado/protocol/openid-connect/token",
            data=data,
            cert=cert_file_name
        )
        self.access_token = res.json().get("access_token")
        cache.set("sicoob_access_token", self.access_token, timeout=res.json().get("expires_in"))

    def create_charge(self, **kwargs):
        return self.post("pix/api/v2/cob", json=kwargs).json()

    def get_pix_from_e2eid(self, e2eid):
        return self.get(f"pix/api/v2/pix/{e2eid}").json()

    def refund(self, internal_refund_id, e2eid, **kwargs):
        """
        @param internal_refund_id: internal id of the refund
        @param e2eid: end-to-end id of the payment
        @param kwargs: request body with the valor field
        @return:
        """
        return self.put(f"pix/api/v2/pix/{e2eid}/devolucao/{internal_refund_id}", json=kwargs).json()

    def balance(self):
        return self.get("conta-corrente/v4/saldo").json().get("saldo")

    def create_webhook(self, key: str, **kwargs):
        """
        @param key: Chave DICT do recebedor
        @param kwargs:
        @return:
        """
        return self.put(f"webhook/{key}", json=kwargs).json()

    def create_sent_pix_webhook(self, **kwargs):
        """
        Webhook para notificações acerca de Pix enviados.
        @return:
        """
        return self.put("pagamentos/webhook", json=kwargs).json()

    def start_withdraw(self, pix_key):
        return self.post(
            uri="pix-pagamentos/v2/pagamentos",
            json={
                "chave": pix_key,
            }
        ).json()

    def confirm_withdraw(self, **kwargs):
        return self.post("pix-pagamentos/v2/pagamentos/confirmacao", json=kwargs).json()
