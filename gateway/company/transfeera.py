from .base import BaseAPI
from django.conf import settings
from .utils import make_signature
from django.core.cache import cache
from rest_framework import status
import requests
import base64
import os

class Transfeera(BaseAPI):
    URL = 'https://api-sandbox.transfeera.com'
    LOGIN_URL = 'https://login-api-sandbox.transfeera.com'
    client = '538da36a-9170-4ef4-aa24-a38ee628a5a7'
    secret = '2500cc1a-47ad-458d-b368-79b064eed6ae27b4f4b8-509f-4289-9195-f18c74ac89df'

    def generateAcessToken(self):
        return self.api.post(self.LOGIN_URL + '/authorization', data={
            'client_id': self.client,
            'client_secret': self.secret,
            'grant_type': 'client_credentials'
        }).json()

    def getAccessToken(self):
        if cache.get('transfeera_access_token'):
            return cache.get('transfeera_access_token')
        else:
            token = self.generateAcessToken()
            cache.set('transfeera_access_token', token.get('access_token'), token.get('expires_in'))
            return token.get('access_token')

    def __init__(self, acquirer) -> None:
        super().__init__()
        # if acquirer.customUrl:
        #     self.URL = acquirer.customUrl

        # if acquirer.keys.get('secret'):
        #     self.secret = acquirer.keys.get('secret')
        # if acquirer.keys.get('client'):
        #     self.client = acquirer.keys.get('client')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Bearer ' + self.getAccessToken()
        })

    def createTransaction(self, **kwargs):
        res = self.post('/charges/', json=kwargs)
        if not res.status_code == 201:
            raise Exception(res.text)
        return res.json()

    def getTransaction(self, id):
        return self.get('/charges/' + id).json()

    def refundTransaction(self, id, **kwargs):
        res = self.post(self.URL + '/api/transactions/' + id + '/refund', json=kwargs)
        if not res.status_code == 200:
            raise Exception(res.text)
        return res.json()
