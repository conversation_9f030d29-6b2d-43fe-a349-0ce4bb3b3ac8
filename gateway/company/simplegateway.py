from .base import BaseAPI
from django.conf import settings
from .utils import make_signature
from django.core.cache import cache
from rest_framework import status
import requests
import base64
import os

class SimpleGateway(BaseAPI):
    URL = 'https://api.simplegateway.net'

    def set_basic_auth(self):
        client = self.acquirer.keys.get('client_id')
        secret = self.acquirer.keys.get('client_secret')
        return base64.b64encode(f'{client}:{secret}'.encode()).decode()

    def __init__(self, acquirer):
        super().__init__()
        self.acquirer = acquirer
        if self.acquirer.customUrl:
            self.URL = self.acquirer.customUrl

        self.getAccessToken()

    def getAccessToken(self):
        if cache.get('simplegateway_access_token'):
            self.access_token = cache.get('simplegateway_access_token')
            self.api.headers.update({
                'Authorization': f'Bearer {self.access_token}'
            })
        else:
            response = self.post('/v2/oauth/token',
                headers={
                    'Authorization': f'Basic {self.set_basic_auth()}'
                },
                json={
                    'grant_type': 'client_credentials'
                }
            )
            data = response.json()
            cache.set('simplegateway_access_token', data.get('access_token'), data.get('expires_in')-1)

            self.api.headers.update({
                'Authorization': f'Bearer {data.get('access_token')}'
            })
    def createPayment(self, **kwargs):
        response = self.post('/v2/pix/qrcode', json=kwargs)
        return response.json()

    def refundPayment(self, **kwargs):
        response = self.post('/v1/refunds', json=kwargs)
        return response.json()
