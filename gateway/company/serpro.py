import base64
import datetime
from urllib.parse import urljoin

from gateway.company.utils import <PERSON>rrorException

from .base import BaseAPI

_SERPRO_ERROR_STATUS_CODE_MAPPING = {
    400 : 'Número de CPF inválido',
    401 : 'Requisição não autenticada',
    403 : 'Requisição não autorizada',
    404 : 'CPF não encontrado',
    422 : 'CPF pertence a menor de idade',
    500 : 'Erro interno no servidor',
    504 : 'Tempo esgotado no gateway'
}

def _get_token_expiration_date(response_json) -> datetime.datetime:
    try:
        expiration_in_seconds = max(int(response_json['expires_in']) - 60, 0)
    except Exception:
        expiration_in_seconds = 0

    return (
        datetime.datetime.now(tz=datetime.timezone.utc) +
        datetime.timedelta(seconds=expiration_in_seconds)
    )

class Serpro(BaseAPI):
    def _auth_token_is_none_or_expired(self):
        return (self._access_token is None or
                self._auth_token_expired_date < datetime.datetime.now(tz=datetime.timezone.utc))


    def _update_access_token_and_headers_if_needed(self):
        if self._auth_token_is_none_or_expired():
            response_json = self._get_access_token()
            self._access_token = response_json['access_token']
            self._auth_token_expired_date = _get_token_expiration_date(response_json)
            self.api.headers.update({
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': f'Bearer {self._access_token}'
            })

    def _get_access_token(self):
        try:
            response = self.api.post(
                urljoin(self.URL, 'token'),
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': f'Basic {self._basic_auth_token}'
                },
                data={
                    'grant_type': 'client_credentials'
                }
            )

            if self.error_condition(response):
                raise ErrorException(
                    message=f'Erro ao obter token de acesso. Erro: {response.text}',
                    code=-response.status_code,
                    type=self.api_type
                )

            return response.json()
        except Exception as e:
            raise ErrorException(
                message=f'Erro ao obter token de acesso: {e}',
                code=-1,
                type=self.api_type) from None

    def __init__(self, integration) -> None:
        super().__init__()
        self.api_type = 'entity_information'
        self.integration = integration
        self.URL = self.integration.keys['base_url']
        self._consumer_key = self.integration.keys['consumer_key']
        self._consumer_secret = self.integration.keys['consumer_secret']
        self._basic_auth_token = base64.b64encode(
            f'{self._consumer_key}:{self._consumer_secret}'.encode('utf-8')
        ).decode("utf-8")
        self._access_token = None
        self._auth_token_expired_date = datetime.datetime.now(tz=datetime.timezone.utc)

    def get_error_message(self, res):
        if res.status_code in _SERPRO_ERROR_STATUS_CODE_MAPPING:
            return res.status_code, _SERPRO_ERROR_STATUS_CODE_MAPPING[res.status_code]

        return res.status_code, 'Erro desconhecido'

    def get_data_from_cpf(self, cpf:str) -> dict:
        self._update_access_token_and_headers_if_needed()
        final_url = f'consulta-cpf-df/v1/cpf/{cpf}'
        response = self.get(
            final_url,
            type=self.api_type
        )

        return response.json()
