from .base import BaseAPI
from django.core.cache import cache

class Cielo(BaseAPI):
    URL = 'https://apisandbox.cieloecommerce.cielo.com.br'
    merchantId = ""
    merchantKey = ""

    def get_error_message(self, res):
        try:
            data = res.json()
            if "errors" in data and isinstance(data["errors"], list) and data["errors"]:
                data = data["errors"][0]
            return 0, data.get('message')
        except:
            raise Exception(res.text)

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
           self.URL = acquirer.customUrl

        self.merchantId = acquirer.keys.get("merchant_id", "")
        self.merchantKey = acquirer.keys.get("merchant_key", "")

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'MerchantId': f'{self.merchantId}',
            'MerchantKey': f'{self.merchantKey}',
        })

    def createTransactionWithPIX(self, **kwargs):
        res = self.post("/1/sales/", type="payment", json=kwargs)
        return res.json()

    def createTransactionWithWallet(self, **kwargs):
        res = self.post("/1/sales/", type="payment", json=kwargs)
        return res.json()

    def refund(self, paymentId):
        res = self.put(f"/1/sales/{paymentId}/void", type="refund")
        return res.status_code == 200
