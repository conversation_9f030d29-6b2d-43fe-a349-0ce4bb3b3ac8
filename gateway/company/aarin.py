from .base import BaseAPI
from django.conf import settings
from .utils import make_signature
from rest_framework import status
from django.core.cache import cache
import tempfile
import requests
import base64
import os

class <PERSON>arin(BaseAPI):
    URL = 'https://baas-sandbox.aarin.dev'
    client_id = ''
    client_secret = ''
    account_id = ''
    partner_id = ''
    pixKey = ''
    certificate_crt = ''
    certificate_key = ''

    def generateAcessToken(self):
        return self.api.post(self.URL + '/baas/auth/token/login', data={
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'grant_type': 'client_credentials'
        }).json()

    def getAccessToken(self):
        if cache.get('aarin_access_token'):
            return cache.get('aarin_access_token')
        else:
            token = self.generateAcessToken()
            cache.set('aarin_access_token', token.get('access_token'), token.get('expires_in'))
            return token.get('access_token')

    def makeCert(self):
        cert_file = tempfile.NamedTemporaryFile(delete=False)
        key_file = tempfile.NamedTemporaryFile(delete=False)

        cert_file.write(self.certificate_crt.encode('utf-8'))
        key_file.write(self.certificate_key.encode('utf-8'))

        cert_file.close()
        key_file.close()

        return cert_file, key_file

    def get_error_message(self, res):
        try:
            data = res.json()
            if isinstance(data, list):
                data = data[0]

            message = data.get('message')
            if data.get('code') == 'not_found':
                message = 'Chave pix não encontrada'

            return data.get('code'), message
        except:
            raise Exception(res.text)

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
           self.URL = acquirer.customUrl

        self.client_secret = acquirer.keys.get('client_secret', '')
        self.client_id = acquirer.keys.get('client_id', '')
        self.account_id = acquirer.keys.get('account_id', '')
        self.partner_id = acquirer.keys.get('partner_id', '')
        self.pixKey = acquirer.keys.get('pixKey', '')
        self.certificate_crt = acquirer.keys.get('certificate_crt', '')
        self.certificate_key = acquirer.keys.get('certificate_key', '')
        self.webhookToken = acquirer.keys.get('webhook_token', '')

        if self.certificate_crt and self.certificate_key:
            self.cert_file, self.key_file = self.makeCert()
            self.api.cert = (self.cert_file.name, self.key_file.name)

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.getAccessToken()}'
        })

    def balance(self):
        res = self.get('/baas/account/balance', headers = {
            'X-AccountId': self.account_id,
            'X-PartnerID':self.partner_id,        })
        return res.json().get('available')

    def createPixTransaction(self, txId, **kwargs):
        res = self.put(f'/baas-pix/dynamic-charges/{txId}', headers = {
            'X-AccountId': self.account_id,
            'X-PartnerID':self.partner_id,        }, json=kwargs)
        return res.json()

    def refundPix(self, e2eId, id, **kwargs):
        res = self.put(f'/baas-pix/pix/{e2eId}/refund/{id}', headers = {
            'X-AccountId': self.account_id,
            'X-PartnerID':self.partner_id,        }, json=kwargs)
        return res.json()

    def getPix(self, txId):
        res = self.get(f'/baas-pix/dynamic-charges/{txId}', headers = {
            'X-AccountId': self.account_id,
            'X-PartnerID':self.partner_id,        })
        return res.json()

    def withdrawPix(self, id, pixKey, **kwargs):
        res = self.get(f'/baas-pix/v2/entries/{pixKey}', type='withdrawal', headers = {
            'X-AccountId': self.account_id,
            'X-PartnerID':self.partner_id,
        })
        keyData = res.json()

        res = self.put(f'/baas-pix/v2/pix/{id}', type='withdrawal', headers = {
            'X-AccountId': self.account_id,
            'X-PartnerID':self.partner_id,
            'X-PaymentInitiationId': keyData.get("paymentInitiationId"),
        }, json=kwargs)
        return res.json()

    def checkPixKey(self, key):
        res = self.get(f'/baas-pix/v2/entries/{key}', type='withdrawal', headers={
            'X-AccountId': self.account_id,
            'X-PartnerID':self.partner_id
        })
        return res

    def createBoletoTransaction(self, id, **kwargs):
        res = self.put(f'/baas-boleto/boletos/{id}', headers = {
            'X-AccountId': self.account_id,
            'X-PartnerID':self.partner_id,        }, json=kwargs)
        return res.json()

    def getBoleto(self, id):
        res = self.get(f'/baas-boleto/boletos/{id}', headers = {
            'X-AccountId': self.account_id,
            'X-PartnerID':self.partner_id,        })
        return res.json()

    def setWebhook(self, type, url, **kwargs):
        res = self.put(f'/baas-notification/webhook/{type}', json={
          'webhookUrl': url
        })
        return res.json()
