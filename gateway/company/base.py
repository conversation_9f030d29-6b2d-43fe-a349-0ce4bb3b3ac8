from urllib.parse import urljoin
from .utils import ErrorException
from rest_framework import status
import requests

class BaseAPI:
    URL = None

    def __init__(self, debug=False) -> None:
        self.api = requests.Session()

    def get_error_message(self, res):
        return res.status_code, res.text

    def error_condition(self, res: requests.Response) -> bool:
        return not status.is_success(res.status_code)

    def check_error(self, res, type='default') -> None:
        if self.error_condition(res):
            code, message = self.get_error_message(res)
            raise ErrorException(message, code, type)

    def post(self, uri, customUrl=None, type='default', **kwargs) -> requests.Response:
        kwargs['timeout'] = 60
        if not self.URL:
            raise NotImplementedError('URL not defined')

        res = self.api.post(urljoin(customUrl or self.URL, uri), **kwargs)

        self.check_error(res, type)

        return res

    def get(self, uri, customUrl=None, type='default', **kwargs) -> requests.Response:
        if not self.URL:
            raise NotImplementedError('URL not defined')

        res = self.api.get(urljoin(customUrl or self.URL, uri), **kwargs)

        self.check_error(res, type)

        return res

    def put(self, uri, customUrl=None, type='default', **kwargs) -> requests.Response:
        if not self.URL:
            raise NotImplementedError('URL not defined')
        res = self.api.put(urljoin(customUrl or self.URL, uri), **kwargs)

        self.check_error(res, type)

        return res

    def delete(self, uri, customUrl=None, type='default', **kwargs) -> requests.Response:
        if not self.URL:
            raise NotImplementedError('URL not defined')
        res = self.api.delete(urljoin(customUrl or self.URL, uri), **kwargs)

        self.check_error(res, type)

        return res
