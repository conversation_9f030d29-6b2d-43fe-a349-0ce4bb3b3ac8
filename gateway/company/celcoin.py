from .base import BaseAPI
from django.conf import settings
from rest_framework import status
from django.core.cache import cache
import tempfile
import requests
import base64
import os

class Celcoin(BaseAPI):
    URL = 'https://sandbox.openfinance.celcoin.dev'
    client_id = ''
    client_secret = ''
    accountId = ''
    pixKey = ''

    def generateAcessToken(self):
        return self.api.post(self.URL + '/v5/token', data={
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'grant_type': 'client_credentials'
        }).json()

    def getAccessToken(self):
        if cache.get('celcoin_access_token'):
            return cache.get('celcoin_access_token')
        else:
            token = self.generateAcessToken()
            cache.set('celcoin_access_token', token.get('access_token'), token.get('expires_in'))
            return token.get('access_token')

    def get_error_message(self, res):
        data = res.json()
        if isinstance(data, list):
            data = data[0]
        
        error = data.get('error')
        if error:
            return error.get('errorCode'), error.get('message')
        
        return 0, res.text

    def __init__(self, acquirer, type=None) -> None:
        super().__init__()

        if acquirer.customUrl:
          self.URL = acquirer.customUrl

        self.client_id = acquirer.keys.get('client_id', '')
        self.client_secret = acquirer.keys.get('client_secret', '')
        self.accountId = acquirer.keys.get('accountId', '')
        self.pixKey = acquirer.keys.get('pixKey', '')

        self.api.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.getAccessToken()}'
        })

    def balance(self):
        res = self.get(f'/baas-walletreports/v1/wallet/balance?Account={self.accountId}')
        return res.json().get('body').get('amount')

    def createLocationTransaction(self, payment_data):
        res = self.post('/pix/v1/location', type='payment', json={
            "clientRequestId": str(payment_data.id),
            "type": "COB",
            "merchant": {
                "postalCode": payment_data.acquirer.keys.get('merchant_zipcode'),
                "city": payment_data.acquirer.keys.get('merchant_city'),
                "merchantCategoryCode": "0000", 
                "name": payment_data.acquirer.keys.get('merchant_name')
            }
        })
        return res.json()
    
    def createPixTransaction(self, **kwargs):
        res = self.post('/pix/v1/collection/immediate', type='payment', json=kwargs)
        return res.json()

    def refundPixTransaction(self, payment_data):
        res = self.post(f'/baas-wallet-transactions-webservice/v1/pix/reverse', type='refund', json={
            'id': payment_data.externalId,
            'endToEndId': payment_data.e2eId,
            'clientCode': payment_data.id,
            'amount': str(format(payment_data.amount, '.2f')),
            'reason': 'MD06',
            'reversalDescription': 'Reembolso de pagamento'
        })
        return res.json()
    
    def withdrawPix(self, **kwargs):
        kwargs['debitParty']['account'] = self.accountId

        res = self.post(f'/baas-wallet-transactions-webservice/v1/pix/payment', type='withdrawal', json=kwargs)
        return res.json()
    
    def setWebhook(self, type, url):
        res = self.post(f'/baas-webhookmanager/v1/webhook/subscription', json={
            "entity": type,
            "webhookUrl": url
        })
        return res.json()
    
    def updateWebhook(self, type, url):
        res = self.put(f'/baas-webhookmanager/v1/webhook/subscription/{type}', json={
            "webhookUrl": url,
            "active": True
        })
        return res.json()
