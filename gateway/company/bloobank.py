from .base import BaseAPI
from rest_framework import status
from ellipticcurve.ecdsa import Ecdsa
from ellipticcurve.privateKey import Private<PERSON>ey
import time
import base64
import json
import os

class Bloobank(BaseAPI):
    URL = 'https://api.bloobank.com.br/'
    token = ''

    def make_signature(self, body):
        timestamp = str(int(time.time() * 1000))
        body = json.dumps(body)
        message = f"{self.accessKey}|{body}|{timestamp}"
        private_key = PrivateKey.fromPem(self.privateKey)

        # Sign the message (encoded in utf-8)
        signature = Ecdsa.sign(message, private_key)

        return signature, timestamp

    def get_error_message(self, res):
        return None, res.json().get('description')

    def __init__(self, acquirer) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('accessKey'):
            self.accessKey = acquirer.keys.get('accessKey')

        if acquirer.keys.get('privateKey'):
            self.privateKey = acquirer.keys.get('privateKey')

        self.api.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })

    def createTransaction(self, **kwargs):
        signature, timestamp = self.make_signature(kwargs)
        self.api.headers.update({
            "X-Access-Key": self.accessKey,
            "X-Access-Timestamp": str(timestamp),
            "X-Access-Signature": signature.toBase64()
        })

        res = self.post('/v1/payments', type='payment', json=kwargs)
        return res.json()
