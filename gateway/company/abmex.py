from .base import BaseAPI
from rest_framework import status
import base64
import os

class Abmex(BaseAPI):
    URL = 'https://sandbox.abmexpay.com.br/api'
    token = ''

    def getAuthorization(self):
        return 'Bearer ' + self.token

    def get_error_message(self, res):
        return None, res.json().get('description')

    def __init__(self, acquirer) -> None:
        super().__init__()
        if acquirer.customUrl:
            self.URL = acquirer.customUrl

        if acquirer.keys.get('token'):
            self.token = acquirer.keys.get('token')

        self.api.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json",
            'Authorization': self.getAuthorization()
        })

    def createTransaction(self, **kwargs):
        res = self.post('/api/sells', type='payment', json=kwargs)
        return res.json()

    def getTransaction(self, id):
        return self.api.get(self.URL + 'transactions/' + id).json()

    def refundTransaction(self, id, **kwargs):
        res = self.post('sells/' + id + '/refund', json=kwargs)
        if not res.status_code == 200:
            raise Exception(res.text)
        return res.json()
