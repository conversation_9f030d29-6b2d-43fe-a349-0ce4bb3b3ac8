from django.conf import settings
from django.core.mail import EmailMessage
from django.template.loader import render_to_string


def send_email_template(to: list[str], subject: str, template_file_name: str, context: dict) -> int:
    html_content = render_to_string(template_file_name, context)

    email = EmailMessage(
        subject=subject,
        body=html_content,
        from_email=settings.DEFAULT_FROM_MAIL,
        to=to,
    )
    email.content_subtype = 'html'
    return  email.send(fail_silently=False)
