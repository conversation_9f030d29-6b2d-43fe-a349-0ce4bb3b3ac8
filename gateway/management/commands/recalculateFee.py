from django.core.management.base import BaseCommand
from gateway.models import PendingBalance
from payment.models import Payment
from decimal import Decimal

class Command(BaseCommand):
    help = 'Recalculate fee for a period of time'

    def handle(self, *args, **options):
        self.stdout.write(self.style.WARNING('Start recalculate fee'))
        for payment in Payment.objects.filter(status='paid', createdAt__range=['2024-05-16', '2024-05-21']):
            if PendingBalance.objects.filter(payment=payment, status='pending').count() == 2:
                fee = payment.user.getFeeByPaymentMethod(payment.acquirer.id, payment.paymentMethod)

                payment.fee = 0

                # Calculate fixed fee
                payment.fixedFee = fee.get('fixed')
                payment.fee += payment.fixedFee

                # Percentage fee
                interest = 0.16
                payment.percentageFee = Decimal(payment.baseAmount) * Decimal(interest / 100)
                payment.fee += payment.percentageFee

                # Calculate liquid amount
                payment.liquidAmount = Decimal(payment.baseAmount) - payment.fee
                payment.save()

                interest = payment.user.getInterest(payment, payment.acquirer)

                # Add pending balance
                reserve = payment.user.getReserveByPaymentMethod(payment.paymentMethod)
                reserveAmount = payment.liquidAmount * reserve.get('reserve') / 100
                releaseAmount = payment.liquidAmount - reserveAmount

                payment.profit = payment.fee + payment.interest - payment.cost

                payment.producerSplit.pendingBalance.amount = releaseAmount
                payment.producerSplit.pendingBalanceReserve.amount = reserveAmount
                payment.producerSplit.pendingBalance.remainingAmount = min(payment.producerSplit.pendingBalance.remainingAmount, releaseAmount)
                payment.producerSplit.pendingBalanceReserve.remainingAmount = min(payment.producerSplit.pendingBalanceReserve.remainingAmount, reserveAmount)
                payment.producerSplit.pendingBalance.save()
                payment.producerSplit.pendingBalanceReserve.save()

        self.stdout.write(self.style.SUCCESS('Successfully add missing pending balance'))
