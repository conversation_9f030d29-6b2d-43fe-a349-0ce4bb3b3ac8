from django.core.management.base import BaseCommand, CommandError
from payment.strategies.webhook.base import WebhookStategy
from gateway.models import PendingBalance
from payment.models import Payment
from datetime import timedelta
from gateway.tasks import updateBalance
from django.utils import timezone
from user.models import User

class Command(BaseCommand):
    help = 'Add missing pending balance'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Checking missing pending balance...'))
        for payment in Payment.objects.all():
            WebhookStategy().handle_balance(payment)
        self.stdout.write(self.style.SUCCESS('Successfully add missing pending balance'))
