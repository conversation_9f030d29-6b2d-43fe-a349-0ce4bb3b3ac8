from django.core.management.base import BaseCommand, CommandError
from gateway.models import PendingBalance
from payment.models import Payment
from datetime import timedelta
from gateway.tasks import updateBalance
from django.utils import timezone
from payment.strategies.webhook.base import WebhookStategy
from user.models import User

class Command(BaseCommand):
    help = 'Recalculate the balance of the users'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Recalculating the balances...'))
        User.objects.all().update(balance=0)
        for payment in Payment.objects.all():
            WebhookStategy().handle_balance(payment)

        self.stdout.write(self.style.SUCCESS('Successfully recalculated the balance'))
