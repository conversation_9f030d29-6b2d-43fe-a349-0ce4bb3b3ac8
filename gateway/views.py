from collections import defaultdict
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Any
from django.db.models.functions.datetime import ExtractHour

import pytz
import waffle
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.core.validators import validate_integer
from django.db.models import (
    Avg,
    Case,
    Count,
    DecimalField,
    IntegerField,
    Max,
    Min,
    Q,
    QuerySet,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Coalesce, TruncMonth, ExtractHour
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.timezone import make_aware, now
from django_filters.rest_framework import DjangoFilterBackend
from drf_excel.renderers import XLSXRenderer
from rest_flex_fields import is_expanded
from rest_flex_fields.views import FlexFieldsMixin
from rest_framework import (
    filters,
    generics,
    mixins,
    pagination,
    permissions,
    status,
    views,
    viewsets,
)
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON>er
from rest_framework.response import Response
from rest_framework.viewsets import ReadOnlyModelViewSet

from gateway.models import PlanFee
from rest_framework_csv.renderers import CSVRenderer
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.pagination import PageNumberPagination

from gateway.company import Aarin, Asaas, Efi, Pixcred, Sicoob, SaQ
from gateway.company.utils import WithdrawalException
from payment.models import Payment
from payment.services.acquirer_factory import AcquirerStrategyFactory
from payment.services.cashout_factory import CashoutStrategyFactory
from user.models import User, Withdrawal
from user.permissions import (
    AccessDashboardPermission,
    DenyAllPermission,
    FullAccessPermission,
    ManageAcquirersPermission,
    ManageAnticipationsPermission,
    ManageWithdrawalsPermission, PlanFeePermission,
)
from user.serializers import SellerWithdrawalSerializer, WithdrawalRequestSerializer

from .filters import ExtractFilter, PendingBalanceAdminFilter, WithdrawalFilter
from .models import Acquirer, DefaultFee, Extract, Fee, PendingBalance, Settings
from .serializers import (
    AcquirerSerializer,
    DefaultFeeSerializer,
    ExtractSerializer,
    FeeCheckoutSerializer,
    FeeSerializer,
    PendingBalanceByDateSerializer,
    PendingBalanceSerializer,
    SettingsSerializer,
    PlanFeeSerializer,
)
from .tasks import send_extract_email
from .utils import create_antecipation, remove_query_param


class DefaultFeeView(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = DefaultFeeSerializer

    def get_object(self):
        user: User = self.request.user
        if user.is_staff or user.is_superuser:
            return DefaultFee.get_or_cache('defaultFee', 60)
        fee, created = Fee.objects.get_or_create(user=user)
        return fee

    # Only admin can edit default fee
    def update(self, request, *args, **kwargs):
        if request.user.is_superuser:
            return super().update(request, *args, **kwargs)
        else:
            return Response(status=status.HTTP_403_FORBIDDEN)


class ImpersonateAPI(views.APIView):
    def get(self, request, pk):
        user = User.objects.get(pk=pk)
        refresh = RefreshToken.for_user(user)
        return Response(
            {'url': f'{settings.FRONTEND_URL}?accessToken={str(refresh.access_token)}'},
            status=status.HTTP_200_OK,
        )


class FeeView(generics.RetrieveUpdateAPIView):
    serializer_class = FeeSerializer

    def get_object(self):
        user = self.request.user
        if self.request.user.is_superuser or self.request.user.is_staff:
            user = get_object_or_404(User, pk=self.kwargs['pk'])
        fee, created = Fee.objects.get_or_create(user=user)
        return fee


class AcquirerAPI(FlexFieldsMixin, ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]

    queryset = Acquirer.objects.filter(enabled=True)
    serializer_class = AcquirerSerializer
    pagination.PageNumberPagination.page_size = 100

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [FullAccessPermission(), ManageAcquirersPermission()]

        has_permissions = [
            permission
            for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_serializer(self, *args, **kwargs):
        context = self.get_serializer_context()
        if waffle.switch_is_active('get_metrics_from_bigquery'):
            from bigquery.acquirer_metrics import client

            context['acquirer_metrics'] = client.get_all_metrics_at_once()
        kwargs['context'] = context
        return super().get_serializer(*args, **kwargs)


class AcquirerObjectAPI(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = AcquirerSerializer

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [IsAdminUser(), FullAccessPermission(), ManageAcquirersPermission()]

        has_permissions = [
            permission
            for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_object(self):
        return Acquirer.objects.get(pk=self.kwargs['pk'])

    def partial_update(self, request, *args, **kwargs):
        if request.data.get('onboardEnabled') == True:
            Acquirer.objects.filter(onboardEnabled=True).update(onboardEnabled=False)
        if request.data.get('cashoutEnabled') == True:
            Acquirer.objects.filter(cashoutEnabled=True).update(cashoutEnabled=False)

        res = super().partial_update(request, *args, **kwargs)

        obj = self.get_object()
        try:
            factory = AcquirerStrategyFactory.get_strategy(obj.acquirer_gateway)
            if factory:
                factory.handle_setup(obj)
        except:
            pass
        return res


class SellerFeeAPI(viewsets.ViewSet):
    serializer_class = FeeCheckoutSerializer

    def retrieve(self, request):
        user = request.user
        account_id = request.query_params.get('account_id', None)

        if account_id and not account_id.isdigit():
            return Response({'detail': 'account_id invalid'}, status=status.HTTP_400_BAD_REQUEST)

        if user.is_superuser and account_id:
            user = get_object_or_404(User, id=account_id)

        return Response(user.installmentFee(), status=status.HTTP_200_OK)

class SellerWithdrawPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    max_page_size = 100


class SellerWithdrawAPI(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = SellerWithdrawalSerializer
    pagination_class = SellerWithdrawPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = WithdrawalFilter

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = []
        if self.action == 'create':
            if self.request.data.get('type') == 'antecipation':
                necessary_permissions = [
                    IsAdminUser(),
                    FullAccessPermission(),
                    ManageAnticipationsPermission(),
                ]
            else:
                necessary_permissions = [
                    IsAdminUser(),
                    FullAccessPermission(),
                    ManageWithdrawalsPermission(),
                ]

        has_permissions = [
            permission
            for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return []

    def get_queryset(self):
        user = self.request.user
        if self.request.user.is_superuser:
            if self.request.query_params.get('account_id'):
                user = get_object_or_404(User, id=self.request.query_params.get('account_id'))
        return Withdrawal.objects.filter(user=user).order_by('-createdAt')

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        grouped_param = request.query_params.get('grouped', 'false').lower() == 'true'

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            if not grouped_param:
                return self.get_paginated_response(serializer.data)
            else:
                grouped = defaultdict(lambda: defaultdict(list))
                for data in serializer.data:
                    createdAt = data.get('createdAt')
                    dt = datetime.strptime(createdAt[:10], "%Y-%m-%d")
                    month_year = dt.strftime("%Y-%m")
                    day = dt.strftime("%Y-%m-%d")
                    grouped[month_year][day].append({
                        "id": data.get("id"),
                        "amount": data.get("amount"),
                        "status": data.get("status"),
                    })

                grouped_dict = {month: dict(days) for month, days in grouped.items()}

                next_link = remove_query_param(self.paginator.get_next_link(), 'account_id')
                previous_link = remove_query_param(self.paginator.get_previous_link(), 'account_id')

                return Response({
                    "count": self.paginator.page.paginator.count,
                    "next": next_link,
                    "previous": previous_link,
                    "results": grouped_dict
                }, status=status.HTTP_200_OK)

        serializer = self.get_serializer(queryset, many=True)
        if not grouped_param:
            return Response(serializer.data, status=status.HTTP_200_OK)

        grouped = defaultdict(lambda: defaultdict(list))
        for data in serializer.data:
            createdAt = data.get('createdAt')
            dt = datetime.strptime(createdAt[:10], "%Y-%m-%d")
            month_year = dt.strftime("%Y-%m")
            day = dt.strftime("%Y-%m-%d")
            grouped[month_year][day].append({
                "id": data.get("id"),
                "amount": data.get("amount"),
                "status": data.get("status"),
            })

        grouped_dict = {month: dict(days) for month, days in grouped.items()}
        return Response(grouped_dict, status=status.HTTP_200_OK)


    def create(self, request, pk=None):
        # check if request.user is owner of the account
        if pk:
            user = User.objects.get(pk=pk)
            if request.user == user.owner:
                user = User.objects.get(pk=pk)
            else:
                return Response(
                    {'detail': 'Você não tem permissão para realizar esta ação'},
                    status=status.HTTP_403_FORBIDDEN,
                )
        else:
            user = request.user

        if user.status == 'blocked':
            return Response(
                {'detail': 'Esta conta está bloqueada'}, status=status.HTTP_403_FORBIDDEN
            )

        serializer = WithdrawalRequestSerializer(data=request.data, context={'user': user})
        if serializer.is_valid():
            if (
                user.balance < serializer.validated_data.get('amount')
                and serializer.validated_data.get('type') != 'antecipation'
            ):
                return Response(
                    {'detail': 'Saldo insuficiente para solicitar um saque'},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            withdrawFee = user.getWithdrawFee()
            fee = user.getFee()

            # Calculate total fee
            if serializer.validated_data.get('type') == 'antecipation':
                totalFee = Decimal(serializer.validated_data.get('amount')) * (
                    Decimal(fee.get('antecipationPercentage') / 100)
                    if fee.get('antecipationPercentage')
                    else Decimal('0')
                )
            else:
                totalFee = Decimal(withdrawFee.get('fixed'))

            # Check if balance is enough to cover fees
            if serializer.validated_data.get('type') == 'antecipation':
                anticipationBalance = user.getAnticipationBalance()
                if anticipationBalance < serializer.validated_data.get('amount'):
                    return Response(
                        {'detail': 'Saldo pendente insuficiente para cobrir as taxas'},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            elif user.balance < serializer.validated_data.get('amount'):
                return Response(
                    {'detail': 'Saldo insuficiente para cobrir as taxas'},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Deduce balance
            extract: Extract = None
            if serializer.validated_data.get('type') != 'antecipation':
                withdraw_amount = serializer.validated_data.get('amount')
                extract = user.deduct_balance(withdraw_amount, transaction_type=Extract.WITHDRAWAL)

            totalLessFee = Decimal(serializer.validated_data.get('amount')) - totalFee

            # Finalize
            if serializer.validated_data.get('type') == 'antecipation':
                if not create_antecipation(user, serializer.validated_data.get('amount'), totalFee):
                    return Response(
                        {'detail': 'Erro ao solicitar antecipação'},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                withdrawal = Withdrawal(
                    user=user,
                    amount=serializer.validated_data.get('amount'),
                    amountReceived=totalLessFee,
                    type=serializer.validated_data.get('type') or 'default',
                    fee=totalFee,
                    pixKey=user.pixKey,
                )
                withdrawal.save()

                # Update the extract with the withdrawal details
                extract.update_details(transaction_object=withdrawal)

                # If withdrawal
                if withdrawal.type == 'default':
                    settings = Settings.get_or_cache('settings')
                    # Check sales card > pix
                    amounts = Payment.objects.filter(splits__user=withdrawal.user).aggregate(
                        pix_amount=Sum(
                            Case(
                                When(paymentMethod='pix', then='amount'),
                                default=Value(0),
                                output_field=DecimalField()
                            )
                        ),
                        credit_card_amount=Sum(
                            Case(
                                When(paymentMethod='credit_card', then='amount'),
                                default=Value(0),
                                output_field=DecimalField()
                            )
                        )
                    )

                    credit_card_more_than_pix = (amounts['credit_card_amount'] or 0) > (amounts['pix_amount'] or 0)

                    if settings.autoWithdrawal:
                        sum_total_withdrawal = Withdrawal.objects.filter(user=withdrawal.user, status='approved', createdAt__date=timezone.localdate()).aggregate(total=Sum('amount'))
                        total = sum_total_withdrawal.get('total') or 0
                        total += withdrawal.amount
                        if (
                            (total <= settings.autoWithdrawalValue and not credit_card_more_than_pix)
                            or withdrawal.user.canAutoWidthdraw
                        ):
                            if withdrawal.status == 'pending':
                                if not withdrawal.pixKey:
                                    withdrawal.status = 'rejected'
                                    withdrawal.reason = 'Chave PIX não cadastrada'
                                else:
                                    try:
                                        cashout_strategy = CashoutStrategyFactory.get_strategy()
                                        withdrawal = cashout_strategy.executeCashout(withdrawal)
                                        withdrawal.status = 'approved'
                                        withdrawal.autoApproved = True
                                    except WithdrawalException as e:
                                        withdrawal.internalReason = str(e)
                                        return Response(
                                            {
                                                'detail': str(e.message),
                                                'errors': serializer.errors,
                                            },
                                            status=status.HTTP_400_BAD_REQUEST,
                                        )
                                withdrawal.save()

        else:
            return Response(
                {'detail': 'Erro ao solicitar saque', 'errors': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response({'detail': 'Saque solicitado com sucesso'}, status=status.HTTP_200_OK)


class SellerBalanceAPI(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def retrieve(self, request):
        user = request.user
        if request.user.is_superuser:
            if request.query_params.get('account_id'):
                user = get_object_or_404(User, id=request.query_params.get('account_id'))
        pendingBalance = user.getPendingBalance()
        reserveBalance = user.getReserveBalance()
        anticipationBalance = user.getAnticipationBalance()
        return Response(
            {
                'balance': user.balance,
                'pendingBalance': pendingBalance or 0.0,
                'anticipationBalance': anticipationBalance or 0.0,
                'reservedBalance': reserveBalance or 0.0,
            },
            status=status.HTTP_200_OK,
        )


class SellerInternalFee(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def retrieve(self, request):
        user = request.user
        account_id = request.query_params.get('account_id', None)

        if account_id and not account_id.isdigit():
            return Response({'detail': 'account_id invalid'}, status=status.HTTP_400_BAD_REQUEST)

        if user.is_superuser and account_id:
            user = get_object_or_404(User, id=account_id)

        return Response(user.getFee(), status=status.HTTP_200_OK)


class SellerWithdrawFee(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def retrieve(self, request):
        user = request.user
        account_id = request.query_params.get('account_id', None)

        if account_id and not account_id.isdigit():
            return Response({'detail': 'account_id invalid'}, status=status.HTTP_400_BAD_REQUEST)

        if user.is_superuser and account_id:
            user = get_object_or_404(User, id=account_id)

        return Response(user.getWithdrawFee(), status=status.HTTP_200_OK)


class SellerPixKey(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def create(self, request):
        request.user.pixKey = request.data.get('pixKey')
        request.user.save()

        return Response({'pixKey': request.user.pixKey}, status=status.HTTP_200_OK)

    def retrieve(self, request):
        return Response({'pixKey': request.user.pixKey}, status=status.HTTP_200_OK)


class BalanceAccountAPI(viewsets.ViewSet):
    def retrieve(self, request):
        acquirer = Acquirer.objects.filter(cashoutEnabled=True)
        if not acquirer:
            return Response({'balance': 0}, status=status.HTTP_200_OK)

        acquirer = acquirer.first()
        balance = 0
        if acquirer.acquirer_gateway == 'pixcred':
            pixcred = Pixcred(acquirer)
            balance += float(pixcred.balance().replace('.', '').replace(',', '.'))
        elif acquirer.acquirer_gateway == 'asaas':
            asaas = Asaas(acquirer)
            balance += asaas.balance()
        elif acquirer.acquirer_gateway == 'efi':
            efi = Efi(acquirer, type='pix')
            balance += float(Decimal(efi.balance()))
        elif acquirer.acquirer_gateway == 'aarin':
            aarin = Aarin(acquirer)
            balance += float(aarin.balance())
        elif acquirer.acquirer_gateway == 'sicoob':
            sicoob = Sicoob(acquirer)
            balance += float(sicoob.balance())
        elif acquirer.acquirer_gateway == 'saq':
            saq = SaQ(acquirer)
            balance += float(saq.balance())

        return Response({'balance': balance}, status=status.HTTP_200_OK)


class SellerDashboardAPI(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def retrieve(self, request):
        now = timezone.datetime.now()
        start_date = self.request.query_params.get('startDate', None)
        end_date = self.request.query_params.get('endDate', None)
        if start_date or end_date:
            start_date = timezone.datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = timezone.datetime.strptime(end_date, '%Y-%m-%d').date()

            if start_date > end_date:
                return Response(
                    {'detail': 'A data de ínicio deve ser menor ou igual à de termino.'},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if (end_date - start_date).days > 365:
                return Response(
                    {'detail': 'O intervalo de datas não pode ser maior que 365 dias.'},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            payments = request.user.payments.filter(
                createdAt__date__gte=start_date, createdAt__date__lte=end_date
            )
            withdrawals = Withdrawal.objects.filter(
                user=request.user, createdAt__date__gte=start_date, createdAt__date__lte=end_date
            )
        else:
            payments = request.user.payments.all()
            withdrawals = Withdrawal.objects.filter(user=request.user)

        paymentsCount = payments.count()

        start_date = payments.aggregate(Min('createdAt')).get('createdAt__min')
        end_date = payments.aggregate(Max('createdAt')).get('createdAt__max')

        # Average refund and chargeback
        averageRefund = (
            payments.filter(status='refunded').aggregate(Avg('amount')).get('amount__avg') or 0.0
        )
        averageChargeback = (
            payments.filter(status='chargedback').aggregate(Avg('amount')).get('amount__avg') or 0.0
        )

        # Monthly entries
        payment_entries = (
            payments.filter(status='paid')
            .annotate(month=TruncMonth('createdAt'))
            .values('month')
            .annotate(total_amount=Sum('amount'))
            .order_by('month')
        )
        withdrawal_entries = (
            withdrawals.filter(status='approved', type='antecipation')
            .annotate(month=TruncMonth('createdAt'))
            .values('month')
            .annotate(total_amount=Sum('amount'))
            .order_by('month')
        )

        monthlyEntries = list(payment_entries) + list(withdrawal_entries)
        monthlyEntries.sort(key=lambda x: x['month'])

        # Monthly exits
        payment_exits = (
            payments.filter(Q(status='refunded') | Q(status='chargedback') | Q(status='in_protest'))
            .annotate(month=TruncMonth('createdAt'))
            .values('month')
            .annotate(total_amount=Sum('amount'))
            .order_by('month')
        )
        withdrawal_exits = (
            withdrawals.filter(status='approved', type='default')
            .annotate(month=TruncMonth('createdAt'))
            .values('month')
            .annotate(total_amount=Sum('amount'))
            .order_by('month')
        )

        monthlyExits = list(payment_exits) + list(withdrawal_exits)
        monthlyExits.sort(key=lambda x: x['month'])

        dailyRevenue = []
        if start_date and end_date:
            if start_date == end_date:
                for hour in range(0, 24):
                    filtered_payments = payments.filter(
                        createdAt__date=start_date, createdAt__hour=hour
                    )
                    dailyRevenue.append(
                        {
                            'date': f'{hour:02d}:00h-{hour:02d}:59h',
                            'value': filtered_payments.aggregate(Sum('amount')).get('amount__sum')
                            or 0.0,
                        }
                    )
            # if more than 60 days show by month and append all months/days between the start and end date
            elif (end_date - start_date).days > 60:
                diff = end_date - start_date
                for month in range(int(diff.days / 30) + 1):
                    date = start_date + timezone.timedelta(days=month * 30)
                    filtered_orders = payments.filter(
                        createdAt__year=start_date.year, createdAt__month=month
                    )
                    dailyRevenue.append(
                        {
                            'date': date.strftime('%d-%m-%Y'),
                            'value': filtered_orders.aggregate(Sum('amount')).get('amount__sum')
                            or 0.0,
                        }
                    )
            else:
                for day in range((end_date - start_date).days + 1):
                    filtered_orders = payments.filter(
                        createdAt__date=(start_date + timezone.timedelta(days=day))
                    )
                    dailyRevenue.append(
                        {
                            'date': (start_date + timezone.timedelta(days=day)).strftime(
                                '%d-%m-%Y'
                            ),
                            'value': filtered_orders.aggregate(Sum('amount')).get('amount__sum')
                            or 0.0,
                        }
                    )

        return Response(
            {
                'totalSalesAmount': payments.aggregate(Sum('liquidAmount')).get('liquidAmount__sum')
                or 0,
                'paidSalesAmount': payments.filter(status='paid')
                .aggregate(amount=Sum('liquidAmount'))
                .get('amount')
                or 0,
                'averageTicket': payments.aggregate(Avg('liquidAmount')).get('liquidAmount__avg'),
                'dailyRevenue': dailyRevenue,
                'conversion': {
                    'boletoCount': payments.filter(paymentMethod='boleto', status='paid').count()
                    / payments.filter(paymentMethod='boleto').count()
                    * 100
                    if payments.filter(paymentMethod='boleto').count() > 0
                    else 0,
                    'creditCardCount': payments.filter(
                        paymentMethod='credit_card', status='paid'
                    ).count()
                    / payments.filter(paymentMethod='credit_card').count()
                    * 100
                    if payments.filter(paymentMethod='credit_card').count() > 0
                    else 0,
                    'pixCount': payments.filter(paymentMethod='pix', status='paid').count()
                    / payments.filter(paymentMethod='pix').count()
                    * 100
                    if payments.filter(paymentMethod='pix').count() > 0
                    else 0,
                    'picpayCount': payments.filter(paymentMethod='picpay', status='paid').count()
                    / payments.filter(paymentMethod='picpay').count()
                    * 100
                    if payments.filter(paymentMethod='picpay').count() > 0
                    else 0,
                    'nupayCount': payments.filter(paymentMethod='nupay', status='paid').count()
                    / payments.filter(paymentMethod='nupay').count()
                    * 100
                    if payments.filter(paymentMethod='nupay').count() > 0
                    else 0,
                    'googlepayCount': payments.filter(paymentMethod='googlepay', status='paid').count()
                    / payments.filter(paymentMethod='googlepay').count()
                    * 100
                    if payments.filter(paymentMethod='googlepay').count() > 0
                    else 0,
                    'applepayCount': payments.filter(paymentMethod='applepay', status='paid').count()
                    / payments.filter(paymentMethod='applepay').count()
                    * 100
                    if payments.filter(paymentMethod='applepay').count() > 0
                    else 0,
                    'openFinanceNubankCount': payments.filter(paymentMethod='openfinance_nubank', status='paid').count()
                    / payments.filter(paymentMethod='openfinance_nubank').count()
                    * 100
                    if payments.filter(paymentMethod='openfinance_nubank').count() > 0
                    else 0,
                    'chargebackCount': payments.filter(status='in_protest').count()
                    / paymentsCount
                    * 100
                    if paymentsCount > 0
                    else 0,
                    'refundCount': payments.filter(status='refunded').count() / paymentsCount * 100
                    if paymentsCount > 0
                    else 0,
                    'prechargebackCount': payments.filter(status='prechargeback').count()
                    / paymentsCount
                    * 100
                    if paymentsCount > 0
                    else 0,
                },
                'tracking': {
                    'waitingShipment': 0,
                    'inTransit': 0,
                    'delivered': 0,
                },
                'sales': {
                    'boletoAmount': payments.filter(paymentMethod='boleto')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'paidBoletoAmount': payments.filter(paymentMethod='boleto', status='paid')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'boletoCount': payments.filter(paymentMethod='boleto').count(),
                    'creditCardAmount': payments.filter(paymentMethod='credit_card')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'paidCreditCardAmount': payments.filter(
                        paymentMethod='credit_card', status='paid'
                    )
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'creditCardCount': payments.filter(paymentMethod='credit_card').count(),
                    'pixAmount': payments.filter(paymentMethod='pix')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'paidPixAmount': payments.filter(paymentMethod='pix', status='paid')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'pixCount': payments.filter(paymentMethod='pix').count(),
                    'picpayAmount': payments.filter(paymentMethod='picpay')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'paidPicpayAmount': payments.filter(paymentMethod='picpay', status='paid')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'picpayCount': payments.filter(paymentMethod='picpay').count(),
                    'nupayAmount': payments.filter(paymentMethod='nupay')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'paidNupayAmount': payments.filter(paymentMethod='nupay', status='paid')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'nupayCount': payments.filter(paymentMethod='nupay').count(),
                    'googlepayAmount': payments.filter(paymentMethod='googlepay')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'paidGooglepayAmount': payments.filter(paymentMethod='googlepay', status='paid')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'googlepayCount': payments.filter(paymentMethod='googlepay').count(),
                    'applepayAmount': payments.filter(paymentMethod='applepay')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'paidApplepayAmount': payments.filter(paymentMethod='applepay', status='paid')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'applepayCount': payments.filter(paymentMethod='applepay').count(),
                    'openFinanceNubankAmount': payments.filter(paymentMethod='openfinance_nubank')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'paidOpenFinanceNubankAmount': payments.filter(paymentMethod='openfinance_nubank', status='paid')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                    'openFinanceNubankCount': payments.filter(paymentMethod='openfinance_nubank').count(),
                    'chargebackAmount': payments.filter(status='in_protest')
                    .aggregate(Sum('liquidAmount'))
                    .get('liquidAmount__sum')
                    or 0,
                },
                'averageRefund': averageRefund,
                'averageChargeback': averageChargeback,
                'monthlyEntries': monthlyEntries,
                'monthlyExits': monthlyExits,
            },
            status=status.HTTP_200_OK,
        )


class SellerReserve(generics.RetrieveAPIView):
    permission_classes = [IsAuthenticated]

    def retrieve(self, request):
        return Response(request.user.getReserve(), status=status.HTTP_200_OK)


class DashboardPixAPI(generics.RetrieveAPIView):
    renderer_classes = [JSONRenderer]

    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.has_errors: bool = False
        self.validation_error: str | None = None

    def _get_dates(self):
        createdAt__gte = self.request.query_params.get('createdAt__gte')  # type: ignore
        createdAt__lte = self.request.query_params.get('createdAt__lte')  # type: ignore

        start_date = datetime.strptime(createdAt__gte, '%Y-%m-%d')
        end_date = datetime.strptime(createdAt__lte, '%Y-%m-%d')

        return (start_date, end_date)

    def _validate_params(self):
        self.validation_error = None
        self.has_errors = True

        createdAt__gte = self.request.query_params.get('createdAt__gte')  # type: ignore
        createdAt__lte = self.request.query_params.get('createdAt__lte')  # type: ignore

        if not createdAt__gte or not createdAt__lte:
            self.validation_error = "Both 'createdAt__gte' and 'createdAt__lte' are required."
            return

        try:
            start_date, end_date = self._get_dates()
        except ValueError:
            self.validation_error = "Invalid date format. Use 'YYYY-MM-DD'."
            return

        if start_date > end_date:
            self.validation_error = (
                "'createdAt__gte' must be less than or equal to 'createdAt__lte'."  # noqa
            )
            return

        self.has_errors = False

    def _validate_user(self):
        """
        The user is also required to get the data.
        @return:
        """
        if not self.request.user:
            self.validation_error = 'User required.'
            self.has_errors = True

    def _get_withdrawals(self, start_date, end_date, user: User):
        withdrawal_all_status = Q(status__in=['pending', 'approved'])

        withdrawal_approved_status = Q(status='approved')

        withdrawal_query = (
            Q(
                createdAt__gte=start_date,
                createdAt__lte=end_date,
                user=user,
            )
            & withdrawal_all_status
        )

        withdrawal = (
            Withdrawal.objects.filter(withdrawal_query)
            .values('createdAt__date')
            .annotate(
                # all
                sum_all=Coalesce(
                    Sum(
                        Case(
                            When(withdrawal_all_status, then='amountReceived'),
                            output_field=DecimalField(),
                        )
                    ),
                    Value(0.0, output_field=DecimalField()),
                ),
                count_all=Coalesce(
                    Count(
                        Case(
                            When(withdrawal_all_status, then='amountReceived'),
                            output_field=IntegerField(),
                        )
                    ),
                    Value(0, output_field=IntegerField()),
                ),
                # approved
                sum_approved=Coalesce(
                    Sum(
                        Case(
                            When(withdrawal_approved_status, then='amountReceived'),
                            output_field=DecimalField(),
                        )
                    ),
                    Value(0.0, output_field=DecimalField()),
                ),
                count_approved=Coalesce(
                    Count(
                        Case(
                            When(withdrawal_approved_status, then='amountReceived'),
                            output_field=IntegerField(),
                        )
                    ),
                    Value(0, output_field=IntegerField()),
                ),
            )
            .aggregate(
                all_sum=Sum('sum_all'),
                all_count=Sum('count_all'),
                approved_sum=Sum('sum_approved'),
                approved_count=Sum('count_approved'),
            )
        )

        return withdrawal

    def _get_payments(self, start_date, end_date, user: User):
        all_status_query = Q(status__in=['paid', 'refunded', 'chargedback'])

        paid_status_query = Q(status='paid')

        canceled_status_query = Q(status__in=['refunded', 'chargedback'])

        payment_query = (
            Q(
                createdAt__gte=start_date,
                createdAt__lte=end_date,
                paymentMethod='pix',
                user=user,
            )
            & all_status_query
        )

        payments = (
            Payment.objects.filter(payment_query)
            .values('createdAt__date')
            .annotate(
                sum_all_transactions=Coalesce(
                    Sum(Case(When(all_status_query, then='amount'), output_field=DecimalField())),
                    Value(0.0, output_field=DecimalField()),
                ),
                count_all_transactions=Coalesce(
                    Count(Case(When(all_status_query, then='amount'), output_field=IntegerField())),
                    Value(0, output_field=IntegerField()),
                ),
                sum_paid_transactions=Coalesce(
                    Sum(Case(When(paid_status_query, then='amount'), output_field=DecimalField())),
                    Value(0.0, output_field=DecimalField()),
                ),
                count_paid_transactions=Coalesce(
                    Count(
                        Case(When(paid_status_query, then='amount'), output_field=IntegerField())
                    ),
                    Value(0, output_field=IntegerField()),
                ),
                # cancelled
                sum_cancelled_transactions=Coalesce(
                    Sum(
                        Case(
                            When(canceled_status_query, then='amount'), output_field=DecimalField()
                        )
                    ),
                    Value(0.0, output_field=DecimalField()),
                ),
                count_cancelled_transactions=Coalesce(
                    Count(
                        Case(
                            When(canceled_status_query, then='amount'), output_field=IntegerField()
                        )
                    ),
                    Value(0, output_field=IntegerField()),
                ),
            )
            .aggregate(
                all_transactions_sum=Sum('sum_all_transactions'),
                all_transactions_count=Sum('count_all_transactions'),
                paid_transactions_sum=Sum('sum_paid_transactions'),
                paid_transactions_count=Sum('count_paid_transactions'),
                cancelled_transactions_sum=Sum('sum_cancelled_transactions'),
                cancelled_transactions_count=Sum('count_cancelled_transactions'),
            )
        )

        return payments

    def _get_data(self, start_date, end_date, user: User):
        """
        This method will return the data for the dashboard, based on the start and end date and the user.
        @param start_date: used to filter the createdAt greater than or equal to this date
        @param end_date: used to filter the createdAt less than or equal to this date
        @param user: the user that owns the transactions
        @return:
        """
        payment = self._get_payments(start_date, end_date, user)
        withdrawal = self._get_withdrawals(start_date, end_date, user)

        payment_all_sum = payment['all_transactions_sum'] or 0
        payment_all_count = payment['all_transactions_count'] or 0

        payment_paid_sum = payment['paid_transactions_sum'] or 0
        payment_paid_count = payment['paid_transactions_count'] or 0
        payment_paid_avg = payment_paid_sum / payment_paid_count if payment_paid_count else 0

        payment_cancelled_sum = payment['cancelled_transactions_sum'] or 0
        payment_cancelled_count = payment['cancelled_transactions_count'] or 0
        payment_cancelled_avg = round(
            payment_cancelled_sum / payment_cancelled_count if payment_cancelled_count else 0, 2
        )

        withdrawal_all_sum = withdrawal['all_sum'] or 0
        withdrawal_all_count = withdrawal['all_count'] or 0

        withdrawal_aproved_sum = withdrawal['approved_sum'] or 0
        withdrawal_aproved_count = withdrawal['approved_count'] or 0
        withdrawal_aproved_avg = round(
            withdrawal_aproved_sum / withdrawal_aproved_count if withdrawal_aproved_count else 0, 2
        )

        total_sum = payment_all_sum + withdrawal_all_sum
        total_count = payment_all_count + withdrawal_all_count
        total_avg = round(total_sum / total_count if total_count else 0, 2)

        started_avg = round(
            (payment_all_sum + withdrawal_all_sum) / (payment_all_count + withdrawal_all_count)
            if payment_all_count + withdrawal_all_count
            else 0,
            2,
        )

        total_cleared_count = payment_paid_count + withdrawal_aproved_count
        total_cleared_sum = payment_paid_sum + withdrawal_aproved_sum

        cleared_avg = round(
            total_cleared_sum / total_cleared_count if total_cleared_count else 0, 2
        )

        cleared_percent = round((total_cleared_sum / total_sum) * 100, 2) if total_sum else 0

        cancelled_percent = round((payment_cancelled_sum / total_sum) * 100, 2) if total_sum else 0

        started_percent = 100.00

        cash_in_percent = (
            round((payment_paid_sum / total_cleared_sum) * 100, 2) if total_cleared_sum else 0
        )
        cash_out_percent = (
            round((withdrawal_aproved_sum / total_cleared_sum) * 100, 2) if total_cleared_sum else 0
        )

        transactions = {
            'all': {
                'cleared': {
                    'amount': total_cleared_sum,
                    'count': total_cleared_count,
                    'avg': cleared_avg,
                    'percent': cleared_percent,
                },
                'cancelled': {
                    'amount': payment_cancelled_sum,
                    'count': payment_cancelled_count,
                    'avg': payment_cancelled_avg,
                    'percent': cancelled_percent,
                },
                'started': {
                    'amount': payment_all_sum + withdrawal_all_sum,
                    'count': payment_all_count + withdrawal_all_count,
                    'avg': started_avg,
                    'percent': started_percent,
                },
            },
            'cleared': {
                'cashin': {
                    'amount': payment_paid_sum,
                    'count': payment_paid_count,
                    'avg': payment_paid_avg,
                    'percent': cash_in_percent,
                },
                'cashout': {
                    'amount': withdrawal_aproved_sum,
                    'count': withdrawal_aproved_count,
                    'avg': withdrawal_aproved_avg,
                    'percent': cash_out_percent,
                },
            },
        }

        return {
            'totalAmount': total_sum,
            'totalCount': total_count,
            'totalAvg': total_avg,
            'transactions': transactions,
        }

    def get(self, request):
        self._validate_params()

        self._validate_user()

        if self.has_errors:
            return Response({'detail': self.validation_error}, status=status.HTTP_400_BAD_REQUEST)

        start_date, end_date = self._get_dates()
        data = self._get_data(start_date, end_date, request.user)

        return Response(data)


class DashboardAPI(generics.RetrieveAPIView):
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [IsAdminUser(), FullAccessPermission(), AccessDashboardPermission()]

        has_permissions = [
            permission
            for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def retrieve(self, request):
        now = timezone.datetime.now()
        start_date = self.request.query_params.get('startDate', None) or (
            now.date() - timezone.timedelta(days=7)
        ).strftime('%Y-%m-%d')  # type:ignore
        end_date = self.request.query_params.get('endDate', None) or now.date().strftime('%Y-%m-%d')  # type:ignore

        start_date = timezone.datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = timezone.datetime.strptime(end_date, '%Y-%m-%d').date()

        if start_date > end_date:
            return Response(
                {'detail': 'A data de ínicio deve ser menor ou igual à de termino.'},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if (end_date - start_date).days > 365:
            return Response(
                {'detail': 'O intervalo de datas não pode ser maior que 365 dias.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if start_date == end_date:
            payments = Payment.objects.filter(createdAt__date=start_date)
            withdrawals = Withdrawal.objects.filter(createdAt__date=start_date)
        elif start_date and end_date:
            payments = Payment.objects.filter(
                createdAt__date__gte=start_date, createdAt__date__lte=end_date
            )
            withdrawals = Withdrawal.objects.filter(
                createdAt__date__gte=start_date, createdAt__date__lte=end_date
            )
        else:
            payments = Payment.objects.all()
            withdrawals = Withdrawal.objects.all()
        paymentsCount = payments.count()

        if request.GET.get('user'):
            payments = payments.filter(user=request.GET.get('user'))
            withdrawals = withdrawals.filter(user=request.GET.get('user'))

        dailyRevenue = []
        if start_date and end_date:
            if start_date == end_date:
                for hour in range(0, 24):
                    filtered_payments = payments.filter(
                        createdAt__date=start_date, createdAt__hour=hour
                    )
                    dailyRevenue.append(
                        {
                            'date': f'{hour:02d}:00h-{hour:02d}:59h',
                            'pixAmount': filtered_payments.filter(
                                paymentMethod='pix', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'creditCardAmount': filtered_payments.filter(
                                paymentMethod='credit_card', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'boletoAmount': filtered_payments.filter(
                                paymentMethod='boleto', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'picpayAmount': filtered_payments.filter(
                                paymentMethod='picpay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'nupayAmount': filtered_payments.filter(
                                paymentMethod='nupay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'googlepayAmount': filtered_payments.filter(
                                paymentMethod='googlepay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'applepayAmount': filtered_payments.filter(
                                paymentMethod='applepay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'openFinanceNubankAmount': filtered_payments.filter(
                                paymentMethod='openfinance_nubank', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'chargebackAmount': filtered_payments.filter(status='chargedback')
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'refundAmount': filtered_payments.filter(status='refunded')
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                        }
                    )
            # if more than 60 days show by month and append all months/days between the start and end date
            elif (end_date - start_date).days > 60:
                diff = end_date - start_date
                for month in range(int(diff.days / 30) + 1):
                    date = start_date + timezone.timedelta(days=month * 30)
                    filtered_orders = payments.filter(paymentMethod='pix').filter(
                        createdAt__year=start_date.year, createdAt__month=month
                    )
                    dailyRevenue.append(
                        {
                            'date': date.strftime('%d-%m-%Y'),
                            'pixAmount': filtered_orders.filter(paymentMethod='pix', status='paid')
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'creditCardAmount': filtered_orders.filter(
                                paymentMethod='credit_card', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'boletoAmount': filtered_orders.filter(
                                paymentMethod='boleto', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'picpayAmount': filtered_orders.filter(
                                paymentMethod='picpay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'nupayAmount': filtered_orders.filter(
                                paymentMethod='nupay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'googlepayAmount': filtered_orders.filter(
                                paymentMethod='googlepay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'googlepayAmount': filtered_orders.filter(
                                paymentMethod='googlepay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'openFinanceNubankAmount': filtered_orders.filter(
                                paymentMethod='openfinance_nubank', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'chargebackAmount': filtered_orders.filter(status='chargedback')
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                            'refundAmount': filtered_orders.filter(status='refunded')
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0.0,
                        }
                    )
            else:
                for day in range((end_date - start_date).days + 1):
                    filtered_orders = payments.filter(
                        createdAt__date=(start_date + timezone.timedelta(days=day))
                    )
                    dailyRevenue.append(
                        {
                            'date': (start_date + timezone.timedelta(days=day)).strftime(
                                '%d-%m-%Y'
                            ),
                            'pixAmount': filtered_orders.filter(paymentMethod='pix', status='paid')
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                            'creditCardAmount': filtered_orders.filter(
                                paymentMethod='credit_card', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                            'boletoAmount': filtered_orders.filter(
                                paymentMethod='boleto', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                            'picpayAmount': filtered_orders.filter(
                                paymentMethod='picpay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                            'nupayAmount': filtered_orders.filter(
                                paymentMethod='nupay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                            'googlepayAmount': filtered_orders.filter(
                                paymentMethod='googlepay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                            'googlepayAmount': filtered_orders.filter(
                                paymentMethod='googlepay', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                            'openFinanceNubankAmount': filtered_orders.filter(
                                paymentMethod='openfinance_nubank', status='paid'
                            )
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                            'chargebackAmount': filtered_orders.filter(status='chargedback')
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                            'refundAmount': filtered_orders.filter(status='refunded')
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                        }
                    )

        installmentsProfit = []
        for i in range(1, 13):
            filtered_payments = payments.filter(installments=i)
            installmentsProfit.append(
                {
                    'installments': i,
                    'profit': filtered_payments.filter(
                        status='paid', paymentMethod='credit_card', installments=i
                    )
                    .aggregate(Sum('interest'))
                    .get('interest__sum')
                    or 0,
                }
            )
        return Response(
            {
                'totalSalesAmount': payments.aggregate(Sum('amount')).get('amount__sum') or 0,
                'paidSalesAmount': payments.filter(status='paid')
                .aggregate(Sum('amount'))
                .get('amount__sum')
                or 0,
                'averageTicket': payments.aggregate(Avg('amount')).get('amount__avg'),
                'boleto': {
                    'amount': payments.filter(paymentMethod='boleto')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'paidAmount': payments.filter(paymentMethod='boleto', status='paid')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'count': payments.filter(paymentMethod='boleto', status='paid').count(),
                    'conversion': payments.filter(paymentMethod='boleto', status='paid').count()
                    / payments.filter(paymentMethod='boleto').count()
                    * 100
                    if payments.filter(paymentMethod='boleto').count() > 0
                    else 0,
                    'revenue': payments.filter(paymentMethod='boleto', status='paid')
                    .aggregate(revenue=Sum('fee'))
                    .get('revenue')
                    or 0,
                    'fee': payments.filter(paymentMethod='boleto', status='paid')
                    .aggregate(cost=Sum('cost') + Sum('gatewayFee'))
                    .get('cost')
                    or 0,
                    'profit': payments.filter(paymentMethod='boleto', status='paid')
                    .aggregate(profit=Sum('profit'))
                    .get('profit')
                    or 0,
                },
                'pix': {
                    'amount': payments.filter(paymentMethod='pix')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'paidAmount': payments.filter(paymentMethod='pix', status='paid')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'count': payments.filter(paymentMethod='pix', status='paid').count(),
                    'conversion': payments.filter(paymentMethod='pix', status='paid').count()
                    / payments.filter(paymentMethod='pix').count()
                    * 100
                    if payments.filter(paymentMethod='pix').count() > 0
                    else 0,
                    'revenue': payments.filter(paymentMethod='pix', status='paid')
                    .aggregate(revenue=Sum('fee'))
                    .get('revenue')
                    or 0,
                    'fee': payments.filter(paymentMethod='pix', status='paid')
                    .aggregate(cost=Sum('cost') + Sum('gatewayFee'))
                    .get('cost')
                    or 0,
                    'profit': payments.filter(paymentMethod='pix', status='paid')
                    .aggregate(profit=Sum('profit'))
                    .get('profit')
                    or 0,
                },
                'creditCard': {
                    'amount': payments.filter(paymentMethod='credit_card')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'paidAmount': payments.filter(paymentMethod='credit_card', status='paid')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'count': payments.filter(paymentMethod='credit_card', status='paid').count(),
                    'conversion': payments.filter(
                        paymentMethod='credit_card', status='paid'
                    ).count()
                    / payments.filter(paymentMethod='credit_card').count()
                    * 100
                    if payments.filter(paymentMethod='credit_card').count() > 0
                    else 0,
                    'interest': payments.filter(paymentMethod='credit_card')
                    .aggregate(Sum('interest'))
                    .get('interest__sum')
                    or 0,
                    'revenue': payments.filter(paymentMethod='credit_card', status='paid')
                    .aggregate(revenue=Sum('fee') + Sum('interest'))
                    .get('revenue')
                    or 0,
                    'fee': payments.filter(paymentMethod='credit_card', status='paid')
                    .aggregate(cost=Sum('cost') + Sum('gatewayFee'))
                    .get('cost')
                    or 0,
                    'profit': payments.filter(paymentMethod='credit_card', status='paid')
                    .aggregate(profit=Sum('profit'))
                    .get('profit')
                    or 0,
                    'installmentsProfit': installmentsProfit,
                },
                'picpay': {
                    'amount': payments.filter(paymentMethod='picpay')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'paidAmount': payments.filter(paymentMethod='picpay', status='paid')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'count': payments.filter(paymentMethod='picpay', status='paid').count(),
                    'conversion': payments.filter(paymentMethod='picpay', status='paid').count()
                    / payments.filter(paymentMethod='picpay').count()
                    * 100
                    if payments.filter(paymentMethod='picpay').count() > 0
                    else 0,
                    'revenue': payments.filter(paymentMethod='picpay', status='paid')
                    .aggregate(revenue=Sum('fee'))
                    .get('revenue')
                    or 0,
                    'fee': payments.filter(paymentMethod='picpay', status='paid')
                    .aggregate(cost=Sum('cost') + Sum('gatewayFee'))
                    .get('cost')
                    or 0,
                    'profit': payments.filter(paymentMethod='picpay', status='paid')
                    .aggregate(profit=Sum('profit'))
                    .get('profit')
                    or 0,
                },
                'nupay': {
                    'amount': payments.filter(paymentMethod='nupay')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'paidAmount': payments.filter(paymentMethod='nupay', status='paid')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'count': payments.filter(paymentMethod='nupay', status='paid').count(),
                    'conversion': payments.filter(paymentMethod='nupay', status='paid').count()
                    / payments.filter(paymentMethod='nupay').count()
                    * 100
                    if payments.filter(paymentMethod='nupay').count() > 0
                    else 0,
                    'revenue': payments.filter(paymentMethod='nupay', status='paid')
                    .aggregate(revenue=Sum('fee'))
                    .get('revenue')
                    or 0,
                    'fee': payments.filter(paymentMethod='nupay', status='paid')
                    .aggregate(cost=Sum('cost') + Sum('gatewayFee'))
                    .get('cost')
                    or 0,
                    'profit': payments.filter(paymentMethod='nupay', status='paid')
                    .aggregate(profit=Sum('profit'))
                    .get('profit')
                    or 0,
                },
                'googlepay': {
                    'amount': payments.filter(paymentMethod='googlepay')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'paidAmount': payments.filter(paymentMethod='googlepay', status='paid')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'count': payments.filter(paymentMethod='googlepay', status='paid').count(),
                    'conversion': payments.filter(paymentMethod='googlepay', status='paid').count()
                    / payments.filter(paymentMethod='googlepay').count()
                    * 100
                    if payments.filter(paymentMethod='googlepay').count() > 0
                    else 0,
                    'revenue': payments.filter(paymentMethod='googlepay', status='paid')
                    .aggregate(revenue=Sum('fee'))
                    .get('revenue')
                    or 0,
                    'fee': payments.filter(paymentMethod='googlepay', status='paid')
                    .aggregate(cost=Sum('cost') + Sum('gatewayFee'))
                    .get('cost')
                    or 0,
                    'profit': payments.filter(paymentMethod='googlepay', status='paid')
                    .aggregate(profit=Sum('profit'))
                    .get('profit')
                    or 0,
                },
                'applepay': {
                    'amount': payments.filter(paymentMethod='applepay')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'paidAmount': payments.filter(paymentMethod='applepay', status='paid')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'count': payments.filter(paymentMethod='applepay', status='paid').count(),
                    'conversion': payments.filter(paymentMethod='applepay', status='paid').count()
                    / payments.filter(paymentMethod='applepay').count()
                    * 100
                    if payments.filter(paymentMethod='applepay').count() > 0
                    else 0,
                    'revenue': payments.filter(paymentMethod='applepay', status='paid')
                    .aggregate(revenue=Sum('fee'))
                    .get('revenue')
                    or 0,
                    'fee': payments.filter(paymentMethod='applepay', status='paid')
                    .aggregate(cost=Sum('cost') + Sum('gatewayFee'))
                    .get('cost')
                    or 0,
                    'profit': payments.filter(paymentMethod='applepay', status='paid')
                    .aggregate(profit=Sum('profit'))
                    .get('profit')
                    or 0,
                },
                'openFinanceNubank': {
                    'amount': payments.filter(paymentMethod='openfinance_nubank')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'paidAmount': payments.filter(paymentMethod='openfinance_nubank', status='paid')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'count': payments.filter(paymentMethod='openfinance_nubank', status='paid').count(),
                    'conversion': payments.filter(paymentMethod='openfinance_nubank', status='paid').count()
                    / payments.filter(paymentMethod='openfinance_nubank').count()
                    * 100
                    if payments.filter(paymentMethod='openfinance_nubank').count() > 0
                    else 0,
                    'revenue': payments.filter(paymentMethod='openfinance_nubank', status='paid')
                    .aggregate(revenue=Sum('fee'))
                    .get('revenue')
                    or 0,
                    'fee': payments.filter(paymentMethod='openfinance_nubank', status='paid')
                    .aggregate(cost=Sum('cost') + Sum('gatewayFee'))
                    .get('cost')
                    or 0,
                    'profit': payments.filter(paymentMethod='openfinance_nubank', status='paid')
                    .aggregate(profit=Sum('profit'))
                    .get('profit')
                    or 0,
                },
                'chargeback': {
                    'amount': payments.filter(status='chargedback')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'count': payments.filter(status='chargedback').count(),
                    'percentage': payments.filter(status='chargedback').count()
                    / paymentsCount
                    * 100
                    if paymentsCount > 0
                    else 0,
                },
                'gateway': {
                    'fee': payments.aggregate(Sum('gatewayFee')).get('gatewayFee__sum') or 0,
                    'percentage': float(settings.GATEWAY_FEE_PERCENTAGE),
                },
                'approvedCompanies': User.objects.filter(
                    approvedDate__date__gte=start_date, approvedDate__date__lte=end_date
                ).count(),
                'summary': [
                    {
                        'label': 'Antecipação',
                        'volume': withdrawals.filter(status='approved', type='antecipation')
                        .aggregate(Sum('amount'))
                        .get('amount__sum')
                        or 0,
                        'revenue': withdrawals.filter(status='approved', type='antecipation')
                        .aggregate(Sum('fee'))
                        .get('fee__sum')
                        or 0,
                        'fee': 0,
                        'profit': withdrawals.filter(status='approved', type='antecipation')
                        .aggregate(Sum('fee'))
                        .get('fee__sum')
                        or 0,
                    },
                    {
                        'label': 'Saque',
                        'volume': withdrawals.filter(status='approved', type='default')
                        .aggregate(Sum('amount'))
                        .get('amount__sum')
                        or 0,
                        'revenue': withdrawals.filter(status='approved', type='default')
                        .aggregate(Sum('fee'))
                        .get('fee__sum')
                        or 0,
                        'fee': 0,
                        'profit': withdrawals.filter(status='approved', type='default')
                        .aggregate(Sum('fee'))
                        .get('fee__sum')
                        or 0,
                    },
                    {
                        'label': 'Multa',
                        'volume': payments.filter(status='chargedback')
                        .aggregate(Sum('amount'))
                        .get('amount__sum')
                        or 0,
                        'revenue': payments.filter(status='chargedback')
                        .aggregate(Sum('fine'))
                        .get('fine__sum')
                        or 0,
                        'fee': 0,
                        'profit': payments.filter(status='paid')
                        .aggregate(Sum('fine'))
                        .get('fine__sum')
                        or 0,
                    },
                ],
                'total': {
                    'summary': [
                        {
                            'label': 'Receita',
                            'value': payments.filter(status='paid')
                            .aggregate(Sum('amount'))
                            .get('amount__sum')
                            or 0,
                        },
                        {
                            'label': 'Taxa de gateway',
                            'value': payments.filter(status='paid')
                            .aggregate(Sum('gatewayFee'))
                            .get('gatewayFee__sum')
                            or 0,
                        },
                    ],
                    'volume': payments.filter(status='paid')
                    .aggregate(Sum('amount'))
                    .get('amount__sum')
                    or 0,
                    'revenue': payments.filter(status='paid').aggregate(Sum('fee')).get('fee__sum')
                    or 0,
                    'fee': payments.filter(status='paid')
                    .aggregate(Sum('gatewayFee'))
                    .get('gatewayFee__sum')
                    or 0,
                    'profit': payments.filter(status='paid')
                    .aggregate(Sum('profit'))
                    .get('profit__sum')
                    or 0,
                },
                'dailyRevenue': dailyRevenue,
            },
            status=status.HTTP_200_OK,
        )


class SettingsViewSet(mixins.UpdateModelMixin, mixins.RetrieveModelMixin, viewsets.GenericViewSet):
    serializer_class = SettingsSerializer

    def get_object(self):
        return Settings.load()


class PendingBalanceByDateBase(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    serializer_class = PendingBalanceByDateSerializer
    queryset = PendingBalance.objects.all()
    renderer_classes = [JSONRenderer]

    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.has_errors: bool = False
        self.validation_error: str | None = None

    def _get_dates(self):
        createdAt__gte = self.request.query_params.get('createdAt__gte')
        createdAt__lte = self.request.query_params.get('createdAt__lte')

        start_date = datetime.strptime(createdAt__gte, '%Y-%m-%d')
        end_date = datetime.strptime(createdAt__lte, '%Y-%m-%d')

        if (self.request.query_params.get('hourly') == 'true'):
            end_date = end_date.replace(hour=23, minute=59, second=59)

        start_date = make_aware(start_date)
        end_date = make_aware(end_date)

        return (start_date, end_date)

    def _get_user_id(self) -> int | None:
        user_id = self.request.query_params.get('user')
        return int(user_id) if user_id else self.request.user.id  # type: ignore

    def _validate_params(self):
        self.validation_error = None
        self.has_errors = True
        user_id = self.request.query_params.get('user')

        if user_id:
            try:
                user_id = int(user_id)
            except ValueError:
                self.validation_error = 'Parameter user must be an integer.'
                return

        createdAt__gte = self.request.query_params.get('createdAt__gte')
        createdAt__lte = self.request.query_params.get('createdAt__lte')

        if not createdAt__gte or not createdAt__lte:
            self.validation_error = "Both 'createdAt__gte' and 'createdAt__lte' are required."
            return

        try:
            start_date, end_date = self._get_dates()
        except ValueError:
            self.validation_error = "Invalid date format. Use 'YYYY-MM-DD'."
            return

        if start_date > end_date:
            self.validation_error = (
                "'createdAt__gte' must be less than or equal to 'createdAt__lte'."
            )
            return

        self.has_errors = False

    def get_queryset(self):
        queryset = super().get_queryset()
        start_date, end_date = self._get_dates()
        user_id = self._get_user_id()

        query = Q(
            user_id=user_id, releaseDate__gte=start_date, releaseDate__lte=end_date, amount__gt=0.0
        )

        aggregations = {
            'paidAmount': Coalesce(
                Sum(Case(When(status='paid', then='amount'), output_field=DecimalField())),
                Value(0.0, output_field=DecimalField()),
            ),
            'pending': Coalesce(
                Sum(
                    Case(
                        When(isReserve=False, status='pending', then='amount'),
                        output_field=DecimalField(),
                    )
                ),
                Value(0.0, output_field=DecimalField()),
            ),
            'pendingReserve': Coalesce(
                Sum(
                    Case(
                        When(isReserve=True, status='pending', then='amount'),
                        output_field=DecimalField(),
                    )
                ),
                Value(0.0, output_field=DecimalField()),
            ),
            'refunded': Coalesce(
                Sum(
                    Case(
                        When(status='refunded', then='amount'),
                        output_field=DecimalField(),
                    )
                ),
                Value(0.0, output_field=DecimalField()),
            ),
            'chargeback': Coalesce(
                Sum(
                    Case(
                        When(status='chargedback', payment__paymentMethod__exact='pix', then=Value(0)),
                        When(status='chargedback', then='amount'),
                        output_field=DecimalField(),
                    )
                ),
                Value(0.0, output_field=DecimalField()),
            ),
            'med': Coalesce(
                Sum(
                    Case(
                        When(status='chargedback', payment__paymentMethod='pix', then='amount'),
                        output_field=DecimalField(),
                    )
                ),
                Value(0.0, output_field=DecimalField()),
            ),
            'totalAmount': Coalesce(
                Sum(
                    Case(
                        When(status__in=['paid', 'pending'], then='amount'),
                        output_field=DecimalField(),
                    )
                ),
                Value(0.0, output_field=DecimalField()),
            ),
            'totalWithoutRefunds': Coalesce(
                Sum(
                    Case(
                        When(status__in=['paid', 'pending', 'refunded', 'chargedback'], then='amount'),
                        output_field=DecimalField(),
                    )
                ),
                Value(0.0, output_field=DecimalField()),
            ),
        }

        same_day = start_date.date() == end_date.date()

        if same_day and self.request.query_params.get('hourly') == 'true':
            queryset = (
                queryset.filter(query)
                .annotate(hour=ExtractHour('releaseDate'))
                .values('hour')
                .annotate(**aggregations)
                .order_by('hour')
            )

            result_dict = {item['hour']: item for item in queryset}
            complete_result = []

            for hour in range(24):
                if hour in result_dict:
                    complete_result.append(result_dict[hour])
                else:
                    complete_result.append({
                        'hour': hour,
                        'paidAmount': Decimal('0.0'),
                        'pending': Decimal('0.0'),
                        'pendingReserve': Decimal('0.0'),
                        'refunded': Decimal('0.0'),
                        'chargeback': Decimal('0.0'),
                        'med': Decimal('0.0'),
                        'totalAmount': Decimal('0.0'),
                        'totalWithoutRefunds': Decimal('0.0'),
                    })

            return complete_result

        queryset = (
            queryset.filter(query)
            .values('releaseDate__date')
            .annotate(**aggregations)
            .order_by('releaseDate__date')
        )

        return queryset

    def get_renderers(self):
        """
        Sobrescreve o método para escolher o renderizador dinamicamente,
        dependendo da condição de validação.
        """
        self._validate_params()
        if self.has_errors:
            return [JSONRenderer()]
        return super().get_renderers()

    def get(self, request, *args, **kwargs):
        self._validate_params()
        if self.has_errors:
            return Response({'error': self.validation_error}, status=400)
        return super().get(request, *args, **kwargs)

class PendingBalanceByDate(PendingBalanceByDateBase):
    renderer_classes = [JSONRenderer]
    pagination_class = None

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        return response


class PendingBalanceByDateExportCsvView(PendingBalanceByDateBase):
    renderer_classes = [CSVRenderer, JSONRenderer]
    pagination_class = None

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)

        if response.status_code == 200:
            response['Content-Disposition'] = (
                'attachment; filename="pending_balance_by_date_report.csv"'
            )

        return response


class PendingBalanceByDateExportXlsxView(PendingBalanceByDateBase):
    renderer_classes = [XLSXRenderer, JSONRenderer]
    pagination_class = None

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)

        if response.status_code == 200:
            response['Content-Disposition'] = (
                'attachment; filename="pending_balance_by_date_report.xlsx"'
            )

        return response

class PendingBalanceProductView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    queryset = PendingBalance.objects.all()
    renderer_classes = [JSONRenderer]
    pagination_class = None

    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.has_errors: bool = False
        self.validation_error: str | None = None

    def _get_dates(self):
        createdAt__gte = self.request.query_params.get('createdAt__gte')
        createdAt__lte = self.request.query_params.get('createdAt__lte')

        start_date = datetime.strptime(createdAt__gte, '%Y-%m-%d')
        end_date = datetime.strptime(createdAt__lte, '%Y-%m-%d')

        # end date is 23:59:59
        if start_date.date() == end_date.date():
            end_date = end_date.replace(hour=23, minute=59, second=59)

        return (start_date, end_date)

    def _get_user_id(self) -> int | None:
        user_id = None
        if self.request.user.is_superuser:
            user_id = self.request.query_params.get('user')
        return int(user_id) if user_id else self.request.user.id  # type: ignore

    def _validate_params(self):
        self.validation_error = None
        self.has_errors = True
        user_id = self.request.query_params.get('user')

        if user_id:
            try:
                user_id = int(user_id)
            except ValueError:
                self.validation_error = 'Parameter user must be an integer.'
                return

        createdAt__gte = self.request.query_params.get('createdAt__gte')
        createdAt__lte = self.request.query_params.get('createdAt__lte')

        if not createdAt__gte or not createdAt__lte:
            self.validation_error = "Both 'createdAt__gte' and 'createdAt__lte' are required."
            return

        try:
            start_date, end_date = self._get_dates()
        except ValueError:
            self.validation_error = "Invalid date format. Use 'YYYY-MM-DD'."
            return

        if start_date > end_date:
            self.validation_error = (
                "'createdAt__gte' must be less than or equal to 'createdAt__lte'."
            )
            return

        self.has_errors = False

    def get(self, request, *args, **kwargs):
        queryset = super().get_queryset()
        start_date, end_date = self._get_dates()
        user_id = self._get_user_id()

        query = Q(
            user_id=user_id, releaseDate__gte=start_date, releaseDate__lte=end_date, amount__gt=0.0
        )

        queryset = queryset.filter(query)

        result = []
        for pending_balance in queryset:
            payment = pending_balance.payment
            items = payment.items
            for item in items:
                unit = Decimal(item.get('unitPrice', 0))
                quantity = item.get('quantity', 1)
                value = unit * quantity

                result.append({
                    'product': item.get('title'),
                    'value': value,
                    'date': pending_balance.releaseDate,
                })

        return Response(result)

class PendingBalanceListView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    queryset = PendingBalance.objects.all()
    serializer_class = PendingBalanceSerializer
    filterset_class = PendingBalanceAdminFilter
    filter_backends = [DjangoFilterBackend]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.is_superuser:
            if self.request.query_params.get('account_id'):
                queryset = queryset.filter(user__id=self.request.query_params.get('account_id'))
        queryset = queryset.order_by('releaseDate')
        return queryset

    def get(self, request, *args, **kwargs):
        if request.query_params.get('pagination') == 'false':
            self.pagination_class = None  # Disable pagination
            return Response(self.get_serializer(self.get_queryset(), many=True).data)
        return super().get(request, *args, **kwargs)


class PendingBalanceExportCSVView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    queryset = PendingBalance.objects.all()
    serializer_class = PendingBalanceSerializer
    renderer_classes = [CSVRenderer]
    pagination_class = None

    def get_queryset(self):
        queryset = super().get_queryset()
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(releaseDate__range=[start_date, end_date])
        if self.request.user.is_superuser:
            if self.request.query_params.get('account_id'):
                queryset = queryset.filter(user__id=self.request.query_params.get('account_id'))
        return queryset

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        response['Content-Disposition'] = 'attachment; filename="pending_balance_report.csv"'
        return response


class PendingBalanceExportXLSXView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    queryset = PendingBalance.objects.all()
    serializer_class = PendingBalanceSerializer
    renderer_classes = [XLSXRenderer]
    pagination_class = None

    def get_queryset(self):
        queryset = super().get_queryset()
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(releaseDate__range=[start_date, end_date])
        if self.request.user.is_superuser:
            if self.request.query_params.get('account_id'):
                queryset = queryset.filter(user__id=self.request.query_params.get('account_id'))
        return queryset

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        response['Content-Disposition'] = 'attachment; filename="pending_balance_report.xlsx"'
        return response


class FinancialOverviewAPI(views.APIView):
    permission_classes = [IsAuthenticated, FullAccessPermission]

    def get(self, request):
        user = None
        user_id = request.query_params.get('user_id')
        if user_id:
            try:
                validate_integer(user_id)
                user_id = int(user_id)
                user = User.objects.get(pk=user_id)
            except ValidationError:
                return Response({'error': 'Invalid user_id.'}, status=status.HTTP_400_BAD_REQUEST)
            except User.DoesNotExist:
                return Response({'error': 'User not found.'}, status=status.HTTP_404_NOT_FOUND)

        pending_balances = PendingBalance.objects.filter(status='pending')
        blocked_users = User.objects.filter(status='blocked')
        nonblocked_users = User.objects.exclude(status='blocked')
        if user:
            pending_balances = pending_balances.filter(user=user)

        blocked_balance = blocked_users.aggregate(Sum('balance'))['balance__sum'] or 0
        reserved_balance = (
            pending_balances.filter(isReserve=True).aggregate(Sum('amount'))['amount__sum'] or 0
        )
        pending_balance = (
            pending_balances.filter(
                isReserve=False, antecipationRelease__gte=timezone.now()
            ).aggregate(Sum('amount'))['amount__sum']
            or 0
        )
        available_for_antecipation = (
            pending_balances.filter(antecipationRelease__lte=timezone.now()).aggregate(
                Sum('amount')
            )['amount__sum']
            or 0
        )

        available_balance = (
            user.balance
            if user
            else nonblocked_users.aggregate(Sum('balance'))['balance__sum'] or 0
        )

        response_data = {
            'total': available_balance
            + pending_balance
            + available_for_antecipation
            + reserved_balance,
            'available_balance': available_balance,
            'pending_balance': pending_balance,
            'blocked_balance': blocked_balance,
            'available_for_antecipation': available_for_antecipation,
            'reserved_balance': reserved_balance,
        }

        return Response(response_data, status=status.HTTP_200_OK)


class DashboardAPIV2(generics.RetrieveAPIView):
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [FullAccessPermission(), AccessDashboardPermission()]

        has_permissions = [
            permission
            for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def retrieve_dashboard_data_from_bigquery(
        self, start_date: date, end_date: date, user_id: str, acquirer_id: str
    ):
        from bigquery.dashboard import client

        payment_related_data: dict = client.get_dashboard_data(
            start_date, end_date, user_id, acquirer_id
        )

        # Get some additional data from the postgres database
        approved_companies = self.get_approved_companies_count(start_date, end_date)
        company = self.get_company_data(start_date, end_date)

        return Response(
            {**payment_related_data, 'approvedCompanies': approved_companies, 'company': company}
        )

    def retrieve(self, request, *args, **kwargs):
        start_date, end_date = self.get_date_range()
        if start_date > end_date:
            return Response(
                {'detail': 'A data de início deve ser menor ou igual à de término.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if waffle.switch_is_active('get_dashboard_data_from_bigquery'):
            return self.retrieve_dashboard_data_from_bigquery(
                start_date=start_date,
                end_date=end_date,
                user_id=request.GET.get('user'),
                acquirer_id=request.GET.get('acquirer'),
            )

        payments, withdrawals = self.get_filtered_data(
            start_date=start_date,
            end_date=end_date,
            user_id=request.GET.get('user'),
            acquirer=request.GET.get('acquirer'),
        )
        chargebacks = self.get_chargeback_and_med_payments(
            start_date=start_date,
            end_date=end_date,
            user_id=request.GET.get('user'),
            acquirer=request.GET.get('acquirer'),
        )

        revenue_data = self.calculate_revenue(payments)
        payment_details = self.calculate_payment_details(payments)
        chargeback_data = self.chargeback_amount(chargebacks, payment_details)
        refund_data = self.refund_amount(payments)
        gateway_fee_data = self.calculate_gateway_fee(payments)
        approved_companies = self.get_approved_companies_count(start_date, end_date)
        company = self.get_company_data(start_date, end_date)
        summary = self.calculate_summary(withdrawals, payments)
        total = self.calculate_total(payments)
        daily_revenue = self.calculate_daily_revenue(start_date, end_date, payments)

        data = {
            **revenue_data,
            **payment_details,
            **chargeback_data,
            **refund_data,
            **gateway_fee_data,
            'company': company,
            'approvedCompanies': approved_companies,
            'summary': summary,
            'total': total,
            'dailyRevenue': daily_revenue,
        }

        return Response(data, status=status.HTTP_200_OK)

    def get_date_range(self) -> tuple[date, date]:
        # Retrieves and formats the start and end dates from query params
        now = timezone.now()
        end_date = self.request.query_params.get('endDate', (now).strftime('%Y-%m-%d'))
        end_date: date = timezone.datetime.strptime(end_date, '%Y-%m-%d').date()
        start_date = self.request.query_params.get('startDate')
        if not start_date:
            payment = Payment.objects.filter(createdAt__date__lte=end_date).order_by('id').first()
            if payment:
                start_date = payment.createdAt.strftime('%Y-%m-%d')
            else:
                start_date = (now - timedelta(days=30)).strftime('%Y-%m-%d')
        start_date: date = timezone.datetime.strptime(start_date, '%Y-%m-%d').date()
        return start_date, end_date

    @staticmethod
    def get_payment_filters(start_date, end_date, user_id=None, acquirer=None) -> dict:
        filters = {}

        if user_id:
            filters['user'] = user_id
        if acquirer:
            filters['acquirer'] = acquirer

        if start_date == end_date:
            filters['createdAt__date'] = start_date
        else:
            filters['createdAt__date__gte'] = start_date
            filters['createdAt__date__lte'] = end_date

        return filters

    @staticmethod
    def get_withdrawal_filters(start_date: date, end_date: date):
        if start_date == end_date:
            filters = {'createdAt__date': start_date}
        else:
            filters = {'createdAt__date__gte': start_date, 'createdAt__date__lte': end_date}
        return filters

    @staticmethod
    def create_filters_for_med_and_chageback_payments(
        start_date: date, end_date: date, acquirer=None, user_id=None
    ) -> dict:
        filters = {'status__in': ['chargedback', 'MED']}

        if start_date == end_date:
            filters['chargedbackAt__date'] = start_date
        else:
            filters.update(
                {'chargedbackAt__date__gte': start_date, 'chargedbackAt__date__lte': end_date}
            )

        if acquirer:
            filters['acquirer'] = acquirer

        if user_id:
            filters['user'] = user_id

        return filters

    @staticmethod
    def get_payment_specific_fields() -> set[str]:
        return {
            'amount',
            'status',
            'paymentMethod',
            'fee',
            'cost',
            'profit',
            'interest',
            'installments',
            'createdAt',
            'chargedbackAt',
        }

    def get_filtered_data(
        self, start_date: date, end_date: date, user_id=None, acquirer=None
    ) -> tuple[QuerySet, QuerySet]:
        payment_filters: dict = self.get_payment_filters(start_date, end_date, user_id, acquirer)
        withdrawal_filters: dict = self.get_withdrawal_filters(start_date, end_date)

        payment_only_fields: set = self.get_payment_specific_fields()
        withdrawal_only_fields = {'amount', 'status', 'fee', 'paidAt'}

        return (
            Payment.objects.filter(**payment_filters)
            .only(*payment_only_fields)
            .exclude(status='chargedback'),
            Withdrawal.objects.filter(**withdrawal_filters).only(*withdrawal_only_fields),
        )

    def get_company_data(self, start_date, end_date):
        # Define the statuses you want to include
        statuses = ['pending', 'pre_approved', 'gateway_pending', 'approved']

        # Perform the query
        queryset = (
            User.objects.filter(
                Q(
                    date_joined__date__gte=start_date,
                    date_joined__date__lte=end_date,
                    status__in=['pending', 'pre_approved', 'gateway_pending'],
                )
                | Q(
                    approvedDate__date__gte=start_date,
                    approvedDate__date__lte=end_date,
                    status__in=['approved'],
                )
            )
            .values('status')
            .annotate(count=Count('status'))
        )

        # Initialize the dictionary with zero counts for all statuses
        status_counts = {status: 0 for status in statuses}

        # Update the dictionary with counts from the query
        for entry in queryset:
            status_counts[entry['status']] = entry['count']

        # Convert the dictionary to a list of dictionaries for consistency with the original query result
        result = [{'status': status, 'count': count} for status, count in status_counts.items()]

        return result

    def get_chargeback_and_med_payments(
        self, start_date: date, end_date: date, user_id=None, acquirer=None
    ):
        payment_filters = self.create_filters_for_med_and_chageback_payments(start_date, end_date, acquirer, user_id)
        payment_only_fields = self.get_payment_specific_fields()

        return Payment.objects.filter(**payment_filters).only(*payment_only_fields)

    def calculate_revenue(self, payments):
        # Calculates total and paid sales amount, and average ticket from payments
        total_sales_amount = payments.aggregate(Sum('amount')).get('amount__sum') or 0
        paidSales = payments.filter(status='paid')
        paid_sales_amount = paidSales.aggregate(Sum('amount')).get('amount__sum') or 0
        average_ticket = paidSales.aggregate(Avg('amount')).get('amount__avg') or 0

        return {
            'totalSalesAmount': total_sales_amount,
            'paidSalesAmount': paid_sales_amount,
            'averageTicket': average_ticket,
        }

    def calculate_payment_details(self, payments):
        payment_methods = ['pix', 'credit_card', 'boleto', 'picpay', 'nupay', 'googlepay', 'applepay', 'openfinance_nubank']
        method_map = {
            'boleto': 'boleto',
            'pix': 'pix',
            'credit_card': 'creditCard',
            'picpay': 'picpay',
            'nupay': 'nupay',
            'googlepay': 'googlepay',
            'applepay': 'applepay',
            'openfinance_nubank': 'openFinanceNubank'
        }
        result = {}

        for method in payment_methods:
            aggregation = payments.filter(paymentMethod=method).aggregate(
                totalAmount=Sum('amount'),
                paymentPaidAmount=Sum('amount', filter=Q(status='paid')),
                count=Count('id', filter=Q(status='paid')),
                revenue=(
                    Sum('fee', filter=Q(status='paid'))
                    + Sum('interest', filter=Q(status='paid'))
                    + Sum('fine', filter=Q(status='paid'))
                ),
                paymentFee=(Sum('cost', filter=Q(status='paid')) or 0),
                paymentProfit=Sum('profit', filter=Q(status='paid')),
                totalCount=Count('id'),
            )

            # Replace None values with 0
            aggregation = {k: 0 if v is None else v for k, v in aggregation.items()}

            conversion_rate = (
                (aggregation['count'] / aggregation['totalCount'] * 100)
                if aggregation['totalCount'] > 0
                else 0
            )

            method_details = {
                'amount': aggregation['totalAmount'],
                'paidAmount': aggregation['paymentPaidAmount'],
                'count': aggregation['count'],
                'conversion': conversion_rate,
                'revenue': aggregation['revenue'],
                'fee': aggregation['paymentFee'],
                'profit': aggregation['paymentProfit'],
            }

            result_key = method_map[method]
            result[result_key] = method_details
            if method == 'credit_card':
                interest_sum = (
                    payments.filter(paymentMethod='credit_card')
                    .aggregate(Sum('interest'))
                    .get('interest__sum')
                    or 0
                )
                result[result_key]['interest'] = interest_sum
                installments_profit = self.calculate_installments_profit(payments)
                result[result_key]['installmentsProfit'] = installments_profit

        return result

    def calculate_installments_profit(self, payments):
        filtered_payments = payments.filter(paymentMethod='credit_card', status='paid')

        installments_data = (
            filtered_payments.values('installments')
            .annotate(profit=Sum('interest'))
            .order_by('installments')
        )

        # Convert the data to a dictionary for easy access
        installments_profit_dict = {
            data['installments']: data['profit'] for data in installments_data
        }

        # Profit for each installment
        installments_profit = []
        for i in range(1, 13):
            profit = installments_profit_dict.get(i, 0)
            installments_profit.append({'installments': i, 'profit': profit})

        return installments_profit

    @staticmethod
    def get_chargeback_data(payments: object, payment_method: str, paid_count: int) -> dict:
        filtered_payments = payments.filter(paymentMethod=payment_method)
        total_payments = filtered_payments.count() + paid_count

        chargeback_data = filtered_payments.filter(status='chargedback').aggregate(
            totalAmount=Sum('amount'), totalCount=Count('id')
        )

        amount = chargeback_data.get('totalAmount', 0)
        count = chargeback_data.get('totalCount', 0)
        percentage = (count / total_payments * 100) if total_payments > 0 else 0

        return {
            'amount': amount,
            'count': count,
            'percentage': percentage,
        }

    @staticmethod
    def get_med_data(payments: object, payment_method: str, paid_count: int) -> dict:
        filtered_payments = payments.filter(paymentMethod=payment_method)
        total_payments = filtered_payments.count() + paid_count

        chargeback_data = filtered_payments.filter(status='MED').aggregate(
            totalAmount=Sum('amount'), totalCount=Count('id')
        )

        amount = chargeback_data.get('totalAmount', 0)
        count = chargeback_data.get('totalCount', 0)
        percentage = (count / total_payments * 100) if total_payments > 0 else 0

        return {
            'amount': amount,
            'count': count,
            'percentage': percentage,
        }

    @staticmethod
    def get_refund_data(payments: object, payment_count: int) -> dict:
        refund_data = payments.filter(status='refunded').aggregate(
            totalRefundAmount=Sum('amount'), totalRefundCount=Count('id')
        )

        return {
            'amount': refund_data.get('totalRefundAmount') or 0,
            'count': refund_data.get('totalRefundCount') or 0,
            'percentage': (refund_data.get('totalRefundCount') / payment_count * 100)
            if payment_count > 0
            else 0,
        }

    def chargeback_amount(self, payments, payment_details):
        paid_credit_card_count = payment_details['creditCard']['count']
        paid_pix_count = payment_details['pix']['count']
        return {
            'chargeback': self.get_chargeback_data(
                payments=payments, payment_method='credit_card', paid_count=paid_credit_card_count
            ),
            'med': self.get_med_data(
                payments=payments, payment_method='pix', paid_count=paid_pix_count
            ),
        }

    def refund_amount(self, payments):
        return {'refund': self.get_refund_data(payments, payments.count())}

    def calculate_gateway_fee(self, payments):
        # Calculates total gateway fee from payments
        gateway_fee_data = payments.aggregate(totalGatewayFee=Sum('gatewayFee'))

        gateway_fee_result = {
            'gateway': {
                'fee': gateway_fee_data.get('totalGatewayFee') or 0,
                'percentage': float(settings.GATEWAY_FEE_PERCENTAGE),
            }
        }

        return gateway_fee_result

    def get_approved_companies_count(self, start_date, end_date):
        # Retrieves the number of approved companies within the date range
        if self.request.query_params.get('start_date'):
            approved_companies = User.objects.filter(
                approvedDate__date__gte=start_date, approvedDate__date__lte=end_date
            ).count()
        else:
            approved_companies = User.objects.filter(status='approved').count()
        return approved_companies

    def calculate_summary(self, withdrawals, payments):
        # Calculates total volume, revenue, fee, and profit from withdrawals and payments
        antecipation_data = withdrawals.filter(status='approved', type='antecipation').aggregate(
            volume=Sum('amount'), revenue=Sum('fee'), profit=Sum('fee')
        )

        default_withdrawal_data = withdrawals.filter(status='approved', type='default').aggregate(
            volume=Sum('amount'), revenue=Sum('fee'), profit=Sum('fee')
        )

        # Aggregates chargeback and paid fine data
        chargeback_data = payments.filter(status='chargedback').aggregate(
            volume=Sum('amount'), revenue=Sum('fine')
        )
        paid_fine_data = payments.filter(status='paid').aggregate(profit=Sum('fine'))

        # Calculates profit from each type of withdrawal
        summary = [
            {
                'label': 'Antecipação',
                'volume': antecipation_data['volume'] or 0,
                'revenue': antecipation_data['revenue'] or 0,
                'fee': 0,
                'profit': antecipation_data['profit'] or 0,
            },
            {
                'label': 'Saque',
                'volume': default_withdrawal_data['volume'] or 0,
                'revenue': default_withdrawal_data['revenue'] or 0,
                'fee': 0,
                'profit': default_withdrawal_data['profit'] or 0,
            },
            {
                'label': 'Multa',
                'volume': chargeback_data['volume'] or 0,
                'revenue': chargeback_data['revenue'] or 0,
                'fee': 0,
                'profit': paid_fine_data['profit'] or 0,
            },
        ]

        return summary

    def calculate_total(self, payments):
        # Calculates total volume, revenue, fee, and profit from withdrawals and payments
        paid_payments_aggregation = payments.filter(status='paid').aggregate(
            totalAmount=Sum('amount'),
            totalGatewayFee=Sum('gatewayFee'),
            totalFee=Sum('fee'),
            totalProfit=Sum('profit'),
        )

        total_result = {
            'summary': [
                {'label': 'Receita', 'value': paid_payments_aggregation['totalAmount'] or 0},
                {
                    'label': 'Taxa de gateway',
                    'value': paid_payments_aggregation['totalGatewayFee'] or 0,
                },
            ],
            'volume': paid_payments_aggregation['totalAmount'] or 0,
            'revenue': paid_payments_aggregation['totalFee'] or 0,
            'fee': paid_payments_aggregation['totalGatewayFee'] or 0,
            'profit': paid_payments_aggregation['totalProfit'] or 0,
        }

        return total_result

    def calculate_daily_revenue(self, start_date, end_date, payments):
        daily_revenue = []
        timezone = pytz.timezone('America/Sao_Paulo')

        # Check if start_date and end_date are datetime objects
        if isinstance(start_date, datetime):
            start_date = make_aware(start_date, timezone)
        else:
            start_date = make_aware(datetime.combine(start_date, datetime.min.time()), timezone)

        if isinstance(end_date, datetime):
            end_date = make_aware(end_date, timezone)
        else:
            end_date = make_aware(datetime.combine(end_date, datetime.min.time()), timezone)

        if start_date == end_date:
            current_time = timezone.localize(datetime.now())
            last_hour = current_time.hour if start_date.date() == current_time.date() else 23
            for hour in range(24):
                if hour > last_hour:
                    break  # Avoid querying future hours
                hourly_payments = payments.filter(
                    createdAt__date=start_date.date(), createdAt__hour=hour
                )
                daily_revenue.append(
                    self.aggregate_payment_data(hourly_payments, f'{hour:02d}:00h-{hour:02d}:59h')
                )

        elif (end_date - start_date).days > 60:
            num_months = (
                (end_date.year - start_date.year) * 12 + end_date.month - start_date.month + 1
            )
            for month_offset in range(num_months):
                month_date = start_date + relativedelta(months=month_offset)
                monthly_payments = payments.filter(
                    createdAt__year=month_date.year, createdAt__month=month_date.month
                )
                daily_revenue.append(
                    self.aggregate_payment_data(monthly_payments, month_date.strftime('%Y-%m'))
                )

        else:
            num_days = (end_date - start_date).days + 1
            for day_offset in range(num_days):
                day_date = start_date + timedelta(days=day_offset)
                daily_payments = payments.filter(createdAt__date=day_date.date())
                daily_revenue.append(
                    self.aggregate_payment_data(daily_payments, day_date.strftime('%d-%m-%Y'))
                )

        return daily_revenue

    def aggregate_payment_data(self, payments, date_label):
        results = payments.aggregate(
            pixAmount=Sum('amount', filter=Q(paymentMethod='pix', status='paid')),
            creditCardAmount=Sum('amount', filter=Q(paymentMethod='credit_card', status='paid')),
            boletoAmount=Sum('amount', filter=Q(paymentMethod='boleto', status='paid')),
            picpayAmount=Sum('amount', filter=Q(paymentMethod='picpay', status='paid')),
            nupayAmount=Sum('amount', filter=Q(paymentMethod='nupay', status='paid')),
            googlepayAmount=Sum('amount', filter=Q(paymentMethod='googlepay', status='paid')),
            applepayAmount=Sum('amount', filter=Q(paymentMethod='applepay', status='paid')),
            openFinanceNubankAmount=Sum('amount', filter=Q(paymentMethod='openfinance_nubank', status='paid')),
            chargebackAmount=Sum('amount', filter=Q(status='chargedback')),
            refundAmount=Sum('amount', filter=Q(status='refunded')),
        )

        for key in results:
            results[key] = results[key] or 0

        return {
            'date': date_label,
            'pixAmount': results['pixAmount'],
            'creditCardAmount': results['creditCardAmount'],
            'boletoAmount': results['boletoAmount'],
            'picpayAmount': results['picpayAmount'],
            'nupayAmount': results['nupayAmount'],
            'googlepayAmount': results['googlepayAmount'],
            'applepayAmount': results['applepayAmount'],
            'openFinanceNubankAmount': results['openFinanceNubankAmount'],
            'chargebackAmount': results['chargebackAmount'],
            'refundAmount': results['refundAmount'],
        }


class ExtractView(FlexFieldsMixin, ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = ExtractSerializer
    filterset_class = ExtractFilter
    filter_backends = (DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter)
    search_fields = ['id', 'description', 'pending__id', 'payment__id', 'withdraw__id']
    ordering_fields = ['createdAt', 'amount', 'transaction_type']
    permit_list_expands = ['payment', 'withdraw', 'pending']
    pagination.PageNumberPagination.page_size = 100

    def get_queryset(self):
        queryset = Extract.objects.all()
        if self.request.user.is_superuser:
            if self.request.query_params.get('account_id'):
                queryset = queryset.filter(user__id=self.request.query_params.get('account_id'))
        if is_expanded(self.request, 'payment'):
            queryset = queryset.select_related('payment')
        if is_expanded(self.request, 'withdraw'):
            queryset = queryset.select_related('withdraw')
        if is_expanded(self.request, 'pending'):
            queryset = queryset.select_related('pending')

        return queryset

    def list(self, request, *args, **kwargs):
        start_date = request.query_params.get('createdAt__gte')
        end_date = request.query_params.get('createdAt__lte')

        if not start_date or not end_date:
            return Response(
                {'detail': 'Os campos createdAt__gte e createdAt__lte são obrigatórios.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return Response(
                {'detail': 'Formato de data inválido. Use YYYY-MM-DD.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if (end_date - start_date).days > 90:
            return Response(
                {'detail': 'O intervalo de datas não pode ser maior que 90 dias.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class ExtractEmailView(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        filterset = ExtractFilter(request.data, queryset=Extract.objects.filter(user=request.user))
        if not filterset.is_valid():
            return Response(
                {'detail': 'Erro de validação', 'errors': filterset.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        start_date = request.data.get('createdAt__gte')
        end_date = request.data.get('createdAt__lte')

        if not start_date or not end_date:
            return Response(
                {'detail': 'Os campos createdAt__gte e createdAt__lte são obrigatórios.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return Response(
                {'detail': 'Formato de data inválido. Use YYYY-MM-DD.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if (end_date - start_date).days > 90:
            return Response(
                {'detail': 'O intervalo de datas não pode ser maior que 90 dias.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        queryset = filterset.qs

        send_extract_email.delay(request.user.email, list(queryset.values()))

        return Response({'detail': 'O extrato será enviado por email.'}, status=status.HTTP_200_OK)


class ExtractExportCSVView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    serializer_class = ExtractSerializer
    renderer_classes = [CSVRenderer]
    pagination_class = None
    filterset_class = ExtractFilter
    filter_backends = (DjangoFilterBackend,)

    def get_queryset(self):
        return Extract.objects.filter(user=self.request.user)

    def get(self, request, *args, **kwargs):
        filterset = self.filterset_class(request.query_params, queryset=self.get_queryset())
        if not filterset.is_valid():
            return Response(
                {'detail': 'Erro de validação', 'errors': filterset.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        start_date = request.query_params.get('createdAt__gte')
        end_date = request.query_params.get('createdAt__lte')

        if not start_date or not end_date:
            return Response(
                {'detail': 'Os campos createdAt__gte e createdAt__lte são obrigatórios.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return Response(
                {'detail': 'Formato de data inválido. Use YYYY-MM-DD.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if (end_date - start_date).days > 90:
            return Response(
                {'detail': 'O intervalo de datas não pode ser maior que 90 dias.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        current_time = now().strftime('%Y%m%d%H%M%S')
        filename = f'extract_report_{current_time}.csv'

        response = super().get(request, *args, **kwargs)
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response


class ExtractExportXLSXView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    serializer_class = ExtractSerializer
    renderer_classes = [XLSXRenderer]
    pagination_class = None
    filterset_class = ExtractFilter
    filter_backends = (DjangoFilterBackend,)

    def get_queryset(self):
        return Extract.objects.filter(user=self.request.user)

    def get(self, request, *args, **kwargs):
        filterset = self.filterset_class(request.query_params, queryset=self.get_queryset())
        if not filterset.is_valid():
            return Response(
                {'detail': 'Erro de validação', 'errors': filterset.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        start_date = request.query_params.get('createdAt__gte')
        end_date = request.query_params.get('createdAt__lte')

        if not start_date or not end_date:
            return Response(
                {'detail': 'Os campos createdAt__gte e createdAt__lte são obrigatórios.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return Response(
                {'detail': 'Formato de data inválido. Use YYYY-MM-DD.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if (end_date - start_date).days > 90:
            return Response(
                {'detail': 'O intervalo de datas não pode ser maior que 90 dias.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        current_time = now().strftime('%Y%m%d%H%M%S')
        filename = f'extract_report_{current_time}.xlsx'

        response = super().get(request, *args, **kwargs)
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response


@api_view(['GET', 'POST', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def removed_endpoint(request):
    return Response(
        {
            'message': 'the target resource is no longer available at the origin server and this condition is likely to be permanent.'
        },
        status=status.HTTP_410_GONE,
    )


class PlanFeeView(views.APIView):
    permission_classes = [PlanFeePermission]
    all_plans_cache_id = 'all_plan_fee'

    def get(self, request, pk=None):
        if pk:
            try:
                cache_id = f'{pk}_plan_fee'
                plan_fee = cache.get(cache_id)
                if plan_fee is None:
                    plan_fee = PlanFee.objects.get(pk=pk)
                    cache.set(cache_id, plan_fee, 60)

                serializer = PlanFeeSerializer(plan_fee)
                return Response(serializer.data)

            except PlanFee.DoesNotExist:
                return Response({'error': 'Not found'}, status=status.HTTP_404_NOT_FOUND)

        all_plan_fees = cache.get(self.all_plans_cache_id)
        if all_plan_fees is None:
            all_plan_fees = PlanFee.objects.filter(enabled=True)
            cache.set(self.all_plans_cache_id, all_plan_fees, 60)

        serializer = PlanFeeSerializer(all_plan_fees, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = PlanFeeSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            cache.delete(self.all_plans_cache_id)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk):
        try:
            instance = PlanFee.objects.get(pk=pk)
        except PlanFee.DoesNotExist:
            return Response({'error': 'Not found'}, status=status.HTTP_404_NOT_FOUND)
        serializer = PlanFeeSerializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            cache.delete(f'{pk}_plan_fee')
            cache.delete(self.all_plans_cache_id)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        try:
            instance = PlanFee.objects.get(pk=pk)
        except PlanFee.DoesNotExist:
            return Response({'error': 'Not found'}, status=status.HTTP_404_NOT_FOUND)
        instance.delete()
        cache.delete(f'{pk}_plan_fee')
        cache.delete(self.all_plans_cache_id)
        return Response(status=status.HTTP_204_NO_CONTENT)
