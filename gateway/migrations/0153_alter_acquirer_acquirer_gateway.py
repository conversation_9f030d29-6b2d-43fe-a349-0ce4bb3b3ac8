# Generated by Django 5.1.4 on 2025-04-18 14:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0152_merge_20250404_1819"),
    ]

    operations = [
        migrations.AlterField(
            model_name="acquirer",
            name="acquirer_gateway",
            field=models.CharField(
                choices=[
                    ("hopypay", "Hopypay"),
                    ("payup", "Payup"),
                    ("owempay", "Owempay"),
                    ("pixcred", "Pixcred"),
                    ("transfeera", "Transfeera"),
                    ("saqja", "Saqjá"),
                    ("asaas", "<PERSON>aas"),
                    ("arkama", "Arkama"),
                    ("abmex", "Abmex"),
                    ("firebanking", "Firebanking"),
                    ("efi", "Efi"),
                    ("aarin", "Aarin"),
                    ("openpix", "OpenPix"),
                    ("pagarme", "Pagar.me"),
                    ("primepag", "PrimePag"),
                    ("simplegateway", "Simplegateway"),
                    ("sumup", "Sumup"),
                    ("celcoin", "Celcoin"),
                    ("nextpayments", "NextPayments"),
                    ("pagbank", "PagBank"),
                    ("picpay", "PicPay"),
                    ("unlimit", "Unlimit"),
                    ("voluti", "Voluti"),
                    ("wemaxpay", "WemaxPay"),
                    ("bloobank", "Bloobank"),
                    ("zendry", "Zendry"),
                    ("sicoob", "Sicoob"),
                    ("cielo", "Cielo"),
                    ("belvo", "Belvo"),
                    ("saq", "SaQ"),
                    ("mercadopago", "Mercado Pago"),
                ],
                default="hopypay",
                max_length=255,
            ),
        ),
    ]
