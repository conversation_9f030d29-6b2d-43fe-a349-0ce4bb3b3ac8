# Generated by Django 5.1.4 on 2025-03-22 13:30

from django.db import migrations


def add_new_acquirer(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    Acquirer.objects.create(
        name='Belvo',
        acquirer_gateway='belvo',
        fields=[
            {
                "id":"auth_username",
                "name":"Auth - Username"
            },
            {
                "id":"auth_password",
                "name":"Auth - Password"
            },
            {
                "id":"holder_type",
                "name":"Beneficário - Tipo de Pessoa" # INDIVIDUAL/BUSINESS
            },
            {
                "id":"holder_identifier_type",
                "name":"Beneficiário - Tipo de Documento" # CPF/CNPJ
            },
            {
                "id":"holder_identifier",
                "name":"Beneficiário - Número do Documento"
            },
            {
                "id":"holder_first_name",
                "name":"Beneficiário - Primeiro Nome"
            },
            {
                "id":"holder_last_name",
                "name":"Beneficiário - Último Nome"
            },
            {
                "id":"bank_account_type",
                "name":"Beneficiário - Tipo de Conta" # CHECKINGS (Conta Corrente) / SAVINGS (Conta Poupança) / SALARY (Conta Salário) / PAYMENTS (Conta de Pagamento)
            },
            {
                "id":"bank_account_agency",
                "name":"Beneficiário - Agência"
            },
            {
                "id":"bank_account_number",
                "name":"Beneficiário - Número da Conta"
            },
            {
                "id":"bank_account_institution",
                "name":"Beneficiário - Instituição Bancária (UUID)"
            },
            {
                "id":"callback_url",
                "name":"Callback URL"
            },
        ],
        customUrl='https://api.belvo.com',
        enabled=True,
        usesExternalToken=False,
        allowed_methods=[
            'pix',
        ]
    )


def remove_new_acquirer(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    Acquirer.objects.filter(name='Belvo').delete()


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0147_acquirer_antecipationreleaseopenfinancenubank_and_more"),
    ]

    operations = [migrations.RunPython(add_new_acquirer, remove_new_acquirer)]
