# Generated by Django 5.1.2 on 2025-03-20 02:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0146_auto_20250228_0854"),
    ]

    operations = [
        migrations.AddField(
            model_name="acquirer",
            name="antecipationReleaseOpenFinanceNubank",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="acquirer",
            name="costOpenFinanceNubankFixed",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="acquirer",
            name="costOpenFinanceNubankPercentage",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="acquirer",
            name="openFinanceNubankEnabled",
            field=models.BooleanField(
                db_index=True, default=False, help_text="Open Finance Nubank habilitado"
            ),
        ),
        migrations.AddField(
            model_name="acquirer",
            name="openFinanceNubankFixed",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="acquirer",
            name="openFinanceNubankPercentage",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="acquirer",
            name="openFinanceNubankPriority",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="acquirer",
            name="openFinanceNubankRelease",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="acquirer",
            name="openFinanceNubankReleaseReserve",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="acquirer",
            name="openFinanceNubankReserve",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="defaultfee",
            name="antecipationReleaseOpenFinanceNubank",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="defaultfee",
            name="openFinanceNubankFixed",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="defaultfee",
            name="openFinanceNubankPercentage",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="defaultfee",
            name="openFinanceNubankRelease",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="defaultfee",
            name="openFinanceNubankReleaseReserve",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="defaultfee",
            name="openFinanceNubankReserve",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="fee",
            name="antecipationReleaseOpenFinanceNubank",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="fee",
            name="openFinanceNubankFixed",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="fee",
            name="openFinanceNubankPercentage",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="fee",
            name="openFinanceNubankRelease",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="fee",
            name="openFinanceNubankReleaseReserve",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="fee",
            name="openFinanceNubankReserve",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="planfee",
            name="antecipationReleaseOpenFinanceNubank",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="planfee",
            name="openFinanceNubankFixed",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="planfee",
            name="openFinanceNubankPercentage",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="planfee",
            name="openFinanceNubankRelease",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="planfee",
            name="openFinanceNubankReleaseReserve",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="planfee",
            name="openFinanceNubankReserve",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AlterField(
            model_name="acquirer",
            name="acquirer_gateway",
            field=models.CharField(
                choices=[
                    ("hopypay", "Hopypay"),
                    ("payup", "Payup"),
                    ("owempay", "Owempay"),
                    ("pixcred", "Pixcred"),
                    ("transfeera", "Transfeera"),
                    ("saqja", "Saqjá"),
                    ("asaas", "Asaas"),
                    ("arkama", "Arkama"),
                    ("abmex", "Abmex"),
                    ("firebanking", "Firebanking"),
                    ("efi", "Efi"),
                    ("aarin", "Aarin"),
                    ("openpix", "OpenPix"),
                    ("pagarme", "Pagar.me"),
                    ("primepag", "PrimePag"),
                    ("simplegateway", "Simplegateway"),
                    ("sumup", "Sumup"),
                    ("celcoin", "Celcoin"),
                    ("nextpayments", "NextPayments"),
                    ("pagbank", "PagBank"),
                    ("picpay", "PicPay"),
                    ("unlimit", "Unlimit"),
                    ("voluti", "Voluti"),
                    ("wemaxpay", "WemaxPay"),
                    ("bloobank", "Bloobank"),
                    ("zendry", "Zendry"),
                    ("sicoob", "Sicoob"),
                    ("cielo", "Cielo"),
                    ("belvo", "Belvo"),
                    ("saq", "SaQ"),
                ],
                default="hopypay",
                max_length=255,
            ),
        ),
    ]
