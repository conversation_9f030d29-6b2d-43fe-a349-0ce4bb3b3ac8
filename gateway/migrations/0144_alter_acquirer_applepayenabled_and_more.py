# Generated by Django 5.1.5 on 2025-02-05 22:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0143_auto_20250124_1131"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="acquirer",
            name="applepayEnabled",
            field=models.BooleanField(
                db_index=True, default=False, help_text="ApplePay habilitado"
            ),
        ),
        migrations.AlterField(
            model_name="acquirer",
            name="cashoutEnabled",
            field=models.BooleanField(
                db_index=True, default=False, help_text="Saque habilitado"
            ),
        ),
        migrations.AlterField(
            model_name="acquirer",
            name="creditCardEnabled",
            field=models.BooleanField(
                db_index=True, default=False, help_text="Cartão de crédito habilitado"
            ),
        ),
        migrations.AlterField(
            model_name="acquirer",
            name="googlepayEnabled",
            field=models.<PERSON>oleanField(
                db_index=True, default=False, help_text="GooglePay habilitado"
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="acquirer",
            name="nupayEnabled",
            field=models.BooleanField(
                db_index=True, default=False, help_text="NuPay habilitado"
            ),
        ),
        migrations.AlterField(
            model_name="acquirer",
            name="onboardEnabled",
            field=models.BooleanField(
                db_index=True, default=False, help_text="Onboard habilitado"
            ),
        ),
        migrations.AlterField(
            model_name="acquirer",
            name="picpayEnabled",
            field=models.BooleanField(
                db_index=True, default=False, help_text="PicPay habilitado"
            ),
        ),
        migrations.AlterField(
            model_name="acquirer",
            name="pixEnabled",
            field=models.BooleanField(
                db_index=True, default=False, help_text="Pix habilitado"
            ),
        ),
        migrations.AlterField(
            model_name="acquirer",
            name="ticketEnabled",
            field=models.BooleanField(
                db_index=True, default=False, help_text="Boleto habilitado"
            ),
        ),
    ]
