# Generated by Django 5.1.4 on 2025-02-28 11:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0144_alter_acquirer_applepayenabled_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="acquirer",
            name="acquirer_gateway",
            field=models.CharField(
                choices=[
                    ("hopypay", "Hopypay"),
                    ("payup", "Payup"),
                    ("owempay", "Owempay"),
                    ("pixcred", "Pixcred"),
                    ("transfeera", "Transfeera"),
                    ("saqja", "Saqj<PERSON>"),
                    ("asaas", "<PERSON>aas"),
                    ("arkama", "Arkama"),
                    ("abmex", "Abmex"),
                    ("firebanking", "Firebanking"),
                    ("efi", "Efi"),
                    ("aarin", "Aarin"),
                    ("openpix", "OpenPix"),
                    ("pagarme", "Pagar.me"),
                    ("primepag", "PrimePag"),
                    ("simplegateway", "Simplegateway"),
                    ("sumup", "Sumup"),
                    ("celcoin", "Celcoin"),
                    ("nextpayments", "NextPayments"),
                    ("pagbank", "PagBank"),
                    ("picpay", "PicPay"),
                    ("unlimit", "Unlimit"),
                    ("voluti", "Voluti"),
                    ("wemaxpay", "WemaxPay"),
                    ("bloobank", "Bloobank"),
                    ("zendry", "Zendry"),
                    ("sicoob", "Sicoob"),
                    ("cielo", "Cielo"),
                    ("saq", "SaQ"),
                ],
                default="hopypay",
                max_length=255,
            ),
        ),
    ]
