# Generated by Django 5.1.4 on 2025-03-22 14:32

from django.db import migrations


def update_acquirer_allowed_methods(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    Acquirer.objects.filter(name='Belvo').update(allowed_methods=['openfinance_nubank'])


def revert_acquirer_allowed_methods(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    Acquirer.objects.filter(name='Belvo').update(allowed_methods=['pix'])


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0148_auto_20250322_1030"),
    ]

    operations = [migrations.RunPython(update_acquirer_allowed_methods, revert_acquirer_allowed_methods)]
