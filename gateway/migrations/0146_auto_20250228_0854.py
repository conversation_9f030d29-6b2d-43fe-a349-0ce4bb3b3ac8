# Generated by Django 5.1.4 on 2025-02-28 11:54

from django.db import migrations


def add_new_acquirer(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    Acquirer.objects.create(
        name='SaQ',
        acquirer_gateway='saq',
        fields=[
            {
                "id":"client_id",
                "name":"Client ID"
            },
            {
                "id":"client_secret",
                "name":"Client Secret"
            },
            {
                "id":"secret_key",
                "name":"HMAC - Secret Key"
            },
            {
                "id":"bank_agency",
                "name":"Dados Bancários - Agência"
            },
            {
                "id":"bank_account_number",
                "name":"Dados Bancários - Número da Conta"
            },
        ],
        customUrl='https://api.saq.digital',
        enabled=True,
        usesExternalToken=False,
        allowed_methods=[
            'pix',
            'cashout',
        ]
    )


def remove_new_acquirer(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    Acquirer.objects.filter(name='SaQ').delete()


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0145_alter_acquirer_acquirer_gateway"),
    ]

    operations = [migrations.RunPython(add_new_acquirer, remove_new_acquirer)]
