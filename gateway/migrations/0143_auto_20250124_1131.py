# Generated by Django 5.1.4 on 2025-01-24 14:31

from django.db import migrations


def update_acquirer_methods(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    acquirer = Acquirer.objects.filter(name='<PERSON><PERSON><PERSON>').first()
    if acquirer:
        acquirer.allowed_methods = ['pix', 'googlepay', 'applepay']
        acquirer.save()


def revert_acquirer_methods(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    acquirer = Acquirer.objects.filter(name='<PERSON>ielo').first()
    if acquirer:
        acquirer.allowed_methods = [
            'credit_card',
            'pix',
            'boleto',
            'googlepay',
            'applepay',
        ]
        acquirer.save()


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0142_acquirer_applepay10x_acquirer_applepay11x_and_more"),
    ]

    operations = [
        migrations.RunPython(update_acquirer_methods, revert_acquirer_methods),
    ]
