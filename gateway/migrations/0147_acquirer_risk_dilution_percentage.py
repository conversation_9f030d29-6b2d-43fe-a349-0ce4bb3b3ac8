# Generated by Django 5.1.4 on 2025-03-19 00:39

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0146_auto_20250228_0854"),
    ]

    operations = [
        migrations.AddField(
            model_name="acquirer",
            name="risk_dilution_percentage",
            field=models.FloatField(
                default=0,
                help_text="Porcentagem de transações que serão processadas pela adquirente, no fluxo de diluição de risco.",
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(1.0),
                ],
            ),
        ),
    ]
