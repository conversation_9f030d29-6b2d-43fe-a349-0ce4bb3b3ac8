# Generated by Django 5.1.4 on 2025-01-22 00:53

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0140_merge_20250121_1124"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="acquirer",
            name="acquirer_gateway",
            field=models.CharField(
                choices=[
                    ("hopypay", "Hopypay"),
                    ("payup", "Payup"),
                    ("owempay", "Owempay"),
                    ("pixcred", "Pixcred"),
                    ("transfeera", "Transfeera"),
                    ("saqja", "Saqj<PERSON>"),
                    ("asaas", "Asaas"),
                    ("arkama", "Arkama"),
                    ("abmex", "Abmex"),
                    ("firebanking", "Firebanking"),
                    ("efi", "Efi"),
                    ("aarin", "<PERSON><PERSON><PERSON>"),
                    ("openpix", "OpenPix"),
                    ("pagarme", "Pagar.me"),
                    ("primepag", "PrimePag"),
                    ("simplegateway", "Simplegateway"),
                    ("sumup", "Sumup"),
                    ("celcoin", "Celcoin"),
                    ("nextpayments", "NextPayments"),
                    ("pagbank", "PagBank"),
                    ("picpay", "PicPay"),
                    ("unlimit", "Unlimit"),
                    ("voluti", "Voluti"),
                    ("wemaxpay", "WemaxPay"),
                    ("bloobank", "Bloobank"),
                    ("zendry", "Zendry"),
                    ("sicoob", "Sicoob"),
                    ("cielo", "Cielo"),
                ],
                default="hopypay",
                max_length=255,
            ),
        ),
        migrations.AddIndex(
            model_name="fee",
            index=models.Index(fields=["user"], name="gateway_fee_user_id_ff244e_idx"),
        ),
    ]
