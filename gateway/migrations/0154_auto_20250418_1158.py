# Generated by Django 5.1.4 on 2025-04-18 14:58

from django.db import migrations


def add_new_acquirer(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    Acquirer.objects.create(
        name='Mercado Pago',
        acquirer_gateway='mercadopago',
        fields=[
            {
                "id":"access_token",
                "name":"Access Token"
            }
        ],
        customUrl='https://api.mercadopago.com',
        enabled=True,
        usesExternalToken=False,
        allowed_methods=[
            'pix',
            'credit_card',
            'boleto'
        ]
    )


def remove_new_acquirer(apps, schema_editor):
    Acquirer = apps.get_model('gateway', 'Acquirer')
    Acquirer.objects.filter(name='Mercado Pago').delete()


class Migration(migrations.Migration):

    dependencies = [
        ("gateway", "0153_alter_acquirer_acquirer_gateway"),
    ]

    operations = [migrations.RunPython(add_new_acquirer, remove_new_acquirer)]
