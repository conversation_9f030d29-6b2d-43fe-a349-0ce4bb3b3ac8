# Generated by Django 4.2.7 on 2023-11-27 01:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.Char<PERSON>ield(max_length=255)),
                ('docNumber', models.Char<PERSON>ield(help_text='CPF ou CNPJ', max_length=255)),
                ('docType', models.CharField(choices=[('cpf', 'CPF'), ('cnpj', 'CNPJ')], default='cpf', help_text='Tipo de documento (cpf ou cnpj)', max_length=255)),
                ('ip', models.<PERSON>r<PERSON><PERSON>(blank=True, help_text='IP do usuário', max_length=255, null=True)),
                ('fingerprint', models.CharField(help_text='Fingerprint do browser do usuário', max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='Card',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('holderName', models.CharField(max_length=255)),
                ('number', models.CharField(max_length=255)),
                ('expirationDate', models.CharField(max_length=255)),
                ('cvv', models.CharField(max_length=255)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customer')),
            ],
        ),
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('street', models.CharField(max_length=255)),
                ('number', models.CharField(max_length=255)),
                ('complement', models.CharField(blank=True, max_length=255, null=True)),
                ('neighborhood', models.CharField(max_length=255)),
                ('city', models.CharField(max_length=255)),
                ('state', models.CharField(max_length=255)),
                ('zipcode', models.CharField(max_length=255)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customer')),
            ],
        ),
    ]
