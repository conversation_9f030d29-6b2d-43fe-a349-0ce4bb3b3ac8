# Generated by Django 4.2.7 on 2023-12-21 20:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0003_customer_user'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='card',
            name='cvv',
        ),
        migrations.RemoveField(
            model_name='card',
            name='number',
        ),
        migrations.RemoveField(
            model_name='customer',
            name='fingerprint',
        ),
        migrations.AddField(
            model_name='card',
            name='brand',
            field=models.CharField(default=1, max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='card',
            name='lastDigits',
            field=models.CharField(default=1, max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='card',
            name='token',
            field=models.CharField(default=111, max_length=255),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='Fingerprint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fingerprint', models.CharField(max_length=255)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customer')),
            ],
        ),
    ]
