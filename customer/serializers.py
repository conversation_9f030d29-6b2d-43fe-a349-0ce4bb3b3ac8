from rest_framework import serializers

from user.serializers import UserCustomerSerializer
from .models import Customer, Card, Address

class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        exclude = ['user']

class CustomerAdminSerializer(serializers.ModelSerializer):
    user = UserCustomerSerializer(read_only=True)
    class Meta:
        model = Customer
        fields = '__all__'

class CustomerSellerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = ['id', 'name', 'email', 'phone' , 'docNumber', 'docType']

class CustomerCheckoutSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = ['id','name', 'email', 'phone' , 'docNumber', 'docType']

class CustomerCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        exclude = ['user']

class CardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Card
        exclude = ['encryptedNumber']
        extra_kwargs = {
            'number': {'write_only': True},
            'cvv': {'write_only': True},
            'expMonth': {'write_only': True},
            'expYear': {'write_only': True},
        }

class CardCheckoutSerializer(serializers.ModelSerializer):
    class Meta:
        model = Card
        fields = ['lastDigits', 'holderName', 'brand', 'token']

class CreateCardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Card
        exclude = ['customer']

class AddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = '__all__'

class CustomerReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = ('name', 'email', 'phone')
