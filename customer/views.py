from rest_framework import viewsets

from rest_framework.permissions import IsA<PERSON>enticated, IsAdminUser
from user.permissions import DenyAllPermission, FullAccessPermission, ManageCustomersPermission, ManageReportsPermission

from .models import Customer
from .serializers import CustomerSerializer, CustomerAdminSerializer, CustomerReportSerializer
from rest_framework.response import Response
from rest_framework import filters, generics
from django.shortcuts import get_object_or_404
from payment.serializers import PaymentCustomerSerializer
from drf_excel.renderers import XLSXRenderer
from rest_framework_csv.renderers import CSVRenderer


class SellerCustomersListView(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = CustomerSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    ordering_fields = ['id', 'name']
    search_fields = ['id', 'name', 'email', 'phone', 'docNumber']

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            FullAccessPermission(),
            ManageCustomersPermission(),
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        return Customer.objects.filter(user=self.request.user)

    def get_object(self):
        if self.request.user.is_superuser or self.request.user.is_staff:
            return get_object_or_404(Customer, id=self.kwargs['pk'])
        else:
            return get_object_or_404(Customer, id=self.kwargs['pk'], user=self.request.user)

    def retrieve(self, request, pk=None):
        customer = self.get_object()
        serializer = CustomerSerializer(customer)
        averageOrder = customer.get_average_order()
        totalSpent = customer.get_total_spent()
        totalInOrders = customer.get_total_in_orders()
        paidOrderCount = customer.get_paid_order_count()
        totalOrdersCount = customer.get_total_orders_count()

        transactions = customer.get_transactions(3)
        transactions = PaymentCustomerSerializer(transactions, many=True)
        return Response({
            'averageOrder': averageOrder,
            'totalSpent': totalSpent,
            'totalInOrders': totalInOrders,
            'paidOrderCount': paidOrderCount,
            'totalOrdersCount': totalOrdersCount,
            'transactions': transactions.data,
            'customer': serializer.data
        })


class AdminCustomersViewset(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    serializer_class = CustomerAdminSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    ordering_fields = ['id', 'docType']
    search_fields = ['id', 'name', 'email', 'phone', 'docType', 'docNumber', 'createdAt']

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            IsAdminUser(),
            FullAccessPermission(),
            ManageCustomersPermission(),
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        return Customer.objects.all()

    def get_object(self):
        return get_object_or_404(Customer, id=self.kwargs['pk'])

    def retrieve(self, request, pk=None):
        customer = self.get_object()
        serializer = CustomerAdminSerializer(customer)
        averageOrder = customer.get_average_order()
        totalSpent = customer.get_total_spent()
        totalInOrders = customer.get_total_in_orders()
        paidOrderCount = customer.get_paid_order_count()
        totalOrdersCount = customer.get_total_orders_count()

        transactions = customer.get_transactions(3)
        transactions = PaymentCustomerSerializer(transactions, many=True)
        return Response({
            'averageOrder': averageOrder,
            'totalSpent': totalSpent,
            'totalInOrders': totalInOrders,
            'paidOrderCount': paidOrderCount,
            'totalOrdersCount': totalOrdersCount,
            'transactions': transactions.data,
            'customer': serializer.data
        })


class CustomerReportCSV(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    queryset = Customer.objects.all()
    serializer_class = CustomerReportSerializer
    renderer_classes = [CSVRenderer]
    pagination_class = None

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            IsAdminUser(),
            FullAccessPermission(),
            ManageReportsPermission(),
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        response['Content-Disposition'] = 'attachment; filename="customer_report.csv"'
        return response


class CustomerReportXLSX(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    queryset = Customer.objects.all()
    serializer_class = CustomerReportSerializer
    renderer_classes = [XLSXRenderer]
    pagination_class = None

    def get_permissions(self):
        permissions = super().get_permissions()

        necessary_permissions = [
            IsAdminUser(),
            FullAccessPermission(),
            ManageReportsPermission(),
        ]

        has_permissions = [
            permission for permission in necessary_permissions
            if permission.has_permission(self.request, self)
        ]

        if has_permissions:
            permissions.extend(has_permissions)
        else:
            permissions = [DenyAllPermission()]

        return permissions

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        response['Content-Disposition'] = 'attachment; filename="customer_report.xlsx"'
        return response
