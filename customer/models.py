from django.db import models
from encrypted_field import Encrypted<PERSON>ield
from payment.models import Payment
import uuid

class Customer(models.Model):
    class Meta:
        permissions = [
            ("manage_customers", "Can manage customers"),
        ]

    name = models.CharField(max_length=255)
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    email = models.EmailField()
    phone = models.CharField(max_length=255)
    docNumber = models.CharField(max_length=255, help_text='CPF ou CNPJ')
    docType = models.CharField(
        max_length=255,
        choices=(
            ('cpf', 'CPF'),
            ('cnpj', 'CNPJ')
        ),
        default='cpf',
        help_text='Tipo de documento (cpf ou cnpj)'
    )
    birthDate = models.DateField(null=True, blank=True, help_text='Data de nascimento')
    ip = models.CharField(max_length=255, null=True, blank=True, help_text='IP do usuário')
    blockedTimes = models.IntegerField(default=0, help_text='Quantidade de vezes bloqueado')
    blocked_until = models.DateTimeField(null=True, blank=True, help_text='Bloqueio até')
    createdAt = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    def get_average_order(self):
        return Payment.objects.filter(customer=self).aggregate(models.Avg('amount')).get('amount__avg') or 0

    def get_total_spent(self):
        return Payment.objects.filter(status='paid', customer=self).aggregate(models.Sum('amount')).get('amount__sum') or 0

    def get_total_in_orders(self):
        return Payment.objects.filter(customer=self).aggregate(models.Sum('amount')).get('amount__sum') or 0

    def get_paid_order_count(self):
        return Payment.objects.filter(status='paid', customer=self).count()

    def get_total_orders_count(self):
        return Payment.objects.filter(customer=self).count()

    def get_transactions(self, limit=10):
        return Payment.objects.filter(customer=self).order_by('-createdAt')[:limit]

class Card(models.Model):
    customer = models.ForeignKey('customer.Customer', on_delete=models.CASCADE)
    token = models.UUIDField(default=uuid.uuid4, editable=False)
    externalToken = models.CharField(max_length=255, null=True, blank=True)
    reusable = models.BooleanField(default=False)
    holderName = models.CharField(max_length=255, null=True, blank=True)
    encryptedNumber = EncryptedField(hide_algorithm=True, null=True, blank=True)
    number = models.CharField(max_length=255, null=True, blank=True)
    cvv = models.CharField(max_length=255, null=True, blank=True)
    lastDigits = models.CharField(max_length=255, null=True, blank=True)
    expMonth = models.CharField(max_length=255, null=True, blank=True)
    expYear = models.CharField(max_length=255, null=True, blank=True)
    expirationDate = models.CharField(max_length=255, null=True, blank=True)
    brand = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.holderName or 'Sem nome'

    def get_brand_method(self):
        if self.brand == 'Visa':
            return 'visa'
        elif self.brand == 'Mastercard':
            return 'master'
        elif self.brand == 'Elo':
            return 'elo'
        elif self.brand == 'Hipercard':
            return 'hipercard'
        elif self.brand == 'American Express':
            return 'amex'
        elif self.brand == 'Diners':
            return 'diners'
        elif self.brand == 'Discover':
            return 'discover'
        elif self.brand == 'JCB':
            return 'jcb'
        else:
            return None

    def get_last_digits(self):
        return self.lastDigits

class Address(models.Model):
    customer = models.ForeignKey('customer.Customer', on_delete=models.CASCADE)
    street = models.CharField(max_length=255)
    number = models.CharField(max_length=255, null=True, blank=True)
    complement = models.CharField(max_length=255, null=True, blank=True)
    neighborhood = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=255)
    state = models.CharField(max_length=255)
    zipcode = models.CharField(max_length=255)

    def __str__(self):
        return self.street

class Fingerprint(models.Model):
    customer = models.ForeignKey('customer.Customer', on_delete=models.CASCADE)
    fingerprint = models.CharField(max_length=255)

    def __str__(self):
        return self.fingerprint
