from django.urls import path
from . import views

# add endpoint for list all the customers of the current user id /seller/customers/
urlpatterns = [
    path('sellers/customers/', views.SellerCustomersListView.as_view({'get': 'list'}), name='seller-customers-list'),
    path('sellers/customer/<int:pk>/', views.SellerCustomersListView.as_view({'get': 'retrieve'}), name='seller-customers-detail'),
    path('customers/', views.AdminCustomersViewset.as_view({'get': 'list'}), name='seller-customers-list'),

    # Reports
    path('customers/report/csv', views.CustomerReportCSV.as_view(), name='customer-report-csv'),
    path('customers/report/xlsx', views.CustomerReportXLSX.as_view(), name='customer-report-xlsx'),
]
