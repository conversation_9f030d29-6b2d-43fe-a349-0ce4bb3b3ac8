import pytest

from customer.models import Card, Customer
from customer.serializers import CardSerializer
from user.models import User


@pytest.mark.django_db
def test_card_serializer_contains_expected_fields(credit_card: Card):
    serializer = CardSerializer(instance=credit_card)
    expected_fields = {
        'brand',
        'customer',
        'expirationDate',
        'externalToken',
        'holderName',
        'id',
        'lastDigits',
        'reusable',
        'token'
    }
    assert set(serializer.data.keys()) == set(expected_fields)


@pytest.mark.django_db
def test_card_serializer_not_contains_sensitive_fields(credit_card: Card):
    serializer = CardSerializer(instance=credit_card)
    unsafe_fields = {
        'number',
        'cvv',
        'expMonth',
        'expYear'
    }
    for field in unsafe_fields:
        assert field not in set(serializer.data)


@pytest.mark.django_db
def test_create_credit_card_with_sensitive_data(
    regular_user: User,
    regular_customer: Customer,
    faker,
):
    data = {
        "customer": regular_customer.id,
        "token": faker.uuid4(),
        "externalToken": faker.uuid4(),
        "holderName": faker.name(),
        "number": faker.credit_card_number(),
        "cvv": faker.credit_card_security_code(),
    }
    serializer = CardSerializer(data=data)
    assert serializer.is_valid() is True

    serializer.save()

    assert not "number" in serializer.data
    assert not "cvv" in serializer.data
    assert not "expMonth" in serializer.data
    assert not "expYear" in serializer.data
