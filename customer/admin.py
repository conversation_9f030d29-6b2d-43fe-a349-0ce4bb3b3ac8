from django.contrib import admin
from import_export.admin import ExportActionMixin, ImportExportModelAdmin

from payment.models import Payment

from .models import Address, Card, Customer


@admin.register(Customer)
class CustomerAdmin(ImportExportModelAdmin, ExportActionMixin):
    list_display = ('id', 'name', 'email', 'createdAt',)
    list_display_links = ('id', 'name')
    list_filter = ('createdAt',)
    search_fields = ('name', 'email')
    list_per_page = 25
    raw_id_fields = ('user',)

    def get_export_queryset(self, request):
        # get only customers instances
        payments = Payment.objects.filter(createdAt__gte='2024-05-10', status='paid')
        return Customer.objects.filter(payment__in=payments)

@admin.register(Address)
class AddressAdmin(admin.ModelAdmin):
    list_display = ('id', 'street', 'city', 'state', 'zipcode',)
    list_display_links = ('id', 'street')