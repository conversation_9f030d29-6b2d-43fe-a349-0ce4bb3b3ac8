#!/bin/bash

# Check if <PERSON><PERSON><PERSON><PERSON>_SETTINGS_MODULE is set
if [ -z "${DJANGO_SETTINGS_MODULE}" ]; then
    echo "DJANGO_SETTINGS_MODULE environment variable is not set"
    exit 1
fi

echo "DEBUG: DJANGO_SETTINGS_MODULE = ${DJANGO_SETTINGS_MODULE}"

echo "Installing awscli"
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install

echo "Installing awscli-local"
uv pip install awscli-local --system

echo "Creating bucket documents"
awslocal s3api create-bucket --bucket documents

# final test with all the cores we can use
pytest -vvv -x --reuse-db