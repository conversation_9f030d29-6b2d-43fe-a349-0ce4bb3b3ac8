#!/bin/bash


# Check if <PERSON><PERSON><PERSON><PERSON>_SETTINGS_MODULE is set
if [ -z "${DJANGO_SETTINGS_MODULE}" ]; then
    echo "DJANGO_SETTINGS_MODULE environment variable is not set"
    exit 1
fi

echo "!!! WARNING !!! Development script detected. This should not be used in production."
echo "DJANGO_SETTINGS_MODULE = ${DJANGO_SETTINGS_MODULE}"

# exec migration
python manage.py migrate --no-input

# create bucket for local tests
awslocal s3api create-bucket --bucket documents

# start app
if [ ${START_DEV_SERVER} ]; then
    python manage.py runserver 0.0.0.0:8000
fi



