#!/bin/bash

# Check if <PERSON><PERSON><PERSON><PERSON>_SETTINGS_MODULE is set
if [ -z "${DJANGO_SETTINGS_MODULE}" ]; then
    echo "DJANGO_SETTINGS_MODULE environment variable is not set. Stopping application..."
    exit 1
fi

echo "DEBUG: DJANGO_SETTINGS_MODULE = ${DJANGO_SETTINGS_MODULE}"

if [ -z "${SPLIT_APP}" ]; then
    SPLIT_APP=api
fi

case "${SPLIT_APP}" in
    api)
        if [ -z "${GUNICORN_CMD_ARGS}" ]; then
            echo "GUNICORN_CMD_ARGS environment variable is not set. Stopping application..."
            exit 1
        fi
        echo "DEBUG: GUNICORN_CMD_ARGS = ${GUNICORN_CMD_ARGS}"
        cd /app

        echo "Running migrations..."
        python manage.py migrate --no-input

        echo "Collecting static files..."
        python manage.py collectstatic --noinput

        echo "Starting Split API"
        exec gunicorn ${GUNICORN_CMD_ARGS}
        ;;
    rqscheduler)
        echo "Starting rqscheduler"
        python manage.py rqscheduler
        ;;
    runapscheduler)
        echo "Starting runapscheduler"
        python manage.py runapscheduler
        ;;
    pendingReleases)
        echo "Starting pendingReleases worker"
        python manage.py rqworker pendingReleases
        ;;
    rqworker)
        echo "Starting rqworker worker"
        python manage.py rqworker
        ;;
    run_worker)
        echo "Starting run_worker worker"
        python manage.py runscript run_worker
        ;;
    webhooks)
        echo "Starting webhooks worker"
        python manage.py rqworker webhooks
        ;;
    *)
        echo "Invalid option for SPLIT_APP env variable. Please use one of: api, rqscheduler, runapscheduler, pendingReleases, rqworker, run_worker, webhooks"
        exit 1
        ;;
esac