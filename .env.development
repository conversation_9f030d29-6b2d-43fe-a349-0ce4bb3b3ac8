# Container variables
DJANGO_SETTINGS_MODULE='splitpay.settings_docker_local'
GUNICORN_CMD_ARGS='--workers=3 --bind=0.0.0.0:8000 --access-logfile - splitpay.wsgi:application'

# APP Variables
SECRET_KEY='django-insecure-192@@_17b*_-1h!1h*xip8st7v7ljpvd%g%nmvnbis*6^%#^p5'
ALLOWED_HOSTS='*'
CSRF_TRUSTED_ORIGINS=''
FRONTEND_URL='http://localhost:3000'
BACKEND_URL='http://localhost:8000'
OWEMPAY_ORIGIN='MORGAN'
GATEWAY_FEE_PERCENTAGE=0
DATABASE_ENGINE='postgresql'
DB_NAME='caktodb'
DB_USER='caktouser'
DB_PASSWORD='caktopassword'
DB_HOST='db'
DB_PORT='5432'
DB_SSL_MODE='disable'
AWS_S3_ACCESS_KEY_ID='aws_s3_access_key_id'
AWS_SECRET_ACCESS_KEY='aws_secret_access_key'
AWS_STORAGE_BUCKET_NAME='documents'
AWS_S3_REGION_NAME='eu-west-1'
AWS_S3_ENDPOINT_URL='http://localstack:4566'
AWS_S3_CUSTOM_DOMAIN=''
REDIS_URL_QUEUE='redis://redis:6379'
EMAIL_HOST='host-do-email'
EMAIL_PORT=587
EMAIL_USE_TLS=False
EMAIL_HOST_USER=''
EMAIL_HOST_PASSWORD=''
DEFAULT_FROM_MAIL='<EMAIL>'
GOOGLE_APPLICATION_CREDENTIALS=''
GOOGLE_CLOUD_PROJECT_ID=''
GOOGLE_APPLICATION_CREDENTIALS='boxwood-scope-433722-i3-2d338fa6bc38.json'
GOOGLE_CLOUD_PROJECT_ID='boxwood-scope-433722-i3'
GOOGLE_CLOUD_SECRET_NAME='projects/333813351917/secrets/first-scret'
ENCRYPTED_KEY='VjGvtWEhuQkd3s3avh4HFWCToBvo7RF6'
EXTERNAL_LOGGING=False
SPLITPAY_LOGGER_LEVEL=INFO
