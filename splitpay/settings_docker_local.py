"""
Django settings for splitpay project.

Generated by 'django-admin startproject' using Django 4.1.4.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

from datetime import timed<PERSON><PERSON>
from pathlib import Path
import os

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

STAGING = True

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS').split(',')

CORS_ALLOW_ALL_ORIGINS = True

csrf = os.getenv('CSRF_TRUSTED_ORIGINS')
if csrf:
    CSRF_TRUSTED_ORIGINS = csrf.split(',')

FRONTEND_URL = os.getenv('FRONTEND_URL')
BACKEND_URL = os.getenv('BACKEND_URL')
OWEMPAY_ORIGIN = os.getenv('OWEMPAY_ORIGIN')

OWEMPAY_ORIGIN = 'MORGAN'

STANDALONE_GATEWAY = os.getenv('STANDALONE_GATEWAY', 'true') == 'true'

# Fee
GATEWAY_FEE_PERCENTAGE = os.getenv('GATEWAY_FEE_PERCENTAGE', 0) or 0

# Encryption
DJANGO_ENCRYPTED_FIELD_KEY = bytes(os.getenv('ENCRYPTED_KEY').encode('utf-8'))
DJANGO_ENCRYPTED_FIELD_ALGORITHM = 'CC20P'

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework_simplejwt',
    'rest_framework.authtoken',
    'rest_framework',
    'django_filters',
    'corsheaders',
    'django_rq',
    'compliance',
    'customer',
    'gateway',
    'payment',
    'nfse',
    'user',
    'import_export',
    'reports',
    'django_apscheduler',
    'django_otp',
    'django_otp.plugins.otp_static',
    'django_otp.plugins.otp_totp',
    'django_otp.plugins.otp_email',
    'two_factor',
    'two_factor.plugins.email',
    'django_extensions',
    'django_lifecycle_checks',
    'waffle',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'waffle.middleware.WaffleMiddleware',
    'django_otp.middleware.OTPMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'splitpay.health.HealthCheckMiddleware'
]

ROOT_URLCONF = 'splitpay.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'splitpay.wsgi.application'
ASGI_APPLICATION = 'splitpay.asgi.application'

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

if os.getenv('DATABASE_ENGINE') == 'postgresql':
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.getenv('DB_NAME'),
            'USER': os.getenv('DB_USER'),
            'PASSWORD': os.getenv('DB_PASSWORD'),
            'HOST': os.getenv('DB_HOST'),
            'PORT': os.getenv('DB_PORT'),
            'CONN_MAX_AGE': 60,
            'DISABLE_SERVER_SIDE_CURSORS': True,
            'OPTIONS': {
                'sslmode': os.getenv('DB_SSL_MODE'),
            }
        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': os.getenv('REDIS_URL_QUEUE'),
        'KEY_PREFIX': 'split_cache_',
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = 'pt-br'

TIME_ZONE = 'America/Sao_Paulo'

USE_I18N = True

USE_TZ = True

# Auth
AUTH_USER_MODEL = 'user.User'

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/
STATIC_URL = 'assets/'
STATIC_ROOT = BASE_DIR / 'assets'

MEDIA_ROOT = BASE_DIR / 'images/'
MEDIA_URL = '/images/'

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# S3
AWS_ACCESS_KEY_ID = os.getenv('AWS_S3_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME')
AWS_S3_SIGNATURE_VERSION = 's3v4'
AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME')
AWS_S3_VERIFY = True
AWS_DEFAULT_ACL = 'public-read'
AWS_S3_FILE_OVERWRITE = False
AWS_S3_ENDPOINT_URL = os.getenv('AWS_S3_ENDPOINT_URL')
AWS_S3_CUSTOM_DOMAIN = os.getenv('AWS_S3_CUSTOM_DOMAIN')

STORAGES = {
    "default": {
        "BACKEND": "storages.backends.s3.S3Storage",
        "OPTIONS": {
            # "AWS_ACCESS_KEY_ID": AWS_ACCESS_KEY_ID,
            # "AWS_SECRET_ACCESS_KEY": AWS_SECRET_ACCESS_KEY,
            # "AWS_STORAGE_BUCKET_NAME": AWS_STORAGE_BUCKET_NAME,
        },
    },
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
    },
}

# Simple JWT
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=1),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
}

# DJANGO REST FRAMEWORK
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'user.permissions.DefaultUserPermission',
    ),
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.OrderingFilter',
        'rest_framework.filters.SearchFilter',
    ],
    'COERCE_DECIMAL_TO_STRING': False,
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'PAGE_SIZE_QUERY_PARAM': 'page_size',
    'PAGE_QUERY_PARAM': 'page',
}

RQ_QUEUES = {
    'default': {
        'URL': os.getenv('REDIS_URL_QUEUE', ''),
        'DEFAULT_TIMEOUT': 50,
        # keep results for 24h
        'RESULT_TTL': 86400,
    },
    'webhooks': {
        'URL': os.getenv('REDIS_URL_QUEUE', ''),
        'DEFAULT_TIMEOUT': 50,
    },
    'pendingReleases': {
        'URL': os.getenv('REDIS_URL_QUEUE', ''),
        'DEFAULT_TIMEOUT': 50,
    },
    'onboard': {
        'URL': os.getenv('REDIS_URL_QUEUE', ''),
        'DEFAULT_TIMEOUT': 50,
        'PREFIX': 'onboard_',
    },
    'high': {
        'URL': os.getenv('REDIS_URL_QUEUE', ''),
        'DEFAULT_TIMEOUT': 50,
    }
}

#External logging
EXTERNAL_LOGGING = bool(os.getenv('EXTERNAL_LOGGING', 'false').lower() == 'true')
SPLITPAY_LOGGER_LEVEL = os.getenv('SPLITPAY_LOGGER_LEVEL', 'INFO')

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': (
                '{asctime} - [{levelname}] - {name} - {pathname}:{lineno} - {message}'),
            'style': '{',
        },
        'verbose_external': {
            'format': (
                '[{name}] - {pathname}:{lineno} - {message}'),
            'style': '{',
        },
     },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler'
        },
        'console_verbose': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'console_verbose_external': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose_external',
        },

    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'ERROR',
            'propagate': False,
        },
        'splitpay': {
            'handlers': ['console_verbose_external' if EXTERNAL_LOGGING else 'console_verbose'],
            'level': SPLITPAY_LOGGER_LEVEL,
            'propagate': False,
        }
    },
}


# Password reset
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST')
EMAIL_PORT = int(os.getenv('EMAIL_PORT'))
EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS')
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_MAIL = os.getenv('DEFAULT_FROM_MAIL')

# Two Factor
LOGIN_URL = 'two_factor:login'
TWO_FACTOR_REMEMBER_COOKIE_AGE = 120
TWO_FACTOR_LOGIN_TIMEOUT = 0
OTP_EMAIL_TOKEN_VALIDITY = 900
OTP_EMAIL_SENDER = os.getenv('DEFAULT_FROM_MAIL')
OTP_EMAIL_SUBJECT = 'Código de verificação'
OTP_EMAIL_BODY_HTML_TEMPLATE_PATH = 'otp_email_body.html'
MAX_TOTP_DEVICES = 5

# TemporalIO
TEMPORAL_SERVER_URL = os.getenv('TEMPORAL_SERVER_URL')
TEMPORAL_TASK_QUEUE = os.getenv('TEMPORAL_TASK_QUEUE')

# BigQuery
GOOGLE_CLOUD_PROJECT_ID = os.getenv('GOOGLE_CLOUD_PROJECT_ID', '')
GOOGLE_CLOUD_SECRET_NAME = os.getenv('GOOGLE_CLOUD_SECRET_NAME', '')
GOOGLE_APPLICATION_CREDENTIALS =os.getenv('GOOGLE_APPLICATION_CREDENTIALS', '')

# Sandbox
SANDBOX_ENABLED = os.getenv('SANDBOX_ENABLED', False)

# Silky
SILKY_PYTHON_PROFILER = os.getenv('SILKY_PYTHON_PROFILER', False)
SILKY_PYTHON_PROFILER_BINARY = os.getenv('SILKY_PYTHON_PROFILER_BINARY', False)
SILKY_PYTHON_PROFILER_RESULT_PATH = os.getenv('SILKY_PYTHON_PROFILER_RESULT_PATH', False)
SILKY_PYTHON_PROFILER_EXTENDED_FILE_NAME = os.getenv('SILKY_PYTHON_PROFILER_EXTENDED_FILE_NAME', False)

if SILKY_PYTHON_PROFILER:
    INSTALLED_APPS.append('silk')
    MIDDLEWARE.append('silk.middleware.SilkyMiddleware')


# TELEGRAM
TELEGRAM = {
    'bot_token': os.getenv('TELEGRAM_BOT_TOKEN', ''),
    'chat_id': os.getenv('TELEGRAM_CHAT_ID', ''),
}
