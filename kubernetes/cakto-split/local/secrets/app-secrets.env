ALLOWED_HOSTS=pod.localhost,*
AWS_S3_ACCESS_KEY_ID=aws_s3_access_key_id
AWS_S3_CUSTOM_DOMAIN=
AWS_S3_ENDPOINT_URL=http://cakto-split-localstack-svc:4566
AWS_S3_REGION_NAME=eu-west-1
AWS_SECRET_ACCESS_KEY=aws_secret_access_key
AWS_STORAGE_BUCKET_NAME=documents
BACKEND_URL=http://localhost:8000
COMPANY_DESCRIPTOR=CAKTO*
CSRF_TRUSTED_ORIGINS=
DATABASE_ENGINE=postgresql
DB_HOST=cakto-split-postgres-svc
DB_NAME=caktodb
DB_PASSWORD=caktopassword
DB_PORT=5432
DB_SSL_MODE=disable
DB_USER=caktouser
DEFAULT_FROM_MAIL=<EMAIL>
DJANGO_SETTINGS_MODULE=splitpay.settings_docker_local
EMAIL_HOST_PASSWORD=email-password
EMAIL_HOST_USER=
EMAIL_HOST=host-do-email
EMAIL_PORT=587
EMAIL_USE_TLS=False
EXTERNAL_LOGGING=False
FOCUS_MAIN_PROD_TOKEN=prod-token
FRONTEND_URL=http://localhost:3000
GATEWAY_FEE_PERCENTAGE=0
GOOGLE_APPLICATION_CREDENTIALS=boxwood-scope-433722-i3-2d338fa6bc38.json
GOOGLE_CLOUD_PROJECT_ID=boxwood-scope-433722-i3
GOOGLE_CLOUD_SECRET_NAME=projects/333813351917/secrets/first-scret
GUNICORN_CMD_ARGS=--workers=3 --bind=0.0.0.0:8000 --access-logfile - splitpay.wsgi:application
IDEZ_TOKEN=idez-token
NOXPAY_SECRET=nox-pay-secret
OWEMPAY_ORIGIN=MORGAN
REDIS_URL_QUEUE=redis://cakto-split-redis-svc:6379
SECRET_KEY=django-insecure-192@@_17b*_-1h!1h*xip8st7v7ljpvd%g%nmvnbis*6^%#^p5
SPLITPAY_LOGGER_LEVEL=INFO
TEMPORAL_SERVER_URL=temporal-io-svc:7233
TEMPORAL_TASK_QUEUE=payment-task-queue
VITE_HOTJAR_SITE_ID=1813181
VITE_HOTJAR_VERSION=1