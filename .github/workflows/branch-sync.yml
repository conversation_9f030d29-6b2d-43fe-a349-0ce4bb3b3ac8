name: Sync main to dev

on:
  push:
    branches:
      - main

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"

      - name: Sync main to dev
        if: github.ref == 'refs/heads/main'
        run: |
          git checkout dev
          git pull origin dev
          git merge main -Xtheirs
          git push origin dev
