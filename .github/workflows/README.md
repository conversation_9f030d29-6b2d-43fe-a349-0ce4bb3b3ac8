# Pipelines

## CI
Este pipeline é disparado, automaticamente, em Pull Requests ou commits em algumas branches.
Abaixo a relação das atividades realizadas por esse pipeline em determinados eventos do github:

|Evento| Branch de origem | Branch de destino | Atividades realizadas|
| --- | --- | --- | --- |
| Pull Request | Qualquer uma | `main` ou `staging` | Execução de testes de unidade |
| Commit | N/A | `staging` | Execução de testes de unidade; Criação da imagem docker; Implantação automática no ambiente de **staging** |
| Commit | N/A | `main` | Execução de testes de unidade; Criação da imagem docker; Implantação automática no ambiente de **PRD** |



### Github secrets para o ambiente de STAGING

| Nome | Descrição | Exemplo de conteúdo|
| --- | --- | --- |
| `KUBE_CONFIG_STAGING` | [Kubeconfig](https://kubernetes.io/docs/concepts/configuration/organize-cluster-access-kubeconfig/) para acessar o ambiente de **staging**| apiVersion: v1<br/>clusters:<br/>- cluster:<br/>&nbsp;&nbsp;&nbsp;&nbsp;    certificate-authority-data: ...|
| `STAGING_CAKTO_SPLIT_API_URL` | Url para acesso à API da cakto split no ambiente de **staging**| `api.scpay.com.br`|
| `STAGING_TEMPORAL_IO_URL` | Url para acesso à API do `temporal.io` no ambiente de **staging**| `staging.temporal.cakto.com.br`|
| `STAGING_ES_USER_NAME` | Nome de usuário do cluster `elastic search` a ser criado no ambiente de **staging** (utilizado pelo temporal.io)| `elasticuser`|
| `STAGING_ES_USER_PASSWORD` | Senha para o usuário do cluster `elastic search` a ser criado no ambiente de **staging** (utilizado pelo temporal.io).<br/> **ATENÇÃO: A senha precisa ter 6 ou mais caracteres**| `senhacommaisdeseiscaracteres`|
| `STAGING_APP_SECRETS`| Arquivo contendo todas as secrets utilizadas pelas aplicações `cakto-split` no ambiente de **staging**| Ver exemplo de conteúdo do arquivo [/kubernetes/cakto-split/local/secrets/app-secrets.env](/kubernetes/cakto-split/local/secrets/app-secrets.env)|
| `STAGING_TEMPORAL_IO_SECRETS`| Arquivo contendo todas as secrets utilizadas pela aplicação `temporal.io` no ambiente de **staging**| Ver exemplo de conteúdo do arquivo [/kubernetes/cakto-split/local/secrets/temporal-io-secrets.env](/kubernetes/cakto-split/local/secrets/temporal-io-secrets.env)|
| `STAGING_TEMPORAL_IO_WEB_SECRETS`| Arquivo contendo todas as secrets utilizadas pelo **container WEB** da aplicação `temporal.io` no ambiente de **staging**| Ver exemplo de conteúdo do arquivo [/kubernetes/cakto-split/local/secrets/temporal-io-web-secrets.env](/kubernetes/cakto-split/local/secrets/temporal-io-web-secrets.env)|




### Github secrets para o ambiente de PRD
| Nome | Descrição | Exemplo de conteúdo|
| --- | --- | --- |
| `KUBE_CONFIG_PRD` | [Kubeconfig](https://kubernetes.io/docs/concepts/configuration/organize-cluster-access-kubeconfig/) para acessar o ambiente de **PRD**| apiVersion: v1<br/>clusters:<br/>- cluster:<br/>&nbsp;&nbsp;&nbsp;&nbsp;    certificate-authority-data: ...|
| `PRD_CAKTO_SPLIT_API_URL` | Url para acesso à API da cakto split no ambiente de **PRD**| `api.cakto.com.br`|
| `PRD_TEMPORAL_IO_URL` | Url para acesso à API do `temporal.io` no ambiente de **PRD**| `temporal.cakto.com.br`|
| `PRD_ES_USER_NAME` | Nome de usuário do cluster `elastic search` a ser criado no ambiente de **PRD** (utilizado pelo temporal.io)| `elasticuser`|
| `PRD_ES_USER_PASSWORD` | Senha para o usuário do cluster `elastic search` a ser criado no ambiente de **PRD** (utilizado pelo temporal.io).<br/> **ATENÇÃO: A senha precisa ter 6 ou mais caracteres**| `senhacommaisdeseiscaracteres`|

> Observação: No ambiente de produção, as variáveis `PRD_APP_SECRETS`, `PRD_TEMPORAL_IO_SECRETS` e `PRD_TEMPORAL_IO_WEB_SECRETS` não são mais utilizadas, pois seu conteúdo é gerenciado através do [External Secrets Operator](https://external-secrets.io/) conforme documentado na seção **Instalação do ESO - External Secrets Operator  (apenas em produção)** do documento [/kubernetes/README.md](/kubernetes/README.md)
