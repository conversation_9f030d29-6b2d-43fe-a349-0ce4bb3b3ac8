name: CI Pipeline

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - staging

  pull_request:
    branches:
      - main
      - staging

env:
  SPLIT_IMAGE_NAME: cakto-split
  REGISTRY_NAME: cakto
  REGISTRY_URL: registry.digitalocean.com

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Run tests
        run: docker compose -f docker-compose-tests.yaml up --build --exit-code-from cakto-split-tests

  build_and_push:
    if: github.event_name != 'pull_request'
    needs: test
    runs-on: ubuntu-latest
    outputs:
      tag_name: ${{ steps.tag.outputs.tag_name }}
      complete_split_image_name: ${{ steps.split_image_name.outputs.complete_split_image_name }}

    steps:
      - name: Set tag name
        id: tag
        run: echo "tag_name=$(date +'%Y-%m-%d').${{ github.run_number }}" >> $GITHUB_OUTPUT

      - name: Set complete split image name
        id: split_image_name
        run: echo "complete_split_image_name=${{ env.REGISTRY_URL }}/${{ env.REGISTRY_NAME }}/${{ env.SPLIT_IMAGE_NAME }}" >> $GITHUB_OUTPUT

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Registry login
        run: doctl registry login

      - name: Build and publish docker image ${{ env.SPLIT_IMAGE_NAME }}:${{ steps.tag.outputs.tag_name }}
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.split_image_name.outputs.complete_split_image_name }}:${{ steps.tag.outputs.tag_name }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy_to_staging:
    if: github.ref == 'refs/heads/staging'
    needs: build_and_push
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install kubectl
        uses: azure/setup-kubectl@v4

      - name: Set kubeconfig
        uses: azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

      - name: Transform ${{ env.SPLIT_IMAGE_NAME }} image name and image tag
        run: |
          sed -i "s|##SPLIT_IMAGE_NAME##|${{ needs.build_and_push.outputs.complete_split_image_name }}|g" kubernetes/cakto-split/staging/api/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_TAG##|${{ needs.build_and_push.outputs.tag_name }}|g" kubernetes/cakto-split/staging/api/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_NAME##|${{ needs.build_and_push.outputs.complete_split_image_name }}|g" kubernetes/cakto-split/staging/schedulers/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_TAG##|${{ needs.build_and_push.outputs.tag_name }}|g" kubernetes/cakto-split/staging/schedulers/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_NAME##|${{ needs.build_and_push.outputs.complete_split_image_name }}|g" kubernetes/cakto-split/staging/workers/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_TAG##|${{ needs.build_and_push.outputs.tag_name }}|g" kubernetes/cakto-split/staging/workers/kustomization.yaml

      - name: Transform api endpoint
        run: |
          sed -i "s|##API_ENDPOINT_URL##|${{ secrets.STAGING_CAKTO_SPLIT_API_URL }}|g" kubernetes/cakto-split/staging/api/certificate.yaml
          sed -i "s|##API_ENDPOINT_URL##|${{ secrets.STAGING_CAKTO_SPLIT_API_URL }}|g" kubernetes/cakto-split/staging/api/ingress.yaml

      - name: Transform temporal.io endpoint and es user
        run: |
          sed -i "s|##TEMPORAL_IO_URL##|${{ secrets.STAGING_TEMPORAL_IO_URL }}|g" kubernetes/cakto-split/staging/temporal-io/certificate.yaml
          sed -i "s|##TEMPORAL_IO_URL##|${{ secrets.STAGING_TEMPORAL_IO_URL }}|g" kubernetes/cakto-split/staging/temporal-io/ingress.yaml
          sed -i "s|##ES_USER_NAME##|${{ secrets.STAGING_ES_USER_NAME }}|g" kubernetes/cakto-split/staging/temporal-io-components/elastic-search-basic-auth-user.yaml
          sed -i "s|##ES_USER_PASSWORD##|${{ secrets.STAGING_ES_USER_PASSWORD }}|g" kubernetes/cakto-split/staging/temporal-io-components/elastic-search-basic-auth-user.yaml

      - name: Create secrets files
        run: |
          echo "${{ secrets.STAGING_APP_SECRETS }}" > kubernetes/cakto-split/staging/secrets/app-secrets.env
          echo "${{ secrets.STAGING_TEMPORAL_IO_SECRETS }}" > kubernetes/cakto-split/staging/secrets/temporal-io-secrets.env
          echo "${{ secrets.STAGING_TEMPORAL_IO_WEB_SECRETS }}" > kubernetes/cakto-split/staging/secrets/temporal-io-web-secrets.env

      - name: Deploy api
        run: |
          kubectl apply -k kubernetes/cakto-split/staging/namespace
          kubectl apply -k kubernetes/cakto-split/staging/api
          kubectl apply -k kubernetes/cakto-split/staging/api-components

      - name: Deploy schedulers and workers
        run: |
          kubectl apply -k kubernetes/cakto-split/staging/schedulers
          kubectl apply -k kubernetes/cakto-split/staging/workers

      - name: Deploy temporal-io
        run: |
          kubectl apply -k kubernetes/cakto-split/staging/temporal-io-components
          kubectl apply -k kubernetes/cakto-split/staging/temporal-io

  deploy_to_prd:
    if: github.ref == 'refs/heads/main'
    needs: build_and_push
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install kubectl
        uses: azure/setup-kubectl@v4

      - name: Set kubeconfig
        uses: azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_PRD }}

      - name: Transform ${{ env.SPLIT_IMAGE_NAME }} image name and image tag
        run: |
          sed -i "s|##SPLIT_IMAGE_NAME##|${{ needs.build_and_push.outputs.complete_split_image_name }}|g" kubernetes/cakto-split/prd/api/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_TAG##|${{ needs.build_and_push.outputs.tag_name }}|g" kubernetes/cakto-split/prd/api/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_NAME##|${{ needs.build_and_push.outputs.complete_split_image_name }}|g" kubernetes/cakto-split/prd/schedulers/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_TAG##|${{ needs.build_and_push.outputs.tag_name }}|g" kubernetes/cakto-split/prd/schedulers/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_NAME##|${{ needs.build_and_push.outputs.complete_split_image_name }}|g" kubernetes/cakto-split/prd/workers/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_TAG##|${{ needs.build_and_push.outputs.tag_name }}|g" kubernetes/cakto-split/prd/workers/kustomization.yaml

      - name: Transform api endpoint
        run: |
          sed -i "s|##API_ENDPOINT_URL##|${{ secrets.PRD_CAKTO_SPLIT_API_URL }}|g" kubernetes/cakto-split/prd/api/certificate.yaml
          sed -i "s|##API_ENDPOINT_URL##|${{ secrets.PRD_CAKTO_SPLIT_API_URL }}|g" kubernetes/cakto-split/prd/api/ingress.yaml

      - name: Transform temporal.io endpoint and es user
        run: |
          sed -i "s|##TEMPORAL_IO_URL##|${{ secrets.PRD_TEMPORAL_IO_URL }}|g" kubernetes/cakto-split/prd/temporal-io/certificate.yaml
          sed -i "s|##ES_USER_NAME##|${{ secrets.PRD_ES_USER_NAME }}|g" kubernetes/cakto-split/prd/temporal-io-components/elastic-search-basic-auth-user.yaml
          sed -i "s|##ES_USER_PASSWORD##|${{ secrets.PRD_ES_USER_PASSWORD }}|g" kubernetes/cakto-split/prd/temporal-io-components/elastic-search-basic-auth-user.yaml

      - name: Deploy api
        run: |
          kubectl apply -k kubernetes/cakto-split/prd/namespace
          kubectl apply -k kubernetes/cakto-split/prd/api
          kubectl apply -k kubernetes/cakto-split/prd/api-components

      - name: Deploy schedulers and workers
        run: |
          kubectl apply -k kubernetes/cakto-split/prd/schedulers
          kubectl apply -k kubernetes/cakto-split/prd/workers

      - name: Deploy temporal-io
        run: |
          kubectl apply -k kubernetes/cakto-split/prd/temporal-io-components
          kubectl apply -k kubernetes/cakto-split/prd/temporal-io
  deploy_to_nommi:
    if: github.ref == 'refs/heads/main'
    needs: build_and_push
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install kubectl
        uses: azure/setup-kubectl@v4

      - name: Set kubeconfig
        uses: azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_NOMMI_PRD }}

      - name: Transform ${{ env.SPLIT_IMAGE_NAME }} image name and image tag
        run: |
          sed -i "s|##SPLIT_IMAGE_NAME##|${{ needs.build_and_push.outputs.complete_split_image_name }}|g" kubernetes/cakto-split/nommi/api/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_TAG##|${{ needs.build_and_push.outputs.tag_name }}|g" kubernetes/cakto-split/nommi/api/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_NAME##|${{ needs.build_and_push.outputs.complete_split_image_name }}|g" kubernetes/cakto-split/nommi/schedulers/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_TAG##|${{ needs.build_and_push.outputs.tag_name }}|g" kubernetes/cakto-split/nommi/schedulers/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_NAME##|${{ needs.build_and_push.outputs.complete_split_image_name }}|g" kubernetes/cakto-split/nommi/workers/kustomization.yaml
          sed -i "s|##SPLIT_IMAGE_TAG##|${{ needs.build_and_push.outputs.tag_name }}|g" kubernetes/cakto-split/nommi/workers/kustomization.yaml

      - name: Transform api endpoint
        run: |
          sed -i "s|##API_ENDPOINT_URL##|${{ secrets.PRD_NOMMI_SPLIT_API_URL }}|g" kubernetes/cakto-split/nommi/api/certificate.yaml
          sed -i "s|##API_ENDPOINT_URL##|${{ secrets.PRD_NOMMI_SPLIT_API_URL }}|g" kubernetes/cakto-split/nommi/api/ingress.yaml

      - name: Transform temporal.io endpoint and es user
        run: |
          sed -i "s|##TEMPORAL_IO_URL##|${{ secrets.PRD_TEMPORAL_IO_URL }}|g" kubernetes/cakto-split/nommi/temporal-io/certificate.yaml
          sed -i "s|##ES_USER_NAME##|${{ secrets.PRD_ES_USER_NAME }}|g" kubernetes/cakto-split/nommi/temporal-io-components/elastic-search-basic-auth-user.yaml
          sed -i "s|##ES_USER_PASSWORD##|${{ secrets.PRD_NOMMI_ES_USER_PASSWORD }}|g" kubernetes/cakto-split/nommi/temporal-io-components/elastic-search-basic-auth-user.yaml

      - name: Deploy api
        run: |
          kubectl apply -k kubernetes/cakto-split/nommi/namespace
          kubectl apply -k kubernetes/cakto-split/nommi/api
          kubectl apply -k kubernetes/cakto-split/nommi/api-components

      - name: Deploy schedulers and workers
        run: |
          kubectl apply -k kubernetes/cakto-split/nommi/schedulers
          kubectl apply -k kubernetes/cakto-split/nommi/workers

      - name: Deploy temporal-io
        run: |
          kubectl apply -k kubernetes/cakto-split/nommi/temporal-io-components
          kubectl apply -k kubernetes/cakto-split/nommi/temporal-io
